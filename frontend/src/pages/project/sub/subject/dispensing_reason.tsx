import React, {useEffect} from "react";
import {Button, Col, Form, Input, message, Modal, notification, Row, Select, InputNumber, Divider} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {useAuth} from "../../../../context/auth";
import {useSafeState, useSize} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {deleteDispensing, replaceDrug} from "../../../../api/dispensing";
import {DispensingConfirm} from "./dispensing_confirm";
import {useSubject} from "./context";
import {useGlobal} from "../../../../context/global";
import {ModeLogistics} from "./mode_logistics";
import {subjectEdcVerification} from "../../../../api/subject";
import {getProject} from "../../../../api/projects";
import {CustomConfirmModal} from "../../../../components/modal";
import {
    InfoCircleFilled,
    CheckCircleOutlined,
} from "@ant-design/icons";
import {pushScenarioFilter} from "../../../../utils/irt_push_edc_util";
import {DTPOption, DTPOptionIntl} from "../../../../data/data";


export const DispensingReason = (props:any) => {
    const [visible, setVisible] = useSafeState<any>(false);

    const auth = useAuth();
    const intl = useTranslation();
    const ctx = useSubject();

    const {formatMessage} = intl;

    const [visitId, setVisitId] = useSafeState<any>([]);
    const [medicines, setMedicines] = useSafeState<any>([]);
    const [choiceMedicines, setChoiceMedicines] = useSafeState<any>([]);
    const [selectOption, setSelectOption] = useSafeState<any>([])
    const [type, setType] = useSafeState<any>(1);
    const [dtpType, setDtpType] = useSafeState<any>([]);

    const [subjectId, setSubjectId] = useSafeState<any>(null);
    const [form] = Form.useForm();
    const [record, setRecord] = useSafeState<any>({});
    const [dispensing, setDispensing] = useSafeState<any>({});
    const [selectListInfo, setSelectListInfo] = useSafeState<any>({});
    const dispensing_confirm_pt :any = React.useRef();

    const [sendType, setSendType] = useSafeState<number>(0);
    const [otherLogistics, setOtherLogistics] = useSafeState<boolean>(false)

    const {runAsync: deleteDispensingRun, loading: deleteDispensingLoading} = useFetch(deleteDispensing, {manual: true})
    const {runAsync: replaceDrugRun, loading: replaceDrugLoading} = useFetch(replaceDrug, {manual: true})

    const [dtp, setDtp] = useSafeState<boolean>(false);

    const projectId = auth.project ? auth.project.id : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const connectEdc = auth.project.info.connect_edc;
    const pushMode = auth.project.info.push_mode;
    const pushScenario = auth.project.info.push_scenario !== undefined? auth.project.info.push_scenario.dispensing_push: false;

    const {
        runAsync: edcSubjectVerificationRun,
        loading: edcSubjectVerificationRunLoading,
    } = useFetch(subjectEdcVerification, { manual: true });
    const [edcSupplier, setEdcSupplier] = useSafeState(0);
    const { runAsync: runGetProject, loading } = useFetch(() => getProject({ id: projectId }), {
        refreshDeps: [projectId],
        onSuccess: (result:any) => {
            setEdcSupplier(result.data.info.edcSupplier)
        }
    });

    const show = (type: any, subjectId: any, dispensingId: any, data: any, subjectInfo :any, visits:any) => {
        setWidth(g.lang === "en"? 190: 136)

        setType(type)
        setSubjectId(subjectId)
        setVisitId(dispensingId)
        form.resetFields()
        setVisible(true)
        if (type === 1){
            setRecord(subjectInfo);
            setDispensing(data)
            let Medicines :any[] = []
            if (data.dispensingMedicines){
                Medicines = [...data.dispensingMedicines]
            }
            if (data?.otherDispensingMedicines){
                Medicines = [...Medicines, ...data.otherDispensingMedicines]

            }
            Medicines = Medicines.filter((it:any)=>!(it.unReplace || it.unBlind === 2))
            setChoiceMedicines([...Medicines]);
            setMedicines([...Medicines]);
            setSelectOption([]);
        }
        setDtp(visits?.dtp)
        setDtpType(visits?.DTPType)
        runGetProject().then();
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
        setRecord({});
        setDispensing({});
        setMedicines([]);
        setChoiceMedicines([]);
        setSelectOption([]);
        setSendType(0);
        setOtherLogistics(false);
        props.refresh();
        setDtpType([]);
        setSelectListInfo({})
        setShowDTPend(-1)
    };

    // 替换研究产品编号
    const replace = () => {
        // console.log("kkkk====" + JSON.stringify(form.getFieldValue("medicineList")[0].medicine))
        // console.log("kkkk====" + JSON.stringify(medicines))
        // for (let i = 0; i < form.getFieldValue("medicineList").length; i++) {
        //     if (medicines.find((it:any) => it.id === form.getFieldValue("medicineList")[i].medicine)){
        //         if(!form.getFieldValue("medicineList")[i].count){
        //             form.getFieldValue("medicineList")[i].count = medicines.find((it:any) => it.id === form.getFieldValue("medicineList")[i].medicine).count
        //         }
        //     }
        // }
        form.validateFields().then(
            value => {
                let labelNames :any = []
                value.medicineList.forEach(
                    (item:any) => {
                        if (medicines.find((it:any) => it.medicineId === item.medicine)){
                            labelNames.push(medicines.filter((it:any) => it.medicineId === item.medicine)[0]?.number)
                        }else {
                            var me = medicines.filter((it:any) => it.id === item.medicine)[0]
                            if (me) {
                                labelNames.push(me.number?me.number:me.name+"["+me.batch+"]["+me.expireDate+"]" + " / " + item.count)
                            }
                        }
                    }
                )
                let labelName = labelNames.join(",")
                dispensing_confirm_pt.current.show(formatMessage({id: 'subject.dispensing.replace.confirm'}),record, dispensing.visitInfo?.name, labelName, 3, value)

            }
        )
    }

    const replaceValidator = {
        validator: (rule:any, value:any, callback:any) => {
            if (value && value.length !== 0) {
                return Promise.resolve();
            }
            return Promise.reject(formatMessage({id: 'validator.message.replace.medicine'}));

        }
    };

    //EDC对接项目需要校验中心
    const replace_confirm = () => {
        if(pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario)){
            let subjectData = {
                id: subjectId,
            };
            edcSubjectVerificationRun(subjectData).then((resp: any) => {
                if (!resp.data.linkStatus) {    // 请求接口响应异常
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                    fontSize: "14px",
                                }}
                            >
                                <CheckCircleOutlined
                                    style={{
                                        color: "#00BB00",
                                        paddingRight: "8px",
                                    }}
                                />
                                {formatMessage({ id: "common.success" })}
                            </div>
                        ),
                        description: (
                            <div style={{ paddingLeft: "20px", color: "#646566" }}>
                                {formatMessage({
                                    id: "subject.edc.interface.error",
                                })}
                            </div>
                        ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            background: "#F0FFF0",
                            borderRadius: "4px",
                        },
                    });
                    replace_confirm_method();
                }else{
                    // 1中心匹配 2中心不匹配 3 未获取到中心编号或者名称
                    if (resp.data.siteStatus == 1) {
                        replace_confirm_method();
                    }else if (resp.data.siteStatus == 2){
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.inconsistent" }),
                            okText: formatMessage({ id: "subject.edc.continue.dispense" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                                replace_confirm_method()
                        });
                    }else{
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.empty" }),
                            okText: formatMessage({ id: "subject.edc.continue.dispense" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                                replace_confirm_method()
                        });
                    }
                }
            });
        }else{
            replace_confirm_method();
        }
    };

    const replace_confirm_method = () => {
        form.validateFields().then(
            value => {
                // 区分编号药物，未编号药物
                const otherInfo = value.medicines
                const data = {
                    "id":visitId,
                    "customer_id":customerId,
                    "project_id":projectId,
                    "env_id":envId,
                    "cohort_id":record.cohortId,
                    "subject_id":subjectId,
                    "info_count":value.medicineList,
                    "reason":value.reason,
                    "role_id": auth.project.permissions.role_id,
                    "send_type":value.send_type,
                    "logistics":value.logistics,
                    "other":value.other,
                    "number":value.number,
                    "remark":value.remark,
                }
                replaceDrugRun(data).then(
                    (response:any) => {
                        props.refresh()
                        form.resetFields();
                        setVisible(false)
                        if (response.code === 0){
                            message.success(response.msg);
                        }else{
                            message.error(response.msg);
                        }
                        ctx.setDispensingConfirmVisible(false)
                        if (response.data?.length > 0 ){
                            ctx.setShowTitle(formatMessage({ id: "subject.dispensing.replace" }))
                            ctx.setShowData(response.data)
                            ctx.setVisible(true)
                        }
                    }
                )
            }
        )
    };

    //撤销
    const cancel = (id:any) => {
        form.validateFields().then(
            value => {
                deleteDispensingRun({id},{ "reason":value.reason}).then(
                    () => {
                        props.refresh()
                        setVisible(false)
                    }
                )
            }
        )

    }

    const confirmDispensing = () => {
        if (type ===1){
            replace()
        }
        if (type===2){
            cancel(visitId)
        }
    }

    const [selectOtherMax, setSelectOtherMax] = useSafeState<any>(-1);

    const ValidatorList = () => {
        const intl = useTranslation();
        const {formatMessage} = intl;
    
        return {
            validator: async (_: any, names: any[]) => {
                const idList: any[] = [];
                let hasDuplicate = false;
                for (let i = 0; i < form.getFieldsValue().medicineList.length; i++) {
                    if (form.getFieldsValue().medicineList[i] !== null && form.getFieldsValue().medicineList[i] !== undefined) {
                        if(form.getFieldsValue().medicineList[i].medicine !== null){
                            idList.push(form.getFieldsValue().medicineList[i].medicine);
                        }
                    }
                }
                if(idList.some((item, index) => idList.indexOf(item) !== index)){
                    hasDuplicate = true
                }
                if (hasDuplicate) {
                    return Promise.reject(
                        new Error(
                            formatMessage({
                                id: "subject.dispensing.label.selectedRepeatedly",
                            })
                        )
                    );
                }
                return Promise.resolve(); // 返回一个 resolved 状态的 Promise
            }
        }
    }
    
    const setFieldValueInList = (index: any) => {
        var medicineList = form.getFieldValue("medicineList")
        for(let i = 0 ; i < medicineList.length ; i++ ){
            if(i === index){
                medicineList[index].count = null;
            }
        }
        form.setFieldsValue({medicineList});
    };

    const selectOtherMaxValidator = (index: any) => ({
        validator: () => {
            var maxCount = medicines.find((item: any) => item.id === form.getFieldValue("medicineList")[index].medicine).count;
            var count = form.getFieldValue("medicineList")[index].count
            if (count > maxCount) {
                return Promise.reject(formatMessage({ id: "subject.dispensing.medicine.validator" }, {data:maxCount}));
            }
            return Promise.resolve();
        },
    });

    const [showDTPend, setShowDTPend] = useSafeState<number>(-1);

    const changeSelect = () => {
        let infoMap :any = {}
        let setValue :any = []
        form.getFieldsValue().medicineList.forEach((item: any, index: number) => {
            if (item?.medicine) {
                let info :any= medicines?.find((it:any)=>it.medicineId === item.medicine || it.id === item.medicine)
                infoMap[index+""] = {dtp:info?.dtps}
                if (info?.dtps?.length === 1) {
                    item.dtp = info?.dtps[0]
                }
            }
            setValue.push({...item})
        })
        form.setFieldsValue({...form.getFieldsValue()})
        setSelectListInfo(infoMap)
        let set = new Set();
        let dtpValue :any = -1
        Object.keys(infoMap)?.map(
            (key:any)=>{
                infoMap[key]?.dtp?.map((value:any)=>{
                    set.add(value)
                    dtpValue =value
                })
            }
        )
        if (set.size === 1) {
            setShowDTPend(dtpValue)
        }else{
            setShowDTPend(-1)
        }
    }

    const { TextArea } = Input;

    React.useImperativeHandle(props.bind, () => ({show}));
    const g = useGlobal()
    const subjectWidthRef :any = React.useRef();
    const size :any= useSize(subjectWidthRef)
    const [width,setWidth] = useSafeState(g.lang === "en"? 268: 136)
    useEffect(()=>{
        if (visible){
            let w = size?.width+ 20 < width ? width : size?.width+ 20
            if (!isNaN(w)){
                setWidth(w)
            }
        }
        form.setFieldsValue({...form.getFieldsValue(), "medicineList": [{"medicine": null}]})
    },[type,size,visible])


    const getDtpOption = (value: any) => {
        const option = DTPOptionIntl(formatMessage).find((item:any) => item.value === value)
        const label = <div style={{display: 'flex'}}>
            <span style={{fontSize: '14px', color: 'rgba(46, 50, 58, 1)'}}>{option?.label}</span>
            <span>&nbsp;</span>
            <span style={{fontSize: '12px', color: 'rgba(103, 114, 131, 1)'}}>{option?.desc?.replace("。", "")?.replace(".", "")}</span>
        </div>
        return <Select.Option value={value} label={label}>
            <div style={{display: 'flex', flexDirection: 'column'}}>
                <span style={{fontSize: '14px', color: 'rgba(46, 50, 58, 1)'}}>{option?.label}</span>
                <span style={{fontSize: '12px', color: 'rgba(147, 150, 155, 1)'}}>{option?.desc}</span>
            </div>
        </Select.Option>
    }

    return (
        <Modal
            // forceRender={true}
            className="custom-medium-modal"
            title={type === 1?formatMessage({id: 'subject.dispensing.replace'}):formatMessage({id: 'subject.dispensing.cancel'})}
            visible={visible}
            onCancel={hide}
            destroyOnClose={true}
            centered={true}
            maskClosable={false}
            footer={
                <Row justify="end">
                    <Col style={{marginRight:"16px"}}>
                        <Button onClick={hide}>
                            <FormattedMessage id="common.cancel"/>
                        </Button>
                    </Col>
                    <Col>
                        <Button type="primary" loading={deleteDispensingLoading || replaceDrugLoading || edcSubjectVerificationRunLoading} onClick={confirmDispensing}>
                            <FormattedMessage id="common.ok"/>
                        </Button>
                    </Col>
                </Row>
            }
        >

            <Form form={form} {...{
                labelCol: { style: {   width: width} },
            }}>
                {
                    type === 1?
                        <>
                            <Form.Item  label={<span ref={subjectWidthRef}>{auth.attribute?((auth.attribute.info.subjectReplaceText === "" && auth.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((auth.attribute.info.subjectReplaceText !== "" && auth.attribute.info.subjectReplaceTextEn === "")?auth.attribute.info.subjectReplaceText:((auth.attribute.info.subjectReplaceText === "" && auth.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?auth.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?auth.attribute.info.subjectReplaceTextEn:auth.attribute.info.subjectReplaceText)))):formatMessage({id:"subject.number"})}</span> } className="mar-ver-5">
                                <Row className="mar-lft-10">{record.shortname}</Row>
                            </Form.Item>
                            {
                                !(!record.randomNumber || record.randomNumber ===  "" || record.status === 1 || record.status === 2 || !auth.attribute?.info.random) &&
                                <Form.Item  label={formatMessage({ id: 'projects.randomization.randomNumber' })} className="mar-ver-5">
                                    <Row className="mar-lft-10">{record.randomNumber!== ""?record.randomNumber:"-"}</Row>
                                </Form.Item>
                            }

                            <Form.Item label={formatMessage({ id: 'visit.cycle.name' })}>
                                <Row className="mar-lft-10">{dispensing.visitInfo?.name}</Row>
                            </Form.Item>
                            {/* <Form.Item required={true} rules={[replaceValidator]}  name="medicines" label={formatMessage({ id: 'subject.dispensing.replace.select' })}>
                                <Select className="full-width" style={{ width: 120 }} mode="multiple">
                                    {
                                        medicines.map(
                                            (it:any) =>
                                                <Select.Option key={it.medicineId?it.medicineId:it.id} value={it.medicineId?it.medicineId:it.id}>{it.number?it.number:it.name+"["+it.batch+"]["+it.expireDate+"]"}</Select.Option>
                                        )
                                    }
                                </Select>
                            </Form.Item> */}

                            <Form.Item 
                                required={true}
                                label={formatMessage({ id: 'subject.dispensing.replace.select' })}
                                style={{marginBottom: '0'}}

                            >
                                <Form.List name={"medicineList"} rules={[ValidatorList()]}>
                                    {(fields: any, { add, remove }, {errors}) => (
                                    <>
                                        {fields.map((field: any, index: any) => (
                                        <Row
                                            key={field.key}
                                            className={"full-width"}
                                            style={{marginBottom: '16px', display: 'flex', justifyContent: 'space-between'}}
                                        >
                                            <Col style={{flex: 1}}>
                                                <Row
                                                    style={{
                                                        // marginBottom: 8,
                                                        // width: g.lang === "en"?430:550,
                                                        display: 'flex'
                                                    }}
                                                >

                                                    <Form.Item
                                                        style={{marginBottom: "0", flex: 1, width:
                                                                form.getFieldValue("medicineList")[index]!==undefined  && medicines.find((item: any) => (item.id && item.id) === form.getFieldValue("medicineList")[index].medicine)?
                                                                    g.lang === "en"?260:430
                                                                    :"100%"

                                                                 }}
                                                        {...field}
                                                        name={[field.name, "medicine"]}
                                                        required={true}
                                                        rules={[replaceValidator]}
                                                    >
                                                        <Select
                                                            className="full-width"
                                                            style={{ width: 120 }}
                                                            placeholder={formatMessage({id: 'placeholder.select.common'})}
                                                            // onChange={selectOther}
                                                            onChange={(value: any) => {
                                                                // eslint-disable-next-line no-lone-blocks
                                                                {
                                                                    form.getFieldValue("medicineList")[index]!==undefined  && medicines.find((item: any) => (item.id && item.id) === form.getFieldValue("medicineList")[index].medicine)&&
                                                                    setFieldValueInList(index)
                                                                }
                                                                form.validateFields(["medicineList","medicine"]).then();
                                                                // console.log("medicines==" + JSON.stringify(medicines));
                                                                // console.log("medicines==" + JSON.stringify(form.getFieldsValue().medicineList));
                                                                const idList: any[] = [];
                                                                if (form.getFieldsValue().medicineList !== null && form.getFieldsValue().medicineList.length > 0) {
                                                                    for (let i = 0; i < form.getFieldsValue().medicineList.length; i++) {
                                                                        if (form.getFieldsValue().medicineList[i] !== null && form.getFieldsValue().medicineList[i] !== undefined) {
                                                                            if(form.getFieldsValue().medicineList[i].medicine !== null){
                                                                                idList.push(form.getFieldsValue().medicineList[i].medicine);
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                const values = form.getFieldsValue()
                                                                // console.log(values.medicineList)
                                                                // console.log(index)
                                                                values.medicineList[index].dtp = null
                                                                form.setFieldsValue(values)
                                                                setSelectOption(idList);
                                                                const updatedMedicines = choiceMedicines.map((it: any) => {
                                                                    if (idList.includes(it.medicineId?it.medicineId:it.id)) {
                                                                        return { ...it, disable: true };
                                                                    }
                                                                    return it;
                                                                });
                                                                setMedicines(updatedMedicines);
                                                                changeSelect()

                                                            }}
                                                        >
                                                            {
                                                                medicines.map(
                                                                    (it:any) =>
                                                                        <Select.Option
                                                                            key={it.medicineId?it.medicineId:it.id}
                                                                            value={it.medicineId?it.medicineId:it.id}
                                                                            disabled={it.disable}
                                                                        >
                                                                            {it.number?it.number:it.name+"["+it.batch+"]["+it.expireDate+"]"}
                                                                        </Select.Option>
                                                                )
                                                            }
                                                        </Select>
                                                    </Form.Item>

                                                    {
                                                        // (selectOthers.get(index))&&
                                                        form.getFieldValue("medicineList")[index]!==undefined  && medicines.find((item: any) => (item.id && item.id) === form.getFieldValue("medicineList")[index].medicine)&&
                                                        <Form.Item
                                                            // className="mar-ver-5"
                                                            style={{marginBottom: "0", marginLeft: "8px", width: 110 }}
                                                            {...field}
                                                            name={[field.name, "count"]}
                                                            colon={false}
                                                            rules={[{required: true, message:formatMessage({id: 'subject.dispensing.placeholder.input.count'})},selectOtherMaxValidator(index)]}
                                                            // initialValues={{
                                                            //     medicineList: {
                                                            //         count: medicines.find((item: any) => item.id === form.getFieldValue("medicineList")[index].medicine).count
                                                            //     }
                                                            // }}

                                                        >
                                                            <InputNumber
                                                                className="full-width"

                                                                title={formatMessage({id: 'subject.dispensing.placeholder.input.count'})}
                                                                placeholder={formatMessage({id: 'subject.dispensing.placeholder.input.count'})}
                                                                // defaultValue={medicines.find((item: any) => (item.id && item.id) === form.getFieldValue("medicineList")[index].medicine).count}
                                                                // max={medicines.find((item: any) => (item.id && item.id) === form.getFieldValue("medicineList")[index].medicine).count}
                                                                min={1}
                                                            />
                                                        </Form.Item>
                                                    }
                                                </Row>
                                                <Row>
                                                    {
                                                        record?.attribute?.info?.dtpRule === 1 && selectListInfo[field.name]?.dtp &&
                                                        showDTPend === -1 &&
                                                        <Row className={"full-width"}>
                                                            <Form.Item
                                                                {...field}
                                                                style={{marginBottom: 0, marginTop: 16, width: "100%" }}
                                                                name={[field.name, "dtp"]}
                                                                rules={[{
                                                                    required: true,
                                                                    message: formatMessage({
                                                                        id: "drug.batch.treatmentDesign.openSetting",
                                                                    }),
                                                                },]}

                                                            >
                                                                {
                                                                    <Select
                                                                        className="full-width"
                                                                        disabled={selectListInfo[field.name]?.dtp?.length === 1}
                                                                        // placeholder={formatMessage({id: 'logistics.dispensing.method'})}
                                                                        placeholder={formatMessage({id: 'drug.batch.treatmentDesign.openSetting'})}
                                                                        optionLabelProp="label"
                                                                    >
                                                                        {selectListInfo[field.name]?.dtp?.map((it:any)=> getDtpOption(it))}
                                                                    </Select>

                                                                }
                                                            </Form.Item>
                                                        </Row>
                                                    }
                                                </Row>
                                            </Col>
                                            <Col
                                                style={{display: 'flex', alignItems: 'center', paddingRight: choiceMedicines.length > 1 ? '24px' : 0}}
                                            >
                                                {choiceMedicines.length > 1 && <>
                                                    {
                                                        record?.attribute?.info?.dtpRule === 1 &&
                                                        selectListInfo[field.name]?.dtp && choiceMedicines.length > 1 &&
                                                        showDTPend < 0 ?
                                                            <Divider type={"vertical"} style={{paddingRight:0, margin: '0 12px', borderLeft: "1px solid #E0E1E2", height: '60%'}} />
                                                            :
                                                            <Divider type={"vertical"} style={{paddingRight:0, margin: '0 12px',borderLeft: "0px solid #E0E1E2", height: '60%'}} />

                                                    }
                                                </>}
                                                {
                                                    (choiceMedicines.length>1 && (choiceMedicines.length>selectOption.length) && (choiceMedicines.length > form.getFieldsValue().medicineList?.length)) &&
                                                    <svg
                                                        className="iconfont" width={16} height={16}
                                                        fill={"#999999"}
                                                        style={{cursor: 'pointer', marginRight: fields.length > 1 ? 12 : 0}}
                                                        onClick={(value) => {
                                                            // const height = initHeight + (fields.length + 1) * 75;
                                                            // setHeightCount(height);
                                                            add();
                                                            form.validateFields(["medicineList","medicine"]).then();
                                                            const idList: any[] = [];
                                                            if (form.getFieldsValue().medicineList !== null && form.getFieldsValue().medicineList.length > 0) {
                                                                for (let i = 0; i < form.getFieldsValue().medicineList.length; i++) {
                                                                    if (form.getFieldsValue().medicineList[i] !== null && form.getFieldsValue().medicineList[i] !== undefined) {
                                                                        if(form.getFieldsValue().medicineList[i].medicine !== null){
                                                                            idList.push(form.getFieldsValue().medicineList[i].medicine);
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            setSelectOption(idList)
                                                            const updatedMedicines = choiceMedicines.map((it: any) => {
                                                                if (idList.includes(it.medicineId?it.medicineId:it.id)) {
                                                                    return { ...it, disable: true };
                                                                }
                                                                return it;
                                                            });
                                                            setMedicines(updatedMedicines);
                                                        }}
                                                    >
                                                        <use xlinkHref="#icon-zengjia"></use>
                                                    </svg>
                                                }

                                                {fields.length > 1 && (
                                                        <svg
                                                            className="iconfont" width={16} height={16}
                                                            style={{cursor: 'pointer'}}
                                                            onClick={() => {
                                                                remove(field.name);
                                                                form.validateFields(["medicineList","medicine"]).then();
                                                                const idList: any[] = [];
                                                                if (form.getFieldsValue().medicineList !== null && form.getFieldsValue().medicineList.length > 0) {
                                                                    for (let i = 0; i < form.getFieldsValue().medicineList.length; i++) {
                                                                        if (form.getFieldsValue().medicineList[i] !== null && form.getFieldsValue().medicineList[i] !== undefined) {
                                                                            if(form.getFieldsValue().medicineList[i].medicine !== null){
                                                                                idList.push(form.getFieldsValue().medicineList[i].medicine);
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                setSelectOption(idList)
                                                                const updatedMedicines = choiceMedicines.map((it: any) => {
                                                                    if (idList.includes(it.medicineId?it.medicineId:it.id)) {
                                                                        return { ...it, disable: true };
                                                                    }
                                                                    return it;
                                                                });
                                                                setMedicines(updatedMedicines);
                                                                changeSelect()

                                                            }}
                                                            fill={"#999999"}
                                                        >
                                                            <use xlinkHref="#icon-shanchu"></use>
                                                        </svg>
                                                )}
                                            </Col>

                                        </Row>
                                        ))}
                                        {
                                            (record?.attribute?.info?.dtpRule === 1 && showDTPend > -1) ?
                                            <Row>
                                                <Form.Item
                                                    style={{marginBottom: '16px', width: '100%' }}
                                                >
                                                    {
                                                        <Select placeholder={formatMessage({id:"logistics.dispensing.method"})} disabled={true} value={showDTPend} optionLabelProp="label">
                                                            {DTPOption?.map((it:any) => getDtpOption(it.value))}
                                                        </Select>
                                                    }
                                                </Form.Item>
                                            </Row>
                                                :
                                                null
                                        }
                                        {<Form.ErrorList errors={errors}/>}
                                    </>
                                    )}
                                </Form.List>
                            </Form.Item>

                            {
                                record?.attribute?.info?.dtpRule === 2 && dtp && <ModeLogistics dtpType={dtpType} otherLogistics={otherLogistics} setOtherLogistics={setOtherLogistics} type={sendType} setType={setSendType} form={form} />
                            }
                        </>
                        :
                        null
                }
                <Form.Item label={formatMessage({id: 'common.reason'})} rules={[{ required:true }]} name="reason">
                    <TextArea
                        placeholder={formatMessage({id: 'common.required.prefix'})}
                        maxLength={500} showCount allowClear className="full-width" autoSize={{ minRows: 2, maxRows: 6 }}
                    />
                </Form.Item>
                <Form.Item style={{marginBottom: "0px", marginTop:16}} name="remark"
                       label={formatMessage({id: 'common.remark'})}>
                <TextArea placeholder={formatMessage({id: 'common.required.prefix'})} allowClear
                          className="full-width" autoSize={{minRows: 2, maxRows: 6}} maxLength={500}/>
            </Form.Item>
            </Form>
            <DispensingConfirm bind={dispensing_confirm_pt} save={replace_confirm}/>

        </Modal>

    );
};
