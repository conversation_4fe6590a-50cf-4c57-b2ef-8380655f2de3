import React, {useState, useRef, useEffect} from 'react'
import {Button, Select, Spin, message, Card, Space, Row, Col, Statistic, Tag, Divider, Alert, Typography} from "antd";
import {FileSearchOutlined, FilterOutlined, ReloadOutlined, PlusOutlined, WarningOutlined, CheckCircleOutlined, DiffOutlined} from '@ant-design/icons';
import {useFetch} from "../../../../../hooks/request";
import {flatModules, get, getAllKeys, post, splitModule} from "../util";
import {message as zhmessage} from "../../../../../locales/zh";
import {message as enmessage} from "../../../../../locales/en";
import {LocalesTable} from "./table";
import {allFields, allFlatModules, allModules, editFields} from "../../locales";
import {unuseKeys} from "../special-field/unuse";
import {machineKeys} from "../special-field/machine";
import {backendKeys} from "../special-field/backend";
import {menuModule} from "./menu-permissions";
import PathDiffModal from "./path-diff-modal";
import _ from "lodash";
import {useAtom} from "jotai";
import {localesDataAtom} from "./context";
import {reportModule} from "./report";
import {localesProject} from "../../locales/module";
import {AddFieldModal} from "./add-field-modal";

const { Title, Text } = Typography;

// 直接扫描文件中的id
export const LocalesScanFile = (props) => {

    const [data, setData] = useState([])
    const [showData, setShowData] = useState([])
    const [options, setOptions] = useState([])
    const [localesData, setLocalesData] = useAtom(localesDataAtom)
    const [unManageFields, setUnManageFields] = useState([])
    const addFieldModalRef = useRef(null)
    const pathDiffModalRef = useRef(null)

    // 新增状态管理
    const [currentFilter, setCurrentFilter] = useState(null)
    const [statistics, setStatistics] = useState({
        total: 0,
        uniqueKeys: 0,
        duplicateKeys: 0,
        filtered: 0,
        unManaged: 0,
        translated: 0
    })

    // 表格筛选状态
    const [tableSearch, setTableSearch] = useState({
        path: [], type: '', value: '', excludeOperator: false, excludeMenu: false,
        excludeUnuseKey: false, excludeMachineKey: false, duplicateRemoval: false,
        excludeBackendKey: false, unScan: false
    })

    // 筛选后的数据
    const [filteredData, setFilteredData] = useState([])



    const { run: getFields, loading } = useFetch(() => get('read-file'), {
        manual: true,
        onSuccess: (result) => {
            const module = [...result, menuModule, reportModule]
            for (let i = 0; i < module.length; i++) {
                if (module[i].key === 'project_setting') {
                    module[i] = splitModule(module[i], localesProject[1])
                }
            }

            console.log(module)

            const allKeys = getAllKeys(module)
            module.push({
                key: "un_scan",
                name: "没扫描到的字段",
                children: Object.keys(zhmessage).filter(it => !allKeys.includes(it)).map(it => ({
                    key: it
                }))
            })

            const m = flatModules(module)

            // 对初始数据应用相邻排序
            const sortedFields = sortDataWithDuplicatesAdjacent(m.fields);

            setData(sortedFields)
            setShowData(sortedFields)
            setOptions(m.options)
            setLocalesData({...localesData, scanFile: sortedFields})

            // 自动检测未管理字段
            const mangeKeys = allFields.map(it => it.key)
            const unManageData = sortedFields.filter(it => !mangeKeys.includes(it.key))
            setUnManageFields(unManageData)

            // 初始化筛选数据
            setFilteredData(sortedFields);

            // 计算统计信息
            updateStatistics(sortedFields, sortedFields)

            // 显示检测结果
            if (unManageData.length > 0) {
                message.info(`检测到 ${unManageData.length} 个未管理的字段，可以点击"批量添加字段"按钮进行管理`);
            }
        },
        onError: (e) => {
            console.log(e)
        }
    })

    // 更新统计信息
    const updateStatistics = (allData, filteredData) => {
        // 按key分组统计
        const keyGroups = _.groupBy(allData, 'key');
        const uniqueKeys = Object.keys(keyGroups);
        const duplicateKeys = uniqueKeys.filter(key => keyGroups[key].length > 1);

        // 翻译统计（按唯一key计算）
        const translated = uniqueKeys.filter(key =>
            zhmessage[key] && enmessage[key] && zhmessage[key] !== enmessage[key]
        ).length;

        setStatistics({
            total: allData.length,
            uniqueKeys: uniqueKeys.length,
            duplicateKeys: duplicateKeys.length,
            filtered: filteredData.length,
            unManaged: unManageFields.length,
            translated: translated
        });
    };

    // 文本匹配函数
    const like = (text1, text2) => {
        if (!text1 || !text2) return false
        return text1?.toUpperCase().indexOf(text2?.toUpperCase()) !== -1
    }

    // 将重复字段排序到相邻位置
    const sortDataWithDuplicatesAdjacent = (data) => {
        // 统计每个key的出现次数
        const keyCounts = _.countBy(data, 'key');

        // 分离重复字段和非重复字段
        const duplicateFields = [];
        const singleFields = [];

        data.forEach(item => {
            if (keyCounts[item.key] > 1) {
                duplicateFields.push(item);
            } else {
                singleFields.push(item);
            }
        });

        // 对重复字段按key分组并排序
        const groupedDuplicates = _.groupBy(duplicateFields, 'key');
        const sortedDuplicateGroups = Object.keys(groupedDuplicates)
            .sort()
            .map(key => {
                // 每组内按路径排序
                return groupedDuplicates[key].sort((a, b) => {
                    const pathA = a.path ? a.path.join('/') : '';
                    const pathB = b.path ? b.path.join('/') : '';
                    return pathA.localeCompare(pathB);
                });
            });

        // 展平重复字段组
        const sortedDuplicates = sortedDuplicateGroups.flat();

        // 对单个字段按key排序
        const sortedSingles = singleFields.sort((a, b) => a.key.localeCompare(b.key));

        // 重复字段在前，单个字段在后
        return [...sortedDuplicates, ...sortedSingles];
    };

    // 应用表格筛选逻辑
    const applyTableFilter = (sourceData, searchParams) => {
        const {path, type, value, excludeOperator, excludeMenu,
            excludeUnuseKey, excludeMachineKey, duplicateRemoval, excludeBackendKey, unScan} = searchParams

        let filtered = sourceData
            .filter(it => !path || path.length === 0 || it.path.slice(0, path.length).toString() === path.toString())
            .filter(it => !type || it.type === type)
            .filter(it => !value || like(it.key, value) || like(zhmessage[it.key], value) || like(enmessage[it.key], value))
            .filter(it => !excludeOperator || !it.key.startsWith('operation'))
            .filter(it => !excludeMenu || !it.key.startsWith('menu'))
            .filter(it => !excludeUnuseKey || !unuseKeys.includes(it.key))
            .filter(it => !excludeMachineKey || !machineKeys.includes(it.key))
            .filter(it => !excludeBackendKey || !backendKeys.includes(it.key))
            .filter(it => !unScan || it.path[0] !== 'un_scan')

        if (duplicateRemoval) {
            filtered = _.uniqBy(filtered, 'key');
        }

        // 将重复字段排序到相邻位置
        return sortDataWithDuplicatesAdjacent(filtered);
    }

    // 更新筛选数据
    const updateFilteredData = () => {
        const filtered = applyTableFilter(showData, tableSearch);
        setFilteredData(filtered);
        updateStatistics(data, filtered);
    };

    // 监听数据和筛选条件变化
    useEffect(() => {
        if (showData.length > 0) {
            const filtered = applyTableFilter(showData, tableSearch);
            setFilteredData(filtered);
            updateStatistics(data, filtered);
        }
    }, [showData, tableSearch]);

    // 通用筛选函数
    const applyFilter = (filterFn, filterName) => {
        const filtered = data.filter(filterFn);
        setShowData(filtered);
        setCurrentFilter(filterName);
        updateStatistics(data, filtered);
    };

    const unTranslation = () => {
        applyFilter(
            it => !zhmessage[it.key] || !enmessage[it.key] || (zhmessage[it.key] === enmessage[it.key]),
            '未完全翻译字段'
        );
    }

    const unuseButUse = () => {
        applyFilter(
            it => it.path.toString() !== 'un_scan' && unuseKeys.includes(it.key),
            '应该废弃但页面中使用到的字段'
        );
    }

    const unMange = () => {
        const mangeKeys = allFields.map(it => it.key)
        const unManageData = data.filter(it => !mangeKeys.includes(it.key))
        // 只更新未管理字段数据，不改变显示
        setUnManageFields(unManageData)
        // 更新统计信息
        updateStatistics(data, showData)
        setShowData(unManageData)

        // 显示提示信息
        if (unManageData.length > 0) {
            message.success(`发现 ${unManageData.length} 个未管理的字段，可以点击"批量添加"按钮进行管理`);
        } else {
            message.info('所有字段都已被管理');
        }
    }

    // 已经废弃的字段
    const unuseField = () => {
        applyFilter(
            it => unuseKeys.includes(it.key),
            '已经废弃的字段'
        );
    }

    const unuseButInMange = () => {
        const allKeys = allFields.map(it => it.key)
        applyFilter(
            it => unuseKeys.includes(it.key) && allKeys.includes(it.key),
            '应该废弃但还是纳入管理的字段'
        );
    }

    const noScanButInMange = () => {
        const allKeys = allFields.map(it => it.key)
        const unScanKeys = data.filter(it => it.path[0] === 'un_scan').map(it => it.key)
        applyFilter(
            it => allKeys.includes(it.key) && unScanKeys.includes(it.key),
            '没扫描到但是纳入管理的字段'
        );
    }


    const repeatField = () => {
        const counts = _.countBy(data, 'key')
        const repeatNames = Object.keys(counts).filter(key => counts[key] > 1)

        // 按key字母顺序排序，确保相同key的记录聚集在一起
        const sortedRepeatNames = repeatNames.sort();

        // 按排序后的key顺序，将相同key的记录放在一起
        const groupedData = sortedRepeatNames.reduce((res, key) => {
            const keyRecords = data.filter(it => it.key === key);
            // 在每个key组内，按路径排序，让展示更有序
            const sortedKeyRecords = keyRecords.sort((a, b) => {
                const pathA = a.path ? a.path.join('/') : '';
                const pathB = b.path ? b.path.join('/') : '';
                return pathA.localeCompare(pathB);
            });

            res = res.concat(sortedKeyRecords);
            return res;
        }, []);

        // 直接设置分组后的数据
        setShowData(groupedData);
        setCurrentFilter('重复字段');
        updateStatistics(data, groupedData);
    }

    // 显示全部数据
    const showAll = () => {
        const sortedData = sortDataWithDuplicatesAdjacent(data);
        setShowData(sortedData)
        setCurrentFilter(null)
        updateStatistics(data, sortedData)
    }

    const reportField = () => {
        setShowData(data.filter(it => it.key.startsWith('report.')))
    }

    const pathNotSame = () => {
        const showData = data.filter(it =>
            !!editFields.find(f => f.key === it.key && f.path?.join('/') !== it.path?.join('/'))
        )
        setShowData(showData)
    }

    const notLocales = () => {
        const keys = Object.keys(zhmessage)
        setShowData(data.filter(it => !keys.includes(it.key)))
    }

    const translationIsSame = () => {
        const sameData = _.uniqBy(data, 'key')
        const counts = _.countBy(sameData, 'value')
        const repeatNames =  Object.keys(counts).filter(key => counts[key] > 1)
        const showData = repeatNames.reduce((res, item) => {
            res = res.concat(sameData.filter(it => it.value === item))
            return res
        }, [])
        setShowData(showData)
    }


    // 中文一致但是英文不一致的字段
    const partSame = () => {
        const messageList = data.map(it => ({
            ...it,
            cn: zhmessage[it.key],
            en: enmessage[it.key]
        }))
        const d = _.uniqWith(messageList, (one, two) => one.cn ===  two.cn && one.en === two.en)
        const counts = _.countBy(d, 'cn')
        const repeatNames =  Object.keys(counts).filter(key => counts[key] > 1)
        const showData =  repeatNames.reduce((res, item) => {
            res = res.concat(d.filter(it => it.cn === item))
            return res
        }, [])
        setShowData(showData)
    }


    // 打开添加字段弹窗
    const openAddFieldModal = () => {
        if (unManageFields.length === 0) {
            message.warning('请先过滤出未管理的字段');
            return;
        }
        addFieldModalRef.current?.open();
    };

    // 打开路径差异弹窗
    const openPathDiffModal = () => {
        if (pathDiffModalRef.current) {
            pathDiffModalRef.current.open(options, allModules);
        }
    };

    // 处理字段添加成功后的回调
    const handleAddFieldSuccess = () => {
        addFieldModalRef.current?.close();
        // 清空未管理字段列表，因为已经添加完成
        setUnManageFields([]);
        // 更新统计信息
        updateStatistics(data, showData);
        // 显示成功提示
        message.success('字段添加完成，所有字段已被管理！');
    };

    const filterList = [
        {label: '未完全翻译字段', onClick: unTranslation},
        {label: '应该废弃但页面中使用到的字段', onClick: unuseButUse},
        {label: '检测未管理字段', onClick: unMange},
        {label: '已经废弃的字段', onClick: unuseField},
        {label: '应该废弃但还是纳入管理的字段', onClick: unuseButInMange},
        {label: '没扫描到但是纳入管理的字段', onClick: noScanButInMange},
        // TODO 扫描到了但是相关路径下没有的字段，需要二次确认
        {label: '使用到但是路径不一致的字段', onClick: pathNotSame},
        {label: '多个模块重复出现的key', onClick: repeatField},
        {label: '使用到的报表字段', onClick: reportField},
        {label: '扫描到但是没有配置的字段', onClick: notLocales},
        {label: '翻译一致的字段', onClick: translationIsSame},
        {label: '中文一致但是英文不一致的字段', onClick: partSame},
        // TODO 路径下扫描出来了，但是模块下没有进行维护

    ].map((it, index) => ({...it, value: index}))

    return (
        <div style={{
            height: 'calc(100vh - 24px)',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: '#f5f5f5',
            overflow: 'hidden'
        }}>
            <Spin spinning={loading} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* 固定头部区域 */}
                <div style={{
                    padding: '8px 16px 0 16px',
                    flexShrink: 0
                }}>
                    {/* 页面标题 - 单行布局 */}
                    <div style={{
                        marginBottom: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px'
                    }}>
                        <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                            <FileSearchOutlined style={{ marginRight: '8px' }} />
                            多语言字段管理
                        </Title>
                        <Text type="secondary" style={{ fontSize: '12px', color: '#999' }}>
                            扫描、筛选和管理项目中的多语言字段
                        </Text>
                    </div>

                    {/* 紧凑统计面板 */}
                    {data.length > 0 && (
                        <Card size="small" style={{ marginBottom: '8px' }}>
                            <Row gutter={[8, 0]}>
                                <Col span={4}>
                                    <Statistic
                                        title="总字段数"
                                        value={statistics.total}
                                        prefix={<FileSearchOutlined style={{ color: '#1890ff' }} />}
                                        valueStyle={{ color: '#1890ff', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="唯一Key数"
                                        value={statistics.uniqueKeys}
                                        prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                                        valueStyle={{ color: '#52c41a', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="重复Key数"
                                        value={statistics.duplicateKeys}
                                        prefix={<WarningOutlined style={{ color: '#ff4d4f' }} />}
                                        valueStyle={{ color: '#ff4d4f', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="当前显示"
                                        value={statistics.filtered}
                                        prefix={<FilterOutlined style={{ color: '#722ed1' }} />}
                                        valueStyle={{ color: '#722ed1', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="未管理字段"
                                        value={statistics.unManaged}
                                        prefix={<WarningOutlined style={{ color: '#faad14' }} />}
                                        valueStyle={{ color: '#faad14', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="已翻译字段"
                                        value={statistics.translated}
                                        prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                                        valueStyle={{ color: '#52c41a', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                            </Row>
                        </Card>
                    )}

                    {/* 紧凑操作区域 */}
                    <Card size="small" style={{ marginBottom: '8px' }}>
                        <Row gutter={[16, 8]} align="middle">
                            <Col span={16}>
                                <Space wrap size="small">
                                    <Button
                                        type="primary"
                                        icon={<FileSearchOutlined />}
                                        onClick={getFields}
                                        disabled={data.length > 0}
                                        size="small"
                                    >
                                        读取字段
                                    </Button>

                                    <Select
                                        allowClear
                                        placeholder="选择过滤类型"
                                        style={{ width: '160px' }}
                                        options={filterList}
                                        onChange={(value, option) => option ? option?.onClick() : showAll()}
                                        value={currentFilter ? filterList.find(f => f.label === currentFilter)?.value : undefined}
                                        size="small"
                                    />

                                    <Button
                                        icon={<FilterOutlined />}
                                        onClick={() => setShowData(_.uniqBy(data, 'key'))}
                                        disabled={data.length === 0}
                                        size="small"
                                    >
                                        去重
                                    </Button>

                                    <Button
                                        icon={<WarningOutlined />}
                                        onClick={repeatField}
                                        disabled={data.length === 0 || statistics.duplicateKeys === 0}
                                        size="small"
                                        danger={statistics.duplicateKeys > 0}
                                    >
                                        重复字段 ({statistics.duplicateKeys})
                                    </Button>

                                    <Button
                                        icon={<ReloadOutlined />}
                                        onClick={showAll}
                                        disabled={data.length === 0}
                                        size="small"
                                    >
                                        重置
                                    </Button>

                                    <Button
                                        icon={<DiffOutlined />}
                                        onClick={openPathDiffModal}
                                        disabled={data.length === 0}
                                        size="small"
                                        style={{
                                            color: '#fa8c16',
                                            borderColor: '#fa8c16'
                                        }}
                                    >
                                        差异路径
                                    </Button>
                                </Space>
                            </Col>

                            <Col span={8} style={{ textAlign: 'right' }}>
                                <Space size="small">
                                    {currentFilter && (
                                        <Tag color="blue" style={{ margin: 0, fontSize: '11px' }}>
                                            {currentFilter}
                                        </Tag>
                                    )}
                                    {unManageFields.length > 0 && (
                                        <Button
                                            type="primary"
                                            icon={<PlusOutlined />}
                                            onClick={openAddFieldModal}
                                            size="small"
                                        >
                                            批量添加字段 ({unManageFields.length})
                                        </Button>
                                    )}
                                    {data.length > 0 && unManageFields.length === 0 && (
                                        <Tag color="green" style={{ fontSize: '11px' }}>
                                            所有字段已管理
                                        </Tag>
                                    )}
                                </Space>
                            </Col>
                        </Row>
                    </Card>
                </div>

                {/* 可滚动的表格区域 */}
                <div style={{
                    flex: 1,
                    padding: '0 16px 8px 16px',
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column'
                }}>
                    <div style={{
                        flex: 1,
                        backgroundColor: '#fff',
                        borderRadius: '6px',
                        border: '1px solid #f0f0f0',
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column'
                    }}>
                        <LocalesTable
                            dataSource={showData}
                            modules={options}
                            filteredData={filteredData}
                            search={tableSearch}
                            onSearchChange={setTableSearch}
                            allFields={allFields}
                            allModules={allFlatModules}
                        />
                    </div>
                </div>

                <AddFieldModal
                    ref={addFieldModalRef}
                    onSuccess={handleAddFieldSuccess}
                    dataSource={unManageFields}
                    modules={allModules}
                    flatModules={allFlatModules}
                    options={options}
                />

                <PathDiffModal
                    ref={pathDiffModalRef}
                />
            </Spin>
        </div>
    )

}