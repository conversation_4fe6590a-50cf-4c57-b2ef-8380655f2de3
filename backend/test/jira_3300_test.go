package test

import (
	"clinflash-irt/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"testing"
)

func UpdateSupplyPlanMedicine(t *testing.T) {
	var data []map[string]interface{}
	//envOID, _ := primitive.ObjectIDFromHex("60eff6c933189bbf47dfed37")
	cursor, err := DB.Collection("supply_plan_medicine").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		panic(err)
	}
	for _, datum := range data {
		auto_supply_size := datum["info"].(map[string]interface{})["auto_supply_size"]
		auto_supply_sizes := []interface{}{auto_supply_size}
		_, err = DB.Collection("supply_plan_medicine").UpdateOne(nil,
			bson.M{"_id": datum["_id"]},
			bson.M{"$set": bson.M{"info.auto_supply_size": auto_supply_sizes}},
		)
		if err != nil {
			panic(err)
		}
	}

}

func UpdateVisit(t *testing.T) {
	var data []models.VisitCycle
	//envOID, _ := primitive.ObjectIDFromHex("60936580950615256e92d586")
	cursor, err := DB.Collection("visit_cycle").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		panic(err)
	}
	for _, datum := range data {
		var randomDesign models.RandomDesign
		match := bson.M{"env_id": datum.EnvironmentID}
		if !datum.CohortID.IsZero() {
			match["cohort_id"] = datum.CohortID
		}
		err := DB.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
		if err != nil && err != mongo.ErrNoDocuments {
			panic(err)
		}
		group := []interface{}{}
		for _, randomGroup := range randomDesign.Info.Groups {
			group = append(group, randomGroup.Name)
		}
		randomIndex := false
		for i, config := range datum.Infos {
			if config.Random {
				randomIndex = true
			}
			if randomIndex {
				datum.Infos[i].Group = group
			} else {
				datum.Infos[i].Group = []interface{}{"N/A"}
			}
		}
		_, err = DB.Collection("visit_cycle").UpdateOne(nil,
			bson.M{"env_id": datum.EnvironmentID, "cohort_id": datum.CohortID},
			bson.M{"$set": bson.M{
				"update_infos.infos":      datum.Infos,
				"update_infos.visit_type": 0,
				"visit_type":              0,
				"infos":                   datum.Infos,
			}},
		)
		if err != nil {
			panic(err)
		}
	}

}
