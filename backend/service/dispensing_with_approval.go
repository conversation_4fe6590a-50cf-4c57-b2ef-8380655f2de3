package service

import (
	"clinflash-irt/models"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DispensingWithApprovalRequest 带审批的发药请求
type DispensingWithApprovalRequest struct {
	SubjectID        primitive.ObjectID `json:"subjectId"`
	VisitCycleInfoID primitive.ObjectID `json:"visitCycleInfoId"`
	DispensingID     primitive.ObjectID `json:"dispensingId,omitempty"`
	VisitLabels      []string           `json:"visitLabels"`
	MedicineInfo     []interface{}      `json:"medicineInfo"`
	FormulaMedicine  []interface{}      `json:"formulaMedicine"`
	Remark           string             `json:"remark"`
	Reason           string             `json:"reason,omitempty"`           // 超窗申请原因
	ApprovalRemark   string             `json:"approvalRemark,omitempty"`   // 超窗申请备注
}

// DispensingWithApprovalResponse 带审批的发药响应
type DispensingWithApprovalResponse struct {
	Success         bool                       `json:"success"`
	NeedApproval    bool                       `json:"needApproval"`
	ApprovalNumber  string                     `json:"approvalNumber,omitempty"`
	Message         string                     `json:"message"`
	DispensingInfo  *models.RemoteSubjectDispensing `json:"dispensingInfo,omitempty"`
	ApprovalInfo    *OutWindowApprovalResponse `json:"approvalInfo,omitempty"`
}

// ProcessDispensingWithApproval 处理带审批的发药请求
// 这个方法展示了如何在实际的发药流程中集成超窗审批判断
func ProcessDispensingWithApproval(ctx *gin.Context, req DispensingWithApprovalRequest) (*DispensingWithApprovalResponse, error) {
	// 1. 首先检查是否需要超窗审批
	canProceed, approvalResponse, err := CheckOutWindowApprovalProcess(
		ctx, 
		req.SubjectID, 
		req.VisitCycleInfoID, 
		req.DispensingID,
		req, // 将整个请求作为requestData保存
		req.Reason,
		req.ApprovalRemark,
	)
	
	if err != nil {
		return &DispensingWithApprovalResponse{
			Success: false,
			Message: "检查超窗审批流程失败: " + err.Error(),
		}, err
	}

	// 2. 如果需要审批，返回审批信息
	if !canProceed {
		return &DispensingWithApprovalResponse{
			Success:        false,
			NeedApproval:   true,
			ApprovalNumber: approvalResponse.ApprovalNumber,
			Message:        approvalResponse.Message,
			ApprovalInfo:   approvalResponse,
		}, nil
	}

	// 3. 如果不需要审批，继续正常的发药流程
	dispensingInfo, err := processNormalDispensing(ctx, req)
	if err != nil {
		return &DispensingWithApprovalResponse{
			Success: false,
			Message: "发药处理失败: " + err.Error(),
		}, err
	}

	return &DispensingWithApprovalResponse{
		Success:        true,
		NeedApproval:   false,
		Message:        "发药成功",
		DispensingInfo: dispensingInfo,
	}, nil
}

// processNormalDispensing 处理正常的发药流程
// 这里调用现有的发药方法
func processNormalDispensing(ctx *gin.Context, req DispensingWithApprovalRequest) (*models.RemoteSubjectDispensing, error) {
	// 这里应该调用现有的发药方法，例如：
	// return UpdateDispensingVisit(ctx, req.SubjectID.Hex(), req.VisitCycleInfoID.Hex(), 
	//     req.VisitLabels, req.Remark, roleID)
	
	// 为了示例，这里返回一个模拟的结果
	return &models.RemoteSubjectDispensing{
		// 填充实际的发药结果
	}, nil
}

// ApproveOutWindowDispensing 审批超窗发药申请
// 这个方法用于审批人员审批超窗发药申请
func ApproveOutWindowDispensing(ctx *gin.Context, approvalNumber string, approved bool, rejectReason string) error {
	// 1. 查找审批记录
	approvalProcess, err := findApprovalByNumber(ctx, approvalNumber)
	if err != nil {
		return errors.WithStack(err)
	}

	if approvalProcess == nil {
		return errors.New("审批记录不存在")
	}

	// 2. 更新审批状态
	err = updateApprovalStatus(ctx, approvalProcess, approved, rejectReason)
	if err != nil {
		return errors.WithStack(err)
	}

	// 3. 如果审批通过，自动执行发药
	if approved {
		err = executeApprovedDispensing(ctx, approvalProcess)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// findApprovalByNumber 根据审批编号查找审批记录
func findApprovalByNumber(ctx *gin.Context, approvalNumber string) (*models.ApprovalProcess, error) {
	// 实现查找逻辑
	return nil, nil
}

// updateApprovalStatus 更新审批状态
func updateApprovalStatus(ctx *gin.Context, approval *models.ApprovalProcess, approved bool, rejectReason string) error {
	// 实现更新逻辑
	return nil
}

// executeApprovedDispensing 执行已审批的发药
func executeApprovedDispensing(ctx *gin.Context, approval *models.ApprovalProcess) error {
	// 1. 从审批记录中恢复原始请求数据
	var originalRequest DispensingWithApprovalRequest
	if approval.OutWindowApprovalData != nil {
		if requestDataStr, ok := approval.OutWindowApprovalData["request_data"].(string); ok {
			err := json.Unmarshal([]byte(requestDataStr), &originalRequest)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}

	// 2. 执行发药操作
	_, err := processNormalDispensing(ctx, originalRequest)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// GetOutWindowApprovalList 获取超窗审批列表
// 用于审批人员查看待审批的超窗发药申请
func GetOutWindowApprovalList(ctx *gin.Context, projectID, envID primitive.ObjectID, status int) ([]models.ApprovalProcess, error) {
	// 实现获取审批列表的逻辑
	return nil, nil
}

// 使用示例：在现有的发药接口中集成超窗审批
func ExampleDispensingHandler(ctx *gin.Context) {
	// 解析请求参数
	var req DispensingWithApprovalRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": "参数解析失败: " + err.Error()})
		return
	}

	// 处理带审批的发药请求
	response, err := ProcessDispensingWithApproval(ctx, req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	// 返回响应
	if response.NeedApproval {
		// 需要审批
		ctx.JSON(202, gin.H{
			"needApproval":   true,
			"approvalNumber": response.ApprovalNumber,
			"message":        response.Message,
		})
	} else if response.Success {
		// 发药成功
		ctx.JSON(200, gin.H{
			"success": true,
			"message": response.Message,
			"data":    response.DispensingInfo,
		})
	} else {
		// 发药失败
		ctx.JSON(400, gin.H{
			"success": false,
			"message": response.Message,
		})
	}
}

// 审批接口示例
func ExampleApprovalHandler(ctx *gin.Context) {
	approvalNumber := ctx.Param("approvalNumber")
	
	var req struct {
		Approved     bool   `json:"approved"`
		RejectReason string `json:"rejectReason,omitempty"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": "参数解析失败: " + err.Error()})
		return
	}

	err := ApproveOutWindowDispensing(ctx, approvalNumber, req.Approved, req.RejectReason)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	message := "审批拒绝"
	if req.Approved {
		message = "审批通过，发药已自动执行"
	}

	ctx.JSON(200, gin.H{
		"success": true,
		"message": message,
	})
}

// 获取审批列表接口示例
func ExampleGetApprovalListHandler(ctx *gin.Context) {
	projectID, _ := primitive.ObjectIDFromHex(ctx.Query("projectId"))
	envID, _ := primitive.ObjectIDFromHex(ctx.Query("envId"))
	status := 0 // 默认查询待审批的记录

	if statusStr := ctx.Query("status"); statusStr != "" {
		// 解析status参数
	}

	approvals, err := GetOutWindowApprovalList(ctx, projectID, envID, status)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, gin.H{
		"success": true,
		"data":    approvals,
	})
}
