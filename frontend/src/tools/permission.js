import {menu_project} from "../data/menu";

export const permissions = (permissions, operation) => {
    let permission = false
    if (permissions.permissions && operation && permissions.permissions.find(it => (it === operation))) {
        permission =true
    }
    return permission
}

export const permissionsCohort = (permissions, operation, cohortStatus) => {
    let permission = false
    if (permissions.permissions && operation && permissions.permissions.find(it => (it === operation))
        &&
        !permissions.cohortDisablePermission.find((item) => item.permission === operation)?.cohortDisableStatus?.find(it => it === cohortStatus)
    ) {
        permission = true
    }
    return permission
}

export const projectPermissions = (permissions, operation) => {
    let permission = false
    if (permissions.projectPermissions && operation && permissions.projectPermissions.find(it => (it === operation))) {
        permission =true
    }
    return permission
}

export const sysPermissions = (permissions, operation) => {
    let permission = false
    if (permissions.sysPermissions && operation && permissions.sysPermissions.find(it => (it === operation))) {
        permission =true
    }
    return permission
}

export const customerPermissions = (permissions, operation) => {
    let permission = false
    if (permissions.customerPermissions && operation && permissions.customerPermissions.find(it => (it === operation))) {
        permission =true
    }
    return permission
}

export const menuSelect = (permissions) => {
    let path = ""
    for (let i = 0; i < menu_project.length; i++) {
        if (permissions.page === null){
            return ""
        }
        if( permissions.page === ""){
            continue
        }
        if (permissions.page.findIndex((it) => it === menu_project[i].path) !== -1) {
            path = menu_project[i].path
            return path
        }
        if (menu_project[i].children) {
            // @ts-ignore
            for (let j = 0; j < menu_project[i].children.length; j++) {
                // @ts-ignore
                if (permissions.page.findIndex((it) => it === menu_project[i].path + menu_project[i].children[j].path) !== -1) {
                    // @ts-ignore
                    path = menu_project[i].path + menu_project[i].children[j].path
                    return path
                }
            }
        }
    }

    return ""
}

export const projectSettingPermission = (record, auth, operation) =>{
    return  sysPermissions(auth.permissions, operation) ||
    (customerPermissions(auth.permissions, operation) &&
        record.customerAdmin === true)
    ||
    (projectPermissions(auth.permissions, operation) &&
        record.administrators.includes(auth.user.id))
}

export const projectSettingButtonPermission = (record, auth, operation) =>{
    return  sysPermissions(auth.permissions, operation) ||
    (customerPermissions(auth.permissions, operation) &&
        record.customerAdmin === true)
    ||
    (permissionsProject(auth.projectPermissions, operation) &&
        record.administrators.includes(auth.user.id))
}



export const permissionsProject = (permissions, operation) => {
    let permission = false
    if (permissions && operation && permissions.find(it => (it === operation))) {
        permission =true
    }
    return permission
};