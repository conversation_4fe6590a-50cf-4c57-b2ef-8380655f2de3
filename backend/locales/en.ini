[menu_and_permission]
menu.home = Workbench
menu.settings = Settings
menu.settings.storehouse = Depots
menu.settings.roles = Permissions
menu.settings.users = Users
menu.projects = Projects
menu.projects.main = Projects
menu.projects.main.env = Project Environments
menu.projects.main.setting = Project Settings
menu.projects.main.setting.base = Basic Information
menu.projects.main.setting.function = Business Functions
menu.projects.main.setting.docking = External Docking
menu.projects.main.setting.permission = Project Permissions
menu.projects.main.setting.notice = Project Notification
menu.projects.main.setting.custom = Custom Process
menu.projects.project = Project Details
menu.projects.project.home = Home
menu.projects.project.overview = Overview
menu.projects.project.status = Status
menu.projects.project.task = Task
menu.projects.project.random.statistics = Randomization Statistics
menu.projects.project.subject.statistics = Subject Statistics
menu.projects.project.depot.ip.statistics = Depot Item Statistics
menu.projects.project.site.ip.statistics = Site Item Statistics
menu.projects.project.analysis = Anomaly Analysis
menu.projects.project.dynamics = Notification
menu.projects.project.sub = Subject
menu.projects.project.subject = Subjects
menu.projects.project.subject.urgent-unblinding = Emergency Unblinding
menu.projects.project.subject.urgent-unblinding.unblinding = Emergency Unblinding
menu.projects.project.subject.urgent-unblinding.approval-log = Approval record
menu.projects.project.subject.urgent-unblinding-pv = PV Unblinding
menu.projects.project.subject.urgent-unblinding.unblinding-pv = PV Unblinding
menu.projects.project.subject.urgent-unblinding.approval-log-pv = Approval record
menu.projects.project.subject.urgent-unblinding-ip = IP Unblinding
menu.projects.project.subject.urgent-unblinding.unblinding-ip = IP Unblinding
menu.projects.project.subject.urgent-unblinding.approval-log-ip = Approval record
menu.projects.project.subject.dispensing = Dispense
menu.projects.project.subject.medicine.trail = Trail
menu.projects.project.subject.trail = Trail
menu.projects.project.subject.visit.cycle = Visit Management
operation.project.subject.visit.cycle.view = View
operation.project.subject.visit.cycle.notice.view = Notification-View
operation.project.subject.visit.cycle.send.notice = Send Notification
menu.projects.project.supply = Inventory
menu.projects.project.supply.storehouse = Depot Inventory
menu.projects.project.supply.storehouse.summary = Overview
menu.projects.project.supply.storehouse.single = Item Management
menu.projects.project.supply.site = Site Inventory
menu.projects.project.supply.site.summary = Overview
menu.projects.project.supply.site.single = Item Management
menu.projects.project.supply.site.no_number = Unnumbered Item Management
menu.projects.project.supply.storehouse.no_number = Unnumbered Item Management
menu.projects.project.supply.shipment = Shipments
menu.projects.project.supply.shipment.approval = Approval Record
menu.projects.project.supply.shipment.logistics = Logistics
menu.projects.project.supply.drug_recovery = Return Shipments
menu.projects.project.supply.release-record = Quarantine Management
menu.projects.project.supply.drug = IP
menu.projects.project.supply.drug.order = Shipments
menu.projects.project.supply.drug.single = Item Management
menu.projects.project.supply.drug.no_number = Unnumbered Item Management
menu.projects.project.build = Project Design
menu.projects.project.build.storehouse = Depot Management
menu.projects.project.build.site = Site Management
menu.projects.project.build.site.supply-plan = Supply Plan
menu.projects.project.build.attributes = Properties
menu.projects.project.build.code_rule = Coding Configuration
menu.projects.project.build.simulate_random = Randomization Simulation
menu.projects.project.build.randomization = Randomization Design
menu.projects.project.build.randomization.design = Randomization Design
menu.projects.project.build.randomization.design.type = Randomization Type
menu.projects.project.build.randomization.design.group = Treatment Group
menu.projects.project.build.randomization.design.factor = Stratification Factor
menu.projects.project.build.randomization.design.list = Randomization List
menu.projects.project.build.randomization.design.attribute = Randomization List Properties
menu.projects.project.build.randomization.design.block = Randomization ID Block
menu.projects.project.build.randomization.design.factor-in = Stratification Factor
menu.projects.project.build.randomization.form = Form Configuration
menu.projects.project.build.drug = Treatment Design
menu.projects.project.build.drug.visit = Visit Management
menu.projects.project.build.drug.visit.setting = Setting
menu.projects.project.build.drug.config = Treatment Design
menu.projects.project.build.drug.config.setting = Setting
menu.projects.project.build.drug.list = IP list
menu.projects.project.build.drug.no_number = Unnumbered IP
menu.projects.project.build.drug.batch = Batch Management
menu.projects.project.build.drug.barcode = Barcode List
menu.projects.project.build.drug.barcode_label = Label Management
menu.projects.project.build.plan = Supply Plan
menu.projects.project.build.plan.config = Supply Plan Management
menu.projects.project.build.history = Project Log
menu.projects.project.build.push = Push Statistics
menu.projects.project.settings = Other Settings
menu.projects.project.settings.notice = Notification Settings
menu.projects.project.settings.user = User Management
menu.projects.project.settings.export = Configuration Export
menu.projects.project.settings.role = Permissions
menu.projects.project.settings.config = Project Configuration
menu.projects.project.monitor = Dynamic Monitoring
menu.projects.project.info = Project Information
operation.projects.project.info.view = View
menu.projects.project.basic.information = Basic Information
operation.projects.project.basic.information.view = View
menu.projects.project.basic.environment = Project Environment
operation.projects.project.basic.environment.view = View
menu.projects.project.business.functions = Business Functions
operation.projects.project.business.functions.view = View
menu.projects.project.external.docking = External Docking
operation.projects.project.external.docking.view = View
menu.projects.project.custom.process = Custom Process
operation.projects.project.custom.process.view = View
menu.projects.project.permissions = Project Permissions
operation.projects.project.permissions.view = View
menu.projects.notice.permissions = Project Notification
operation.projects.notice.permissions.view = View
menu.projects.project.multiLanguage = Multilingual
operation.projects.project.multiLanguage.view = View
operation.projects.project.multiLanguage.add = Add
operation.projects.project.multiLanguage.edit = Edit
operation.projects.project.multiLanguage.delete = Delete
operation.projects.project.multiLanguage.trail = Trail
menu.projects.project.multiLanguage.details = Language Details
operation.projects.project.multiLanguage.details.view = View
operation.projects.project.multiLanguage.details.edit = Edit
operation.projects.project.multiLanguage.details.preview = Preview
operation.projects.project.multiLanguage.details.downloadTemplate = Download Template
operation.projects.project.multiLanguage.details.batchExport = Export
menu.projects.project.storehouse.no_number.trail = Trail
menu.projects.project.storehouse.sku.trail = Trail
menu.projects.project.drug.sku.trail = Trail
menu.projects.project.site.sku.trail = Trail
menu.projects.project.site.no_number.trail = Trail
menu.projects.project.supply.shipment.trail = Trail
menu.projects.project.recovery.trail = Trail
menu.projects.project.freeze.trail = Trail
menu.projects.project.randomization.list.trail = Trail
menu.projects.project.medicine.upload.trail = Trail
menu.projects.project.supply.order.trail = Trail
menu.report = Report
menu.report.randomizationStatisticsExport = Randomazation Statistics Report
menu.report.subjectStatisticsExport = Subject Statistics Report
menu.report.ProjectNotificationsConfigurationReportExport = Project Notifications Configuration Report
menu.report.forecastingPrediction = Forecasting Prediction Report
menu.report.visitForecast = Visit Statistics Report
menu.report.siteIPStatisticsExport = Site IP Statistics Report
menu.report.depotIPStatisticsExport = Depot IP Statistics Report
menu.report.userLoginHistory = User Login History
menu.report.userRoleAssignHistory = User Role Assign History
menu.report.userRoleStatus = User Role Status Report
menu.report.auditTrailExport = Audit Trail Report
menu.report.auditTrailExport.build = Project Design
menu.report.auditTrailExport.settings = Project Settings
menu.report.auditTrailExport.release-record = Quarantine Management
menu.report.auditTrailExport.order = Shipments
menu.report.auditTrailExport.drug_recovery = Return Shipments
menu.report.auditTrailExport.subject = Subject
menu.report.auditTrailExport.dispensing = Subject dispensation
menu.report.auditTrailExport.ip = IP
menu.report.auditTrailExport.userLoginHistory = user Login History
menu.report.auditTrailExport.userRoleAssignHistory = user Role Assign History
menu.report.projectPermissionConfigurationExport = Project Permission Configuration Report
menu.report.configureReport = Configuration Report
menu.report.sourceIPExport = Source IP List Report
menu.report.sourceRandomizationListExport = Source Randomization List
menu.report.randomizationSimulationReport = Randomization Simulation Report
menu.report.randomizeReport = Subject Detail Report
menu.report.dispenseReport = Dispense Report
menu.report.unblindingReport = Unblinding Report
menu.report.IPUnblindingReport = IP Unblinding Report
menu.report.roomNumberViewHistory = Room Number View History
menu.report.depotItemReport = Depot Item Report
menu.report.siteItemReport = Site Item Report
menu.report.sourceIpUploadHistory = Source IP Upload History
menu.report.shipmentOrdersReport = Shipments Report
menu.report.returnOrdersReport = Return Shipments Report
menu.report.randomizationSimulationResult = Randomization Simulation Result
menu.report.RandomizationSimulationPDFExport = Randomization Simulation Report
operation.settings.roles.view = View
operation.settings.roles.add = Add
operation.settings.roles.edit = Edit
operation.settings.roles.config = Set Permissions
operation.settings.roles.export = export
operation.settings.users.view = View
operation.settings.users.add = Add
operation.settings.users.setting = Admin
operation.settings.users.edit = Edit
operation.settings.users.close = Close
operation.settings.users.invite-again = Invite Again
operation.settings.users.cancel = Cancel
operation.settings.users.export = Export
operation.settings.storehouse.view = View
operation.settings.storehouse.add = Add
operation.settings.storehouse.edit = Edit
operation.settings.storehouse.delete = Delete
operation.projects.main.view = View
operation.projects.main.create = Create Project
operation.projects.main.config.view = View
operation.projects.main.config.create = Add Environment
operation.projects.main.config.copy = Copy Environment
operation.projects.main.config.edit_env = Edit Environment
operation.projects.main.config.unlock = Unlock
operation.projects.main.config.lock = Lock
operation.projects.main.config.add = Add
operation.projects.main.config.edit = Edit
operation.projects.main.config.delete = Delete
operation.projects.main.config.copy_cohort = Copy
operation.projects.main.setting.view = View
operation.projects.main.setting.base.view = View
operation.projects.main.setting.base.edit = Edit
operation.projects.main.setting.base.modify = Modify
operation.projects.main.setting.function.view = View
operation.projects.main.setting.function.edit = Edit
operation.projects.main.setting.function.admin = Admin Enable/Disable
operation.projects.main.setting.docking.view = View
operation.projects.main.setting.docking.edit = Edit
operation.projects.main.setting.custom.view = View
operation.projects.main.setting.custom.edit = Edit
operation.projects.main.setting.permission.view = View
operation.projects.main.setting.permission.add = Add
operation.projects.main.setting.permission.edit = Edit
operation.projects.main.setting.permission.setting = Permission Setting
operation.projects.main.setting.notice.view = View
operation.projects.main.setting.notice.add = Add
operation.projects.main.setting.notice.delete = Delete
operation.projects.main.setting.notice.edit = Edit
operation.projects.home.view = View
operation.project.status.view = View
operation.project.task.view = View
operation.project.analysis.view = View
operation.project.dynamics.view = View
operation.project.site.IPStatistics.view = View
operation.project.site.IPStatistics.download = Download
operation.project.depot.IPStatistics.view = View
operation.project.depot.IPStatistics.download = Download
operation.project.subject.view = View
operation.project.subject.download = Download
operation.project.random.view = View
operation.project.random.download = Download
operation.subject.view-list = View
operation.subject.random = Randomize
operation.subject.replace = Replace Subject
operation.subject.trail = View
operation.subject.unblinding-pv = PV Unblinding
operation.subject.medicine.view-dispensing = View Subject Information
operation.subject.medicine.transport = Transport
operation.subject.medicine.trail = View
operation.subject.medicine.trail.print = Print
operation.subject.cohort.status = Status Modify
operation.subject.medicine.dispensing = Dispense
operation.subject.medicine.reissue = Re-dispense
operation.subject.medicine.replace = Replace IP
operation.subject.medicine.resume = Recover Dispensation
operation.subject.unblinding = View
operation.subject.unblinding-application = Emergency Unblinding application
operation.subject.unblinding-approval = Emergency Unblinding approval
operation.subject.unblinding-log = View
operation.subject.unblinding-sms = Send SMS
operation.subject.unblinding-print = Print
operation.subject.unblinding-pv-view = View
operation.subject.unblinding-pv-application = Unblinding（PV）Application
operation.subject.unblinding-pv-approval = Unblinding（PV）Approval
operation.subject.unblinding-pv-log = View
operation.subject.unblinding-pv-sms = Send SMS
operation.subject.unblinding-pv-print = Print
operation.subject.unblinding-ip-view = View
operation.subject.unblinding-ip-application = Unblinding（IP）Application
operation.subject.unblinding-ip-approval = Unblinding（IP）Approval
operation.subject.unblinding-ip-log = View
operation.subject.unblinding-ip-sms = Send SMS
operation.subject.unblinding-ip-print = Print
operation.subject.registered = Register
operation.subject.switch.cohort = Switch Cohort
operation.subject.update = Edit Subject
operation.subject.delete = Delete Subject
operation.subject.medicine.room = View Room Number
operation.subject.medicine.retrieval = Retrieve
operation.subject.medicine.out-visit-dispensing = Unscheduled Dispense
operation.subject.medicine.invalid = Do Not Attend the Visit
operation.subject.medicine.room-download = Download Room Number Viewing Record
operation.subject.medicine.register = Register Actually Dispensed IP
operation.subject.medicine.formula.update = Modify
operation.subject.medicine.planTime = Enrollment Time
operation.subject.medicine.setUp = Setting
operation.subject.secede = Stop (Randomize)
operation.subject.secede-registered = Stop (Register/Screening successful)
operation.subject.print = Print
operation.subject-dtp.view-list = View
operation.subject-dtp.random = Randomize Subject
operation.subject-dtp.replace = Replace Subject
operation.subject-dtp.trail = Subject Trail
operation.subject-dtp.unblinding-pv = PV Unblinding
operation.subject-dtp.medicine.view-dispensing = Dispensation Application Details
operation.subject-dtp.medicine.trail = Dispensation Trail
operation.subject-dtp.medicine.dispensing = Apply
operation.subject-dtp.medicine.transport = Transport
operation.subject-dtp.medicine.reissue = Re-dispensation Application
operation.subject-dtp.medicine.replace = Replace IP
operation.subject-dtp.unblinding = View
operation.subject-dtp.unblinding-application = Emergency Unblinding application
operation.subject-dtp.unblinding-approval = Emergency Unblinding approval
operation.subject-dtp.unblinding-log = View
operation.subject-dtp.unblinding-sms = Send SMS
operation.subject-dtp.registered = Register
operation.subject-dtp.update = Edit Subject
operation.subject-dtp.delete = Delete Subject
operation.subject-dtp.medicine.room = View Room Number
operation.subject-dtp.medicine.out-visit-dispensing = Unscheduled Dispensation Application
operation.subject-dtp.medicine.invalid = Do Not Attend the Visit
operation.subject-dtp.medicine.export-room = Download Dispensation Report(including room number)
operation.subject-dtp.medicine.room-download = Download Room Number Viewing Record
operation.subject-dtp.medicine.register = Register Actually Dispensed IP
operation.subject-dtp.secede = Stop (Randomize)
operation.subject-dtp.secede-registered = Stop (Register/Screening successful)
operation.subject-dtp.print = Print Subject Trail
operation.subject-dtp.medicine.print = Print Dispensation Trail
operation.supply.storehouse.medicine.summary = View
operation.supply.storehouse.medicine.singe = View
operation.supply.storehouse.medicine.use = Make Available
operation.supply.storehouse.medicine.freeze = Quarantine
operation.supply.storehouse.medicine.lost = Lost/Wasted
operation.supply.storehouse.medicine.history = View
operation.supply.storehouse.medicine.print = Print
operation.supply.storehouse.no_number.view = View
operation.supply.storehouse.no_number.freeze = Quarantine
operation.supply.storehouse.no_number.lost = Lost/Wasted
operation.supply.storehouse.no_number.history = View
operation.supply.storehouse.no_number.print = Print
operation.supply.site.medicine.summary = View
operation.supply.site.medicine.summary.formula = Inventory used time prediction
operation.supply.site.medicine.singe = View
operation.supply.site.medicine.use = Make Available
operation.supply.site.medicine.freeze = Quarantine
operation.supply.site.medicine.lost = Lost/Wasted
operation.supply.site.medicine.history = View
operation.supply.site.medicine.print = Print
operation.supply.site.no_number.view = View
operation.supply.site.no_number.freeze = Quarantine
operation.supply.site.no_number.lost = Lost/Wasted
operation.supply.site.no_number.history = View
operation.supply.site.no_number.print = Print
operation.supply.drug.order.list = View
operation.supply.drug.order.send = Deliver
operation.supply.drug.order.receive = Receive
operation.supply.drug.order.end = Terminate
operation.supply.drug.order.cancel = Cancel
operation.supply.drug.order.reason = Reason
operation.supply.drug.order.history = View
operation.supply.drug.order.print = Print
operation.supply.drug.order.confirm = Confirm
operation.supply.drug.order.close = Close
operation.supply.drug.single.sku = View
operation.supply.drug.single.history = View
operation.supply.drug.single.print = Print
operation.supply.drug.single.download = Download
operation.supply.drug.single.delete = Lost/Wasted
operation.supply.drug.single.use = Make Available
operation.supply.drug.no_number.view = View
operation.supply.shipment.create = Create
operation.supply.shipment.cancel = Common-Cancel
operation.supply.shipment.send = Deliver
operation.supply.shipment.lose = Lost
operation.supply.shipment.list = View
operation.supply.shipment.receive = Receive
operation.supply.shipment.alarm = Shipping Algorithm
operation.supply.shipment.history = View
operation.supply.shipment.Print = Print
operation.supply.shipment.confirm = Common-Confirm Shipment
operation.supply.shipment.reason = Reason
operation.supply.shipment.approval = Site Shipment Application Approval
menu.projects.project.supply.shipment.detail = IP Detail
operation.supply.shipment.detail.change = Replace
operation.supply.shipment.detail.changeRecord = Replacement Records
operation.supply.shipment.detail.edit = Edit
menu.projects.project.supply.recovery.detail = IP Detail
operation.supply.recovery.detail.change = Replace
operation.supply.recovery.detail.changeRecord = Replacement Records
operation.supply.shipment.close = Common-Close
operation.supply.shipment.terminated = Common-Terminate
operation.supply.shipment.approval.view = View
operation.supply.shipment.detail.view = Detail
operation.supply.shipment.confirm-dtp = DTP-Confirm
operation.supply.shipment.cancel-dtp = DTP-Cancel
operation.supply.shipment.close-dtp = DTP-Close
operation.supply.shipment.terminated-dtp = DTP-Terminated
operation.supply.drug_recovery.detail.view = Detail
operation.supply.shipment.contacts = Contacts
operation.supply.shipment.approval.print = Print
operation.supply.shipment.logistics.view = View
operation.supply.shipment.logistics.edit = Edit
operation.supply.drug_recovery.logistics.view = View
operation.supply.drug_recovery.logistics.edit = Edit
operation.supply.recovery.list = View
operation.supply.recovery.add = Add
operation.supply.recovery.receive = Receive
operation.supply.recovery.confirm = Deliver
operation.supply.recovery.cancel = Cancel
operation.supply.recovery.lose = Lost
operation.supply.recovery.determine = Confirm
operation.supply.recovery.close = Close
operation.supply.recovery.end = Terminate
operation.supply.recovery.history = View
operation.supply.recovery.print = Print
operation.supply.recovery.reason = Reason
operation.supply.recovery.detail.view = Detail
operation.supply.freeze.list = View
operation.supply.freeze.release = Lift
operation.supply.freeze.delete = Lost/Wasted
operation.supply.freeze.history = View
operation.supply.freeze.print = Print
operation.supply.freeze.approval = Lifting Quarantine Approval
operation.build.storehouse.add = Add
operation.build.storehouse.delete = Delete
operation.build.storehouse.edit = Edit
operation.build.storehouse.notice = Expiration Reminder
operation.build.storehouse.view = View
operation.build.storehouse.alarm = Inventory Alert
operation.build.site.view = View
operation.build.site.edit = Edit
operation.build.site.add = Add
operation.build.site.dispensing = Quantity of Initial Shipment
operation.build.site.supply-plan.view = View
operation.build.site.supply-plan.edit = Edit
operation.build.attribute.view = View
operation.build.attribute.edit = Edit
operation.build.attribute.history = View Trail
operation.build.code-rule.view = View
operation.build.code-rule.edit = Edit
operation.build.simulate-random.view = View
operation.build.simulate-random.edit = Edit
operation.build.simulate-random.add = Add
operation.build.simulate-random.run = Run
operation.build.simulate-random.site = Overview
operation.build.simulate-random.factor = Detail
operation.build.randomization.type.view = View
operation.build.randomization.type.edit = Edit
operation.build.randomization.group.add = Add
operation.build.randomization.group.inactivating = Synchronize
operation.build.randomization.group.delete = Delete
operation.build.randomization.group.edit = Edit
operation.build.randomization.group.view = View
operation.build.randomization.factor.add = Add
operation.build.randomization.factor.view = View
operation.build.randomization.factor.delete = Delete
operation.build.randomization.factor.edit = Edit
operation.build.randomization.factor.set-toplimit = Set Stratification Factor
operation.build.randomization.list.view-summary = View
operation.build.randomization.list.upload = Upload
operation.build.randomization.list.generate = Generate
operation.build.randomization.list.sync = Synchronize
operation.build.randomization.list.active = Enable/Disable Randomization List
operation.build.randomization.list.invalid = Invalidate
operation.build.randomization.list.edit = Edit
operation.build.randomization.list.segmentation.view = View
operation.build.randomization.list.segmentation.clear = Clear Other Stratification factors
operation.build.randomization.list.segmentation.site = Assign Block to Site
operation.build.randomization.list.segmentation.region = Assign Block to Region
operation.build.randomization.list.segmentation.country = Assign Block to Country
operation.build.randomization.list.segmentation.factor = Assign Block to Stratification Factor
operation.build.randomization.list.segmentation.activate = Active
operation.build.randomization.list.segmentation.deactivate = Inactive
operation.build.randomization.list.history = View
operation.build.randomization.list.print = Print
operation.build.randomization.list.attribute = View
operation.build.randomization.factor-in.view = View
operation.build.randomization.factor-in.add = Add
operation.build.randomization.factor-in.delete = Delete
operation.build.randomization.factor-in.set-people = Set Number
operation.build.randomization.form.add = Add
operation.build.randomization.form.delete = Delete
operation.build.randomization.form.edit = Edit
operation.build.randomization.form.list = View
operation.build.randomization.form.preview = Preview
operation.build.medicine.visit.update = Modify
operation.build.medicine.visit.drag = Sort
operation.build.medicine.visit.copy = Copy Add
operation.build.medicine.visit.push = Push
operation.build.medicine.visit.push.record = Release Record
operation.build.medicine.visit.add = Add
operation.build.medicine.visit.delete = Delete
operation.build.medicine.visit.edit = Edit
operation.build.medicine.visit.list = View
operation.build.medicine.visit.setting.edit = Edit
operation.build.medicine.visit.setting.list = View
operation.build.medicine.configuration.add = Add
operation.build.medicine.configuration.delete = Delete
operation.build.medicine.configuration.edit = Edit
operation.build.medicine.configuration.list = View
operation.build.medicine.configuration.setting.add = Add
operation.build.medicine.configuration.setting.delete = Delete
operation.build.medicine.configuration.setting.edit = Edit
operation.build.medicine.configuration.setting.list = View
operation.build.medicine.upload.list = View
operation.build.medicine.upload.upload = Upload IP
operation.build.medicine.packlist.upload = Upload Packlist
operation.build.medicine.package.setting = Setting
operation.build.medicine.batch.setting = Setting
operation.build.medicine.examine = Approval
operation.build.medicine.update = Modify
operation.build.medicine.release = Release
operation.build.medicine.upload.delete = Delete
operation.build.medicine.upload.uploadHistory = View
operation.build.medicine.upload.print = Print
operation.build.medicine.otherm.add = Add
operation.build.medicine.otherm.delete = Delete
operation.build.medicine.otherm.edit = Edit
operation.build.medicine.otherm.list = View
operation.build.medicine.batch.list = View
operation.build.medicine.batch.update = Update
operation.build.medicine.batch.edit = Edit
operation.build.medicine.barcode.view = View
operation.build.medicine.barcode.add = Generate Barcode
operation.build.medicine.barcode.scan = Scan for Warehousing
operation.build.medicine.barcode.scanPackage = Package Scanning
operation.build.medicine.barcode.export = Export
operation.build.medicine.barcode_label.view = View
operation.build.medicine.barcode_label.add = Add
operation.build.medicine.barcode_label.preview = Preview
operation.build.medicine.barcode_label.send = Send
operation.build.supply-plan.add = Add
operation.build.supply-plan.delete = Delete
operation.build.supply-plan.edit = Edit
operation.build.supply-plan.view = View
operation.build.supply-plan.history = Trail
operation.build.supply-plan.medicine.add = Add
operation.build.supply-plan.medicine.delete = Delete
operation.build.supply-plan.medicine.edit = Edit
operation.build.supply-plan.medicine.view = View
operation.build.supply-plan.medicine.history = Trail
operation.build.history.view = View
operation.build.history.print = Print
operation.build.push.view = View
operation.build.push.all.send = All
operation.build.push.batch.send = Batch Send
operation.build.push.history = Historical Data Push
operation.build.push.send = Resend
operation.build.push.details = Details
operation.build.settings.notice.view = View
operation.build.settings.notice.edit = Edit
operation.build.settings.user.add = Add
operation.build.settings.user.role = Add Roles
operation.build.settings.user.site = Add Sites
operation.build.settings.user.depot = Add Depots
operation.build.settings.user.app = APP Account Enable/Disable
operation.build.settings.user.view = View
; operation.build.settings.user.edit = Manage Operations
operation.build.settings.user.unbind = Unbind
operation.build.settings.user.history = Trail
operation.build.settings.user.print = Print
operation.build.settings.users.invite-again = Invite Again
operation.build.settings.user.reauthorization = Reauthorization
operation.build.randomization.info.view = View
operation.monitor.view = View
operation.monitor.edit = Management
operation.report.userRoleStatus.download = Download
operation.build.settings.user.download = Download
operation.report.userRoleAssignHistory.download = Download
operation.build.randomization.info.export = Download
operation.report.auditTrailExport.download = Download
operation.report.auditTrailExport.build = Project Design
operation.report.auditTrailExport.settings = Project Settings
operation.report.auditTrailExport.release-record = Quarantine Management
operation.report.auditTrailExport.order = Shipments
operation.report.auditTrailExport.drug_recovery = Return Shipments
operation.report.auditTrailExport.subject = Subject
operation.report.auditTrailExport.dispensing = Subject dispensation
operation.report.auditTrailExport.ip = IP
operation.report.auditTrailExport.userLoginHistory = Download
operation.report.auditTrailExport.userRoleAssignHistory = Download
operation.report.siteIPStatisticsExport.download = Download
operation.report.siteIPStatisticsExport.download.template = Custom Template
operation.report.randomizationStatisticsExport.download = Download
operation.report.subjectStatisticsExport.download = Download
operation.report.forecastingPrediction.download = Download
operation.report.visitForecast.download = Download
operation.report.visitForecast.download.template = Custom Template
operation.build.projectNotificationsConfigurationReport.download = Download
operation.report.depotIPStatisticsExport.download = Download
operation.report.depotIPStatisticsExport.download.template = Custom Template
operation.report.userLoginHistory.download = Download
operation.projects.main.setting.permission.export = Download
operation.subject.download-random = Download
operation.subject.download-random.template = Custom Template
operation.subject.medicine.export = Download
operation.subject.medicine.export.template = Custom Template
operation.subject.download = Download
operation.subject.download.template = Custom Template
operation.report.ip.unblinding.download = IP Unblinding Report Download
operation.report.ip.unblinding.download.template = IP Unblinding Report Custom Template
operation.subject-dtp.download = Download Unblinding Report
operation.subject-dtp.medicine.export = Download Dispensation Report
operation.subject-dtp.download-random = Download Randomization Report
operation.supply.storehouse.medicine.download = Download
operation.supply.storehouse.medicine.download.template = Custom Template
operation.build.randomization.list.export = Download
operation.build.medicine.upload.downdata = Download
operation.build.medicine.upload.downdata.template = Custom Template
operation.source.ip.upload.history.downdata = Download
operation.supply.site.medicine.download = Download
operation.supply.site.medicine.download.template = Custom Template
operation.build.simulate-random.download = Download
operation.supply.recovery.download = Download
operation.supply.recovery.download.template = Custom Template
operation.supply.shipment.download = Download
operation.supply.shipment.download.template = Custom Template
operation.build.simulate-random.pdf.download = Download

[export]
report.projectEnv = Project/Environment
report.latestDownloadTime = Latest Download Time
report.customTemplate = Custom Template
report.history = History
report.template = Template
report.template.name = Template Name
report.template.name.required = Please enter a template name
report.template.default = Refer to Default Template
report.template.name.default = Default Template
report.attributes = FieldLibrary
report.attributes.unit = Items
report.custom = Custom
report.filename = Filename
report.filesize = Filesize
report.exportTime = Export Time
report.filename.search.required = Please enter the file name
report.storehouse = Storehouse
report.randomizationStatisticsExport = Randomization Statistics Report
report.subjectStatisticsExport = Subject Statistics Report
report.siteIPStatisticsExport = Site IP Statistics Report
report.depotIPStatisticsExport = Depot IP Statistics Report
report.userLoginHistory = User Login History
report.userRoleAssignHistory = User Role Assign History
report.userRoleStatus = User Role Status Report
report.auditTrailExport = Audit Trail
report.projectPermissionConfigurationExport = Project Permission Configuration Report
report.configureReport = Configuration Report
report.sourceIPExport = Source IP List Report
report.sourceRandomizationListExport = Source Randomization List Report
report.randomizationSimulationReport = Randomization SimulationReport
report.randomizeReport = Subject Detail Report
report.dispenseReport = Dispense Report
report.unblindingReport = Unblinding Report
report.roomNumberViewHistory = RoomNumber View History
report.depotItemReport = Depot ItemReport
report.siteItemReport = Site ItemReport
report.shipmentOrdersReport = Shipments Report
report.returnOrdersReport = Return Shipments Report
report.randomizationSimulationResult = Randomization Simulation Result
report.site.warning = No Site Selected
report.template.all = All Templates
report.template.delete.confirm = Confirm Delete?
report.attributes.project = Project
report.attributes.project.number = Project Number
report.attributes.project.name = Project Name
report.attributes.info = Basic Information
report.attributes.info.user.name = Name
report.attributes.info.user.email = Email
report.attributes.info.user.role = Role
report.attributes.info.country = Country (Stratification Property)
report.attributes.info.region = Region (Stratification Property)
report.attributes.info.site.country = Country
report.attributes.info.site.region = Region
report.attributes.info.site.number = Site Number
report.attributes.info.site.name = Site Name
report.attributes.info.subject.number = Subject ID
report.attributes.info.status = Status
report.attributes.info.storehouse.name = Depots
report.attributes.random = Subject Randomization
report.attributes.random.factor = Stratification Factor
report.attributes.random.factor.calc = Enter Stratification Field (Page)
report.attributes.random.actual.factor = Actual stratification
report.attributes.random.time = Randomization/Enrolling Time
report.attributes.random.group = Group
report.attributes.random.sub.group = Sub Group
report.attributes.random.subject.number.replace = Replace subject ID
report.attributes.random.subject.number.replace.substitute = Replace
report.attributes.random.number = Randomization Number
report.attributes.random.register.time = Register Time
report.attributes.random.register.operator = Registered Operator
report.attributes.random.operator = Randomization Operator
report.attributes.random.form = Form
report.attributes.random.plan.number = Planned Randomization Quantity
report.attributes.random.block = Block
report.attributes.random.subject.replace.status = Replace Subject Status
report.attributes.random.cohort = Cohort Name
report.attributes.random.stage = Stage
report.attributes.random.subject.replace.time = Replace Time
report.attributes.random.subject.replace.number = Replace randomization number
report.attributes.random.config.code = Group Code
report.attributes.random.sign.out.operator = Stop Operator
report.attributes.random.sign.out.time = Stop Operate Time
report.attributes.random.sign.out.real.time = Actual Stopped Date
report.attributes.random.sign.out.reason = Reason for Stop
report.attributes.random.screen.time = Screening Date
report.attributes.random.icf.time = ICF Signed Date
report.attributes.random.finish.remark = Complete study-remark
report.attributes.random.plan.time = Scheduled Randomization/Enrollment Time
report.attributes.random.sequence.number = Random Sequence Number
report.attributes.dispensing = Subject Dispensation
report.attributes.dispensing.room = Room Number
report.attributes.dispensing.cycle.name = Visit Name
report.attributes.dispensing.type = Dispensation Type
report.attributes.dispensing.planTime = Scheduled Visit Time
report.attributes.dispensing.dose = Dispensation Level
report.attributes.dispensing.outsize = Is it overdue
report.attributes.dispensing.doseFormulas = Dose Adjustment Form
report.attributes.dispensing.time = Operation Time
report.attributes.dispensing.medicine = IP Number
report.attributes.dispensing.drug.name = IP Name
report.attributes.dispensing.label = Dispensation IP label
report.attributes.dispensing.medicine.replace = Replaced IP number
report.attributes.dispensing.medicine.real = Actual Use of IP
report.attributes.dispensing.medicine.real.group = Actually used IP group
report.attributes.dispensing.medicine.is.replace = Is Replaced IP number
report.attributes.dispensing.medicine.is.real = Is Real IP number
report.attributes.dispensing.medicine.real.number = Actually Used IP Number
report.attributes.dispensing.drug.other.number = Quantity of Unnumbered IP
report.attributes.dispensing.useFormulas = Formula Calculation Form
report.attributes.dispensing.operator = Dispensation operator
report.attributes.dispensing.remark = Dispensing Remark
report.attributes.dispensing.out-visit-dispensing.reason = Unscheduled Dispensation Reason
report.attributes.dispensing.reissue.reason = Re-dispensation reason
report.attributes.dispensing.reissue.remark = Re-Dispense-remark
report.attributes.dispensing.out-visit-dispensing.remark = Unscheduled dispense-remark
report.attributes.dispensing.replace.remark = Replace IP-remark
report.attributes.dispensing.retrieval.remark = Retrieve-remark
report.attributes.dispensing.register.remark = Actually Dispensed IP-remark
report.attributes.dispensing.invalid.remark = Do Not Attend the Visit-remark
report.attributes.dispensing.send.type = Dispensation Method
report.attributes.dispensing.logistics.info = Logistics
report.attributes.dispensing.logistics.remark = Logistics-Remark
report.attributes.dispensing.sheet.actual = Table of variances between dispensation and actual used
report.attributes.unblinding = Unblinding
report.attributes.unblinding.sponsor = Whether the Sponsor has been notified or not.
report.attributes.unblinding.mark = Unblinding Remark
report.attributes.unblinding.reason = Unblinding Reason
report.attributes.unblinding.reason.mark = Unblinding Reason Remark
report.attributes.unblinding.operator = Unblinding Operator
report.attributes.unblinding.time = UnblindingTime
report.attributes.research = IP
report.attributes.research.medicine.serial-number = Sequence Number
report.attributes.research.medicine.number = IP Number
report.attributes.research.medicine.name = IP Name
report.attributes.research.batch = Batch Number
report.attributes.research.expireDate = Expiration date
report.attributes.research.spec = Specification
report.attributes.research.packageNumber = Package Number
report.attributes.research.package.serialNumber = Package Sequence Number
report.attributes.research.packageMethod = Deliver Mode
report.attributes.research.place = Location
report.attributes.research.order.number = Shipment Number
report.attributes.research.status = Status
report.attributes.research.reason = Operation Reason
report.attributes.research.operator = Operator
report.attributes.research.time = Time
report.attributes.research.freeze.reason = Freeze Reason
report.attributes.research.freeze.operator = Quarantine Operator
report.attributes.research.release.reason = Lifting Quarantine Reason
report.attributes.research.release.operator = Lifting Quarantine Operator
report.attributes.research.lost.reason = Lost/Wasted Reason
report.attributes.research.lost.operator = Lost/VoidOperator
report.attributes.research.lost.time = Lost/Wasted Operation Time
report.attributes.research.use.reason = Setting as Available Reason
report.attributes.research.use.operator = Setting as Available Operator
report.attributes.research.use.time = Setting as Available Time
report.attributes.research.other = Number
report.attributes.order = Shipments
report.attributes.order.detail = Shipments Details
report.attributes.order.number = Shipment Number
report.attributes.order.status = Status
report.attributes.order.send = Origin
report.attributes.order.receive = Destination
report.attributes.order.medicineQuantity = IP Quantity
report.attributes.order.create.by = Created By
report.attributes.order.create.time = Created Time
report.attributes.order.cancel.by = Canceller
report.attributes.order.cancel.time = Cancellation Time
report.attributes.order.cancel.reason = Reason for cancellation
report.attributes.order.confirm.by = Confirmed By
report.attributes.order.confirm.time = Confirm Time
report.attributes.order.close.by = Closed By
report.attributes.order.close.time = Closed Time
report.attributes.order.close.reason = Reason for Closure
report.attributes.order.send.by = Delivered By
report.attributes.order.send.time = Delivery time
report.attributes.order.receive.by = Receiver
report.attributes.order.receive.time = Received Time
report.attributes.order.lost.by = Lost By
report.attributes.order.lost.time = Lost Time
report.attributes.order.lost.reason = Reason for Loss
report.attributes.order.end.by = Termination By
report.attributes.order.end.time = Termination Time
report.attributes.order.end.reason = Termination Reason
report.attributes.order.supplier = Logistics Vendor
report.attributes.order.supplier.other = Other Vendor
report.attributes.order.supplier.number = Tracking Number
report.attributes.order.expectedArrivalTime = Expected Arrival Time
report.attributes.order.actualReceiptTime = Actual Receipt Time
report.ip.statistics.status.available = Available
report.ip.statistics.status.toBeConfirmed = To be Confirmed
report.ip.statistics.status.delivered = Confirmed
report.ip.statistics.status.sending = In Delivery
report.ip.statistics.status.quarantine = Quarantined
report.ip.statistics.status.used = Used
report.ip.statistics.status.lose = Lost/Wasted
report.ip.statistics.status.expired = Expired
report.ip.statistics.status.frozen = Frozen
report.ip.statistics.status.locked = Locked
report.user.login.time = Operation time
report.user.login.success = Whether the login is successful
report.forecast.depot = Depot Name
report.forecast.period = Scheduled Dispensation Window
report.forecast.date = Prediction Time
report.simulate.random.name = Name
report.simulate.random.block = Block
report.simulate.random.group = Group
report.simulate.random.subGroup = Sub Group
report.user.role.assign.name = Name
report.user.role.assign.email = Email
report.user.role.assign.operType = Operation
report.user.role.assign.content = Operation Content
report.user.role.assign.oper = Operator
report.user.role.assign.operTime = Operation Time
report.audit.trail.operation.name = Operation Name
report.audit.trail.operation.way = Operation Way
report.project.env = Project Environment
report.random.list = Source Randomization List
report.random.list.attribute = Randomization List Property
report.random.list.name = Randomization List Name
report.random.list.all.site = All Site
report.random.list.applicable.site = Applicable Site
report.random.list.status = Randomization List Status
report.random.list.status.open = Open
report.random.list.status.close = Close
report.random.list.status.invalidate = Invalidate
report.random.list.create.time = Create Time
report.random.list.create.by = Creator
report.random.list.block.rule = Block Rules
report.random.list.random.number.rule = Randomization ID Rule
report.random.list.block.rule.order = Order
report.random.list.block.rule.reverse = Disorder
report.random.list.random.number.count = Randomization ID Total Number
report.random.list.initial.number = Initial Number
report.random.list.end.number = Termination Number
report.random.list.number.length = Number Length
report.random.list.prefix = Number Prefix
report.random.list.seed = Randomization Seed
report.random.list.block.configuration = Block Configuration
report.random.list.block.length = Block Length
report.random.list.block.number = Block Quantity
report.random.list.group.ratio = Group Ratio
report.random.list.group.config = Group Configuration
report.random.list.factor.code = Factor Code
report.random.list.factor.name = Factor Name
report.random.list.factor.option = Option
report.random.list.factor.country = Country acts as a stratifying factor or not
report.random.list.factor.site = Site acts as a stratifying factor or not
report.random.list.factor.region = Region acts as a stratifying factor or not
report.random.list.factor.no = No
report.random.list.factor.yes = Yes
report.random.list.probability = Bias Probability
report.random.list.factor.ratio = Stratification Factor Weight Ratio
report.visit.forecast.visit.status = Visit Status
report.visit.forecast.notice.content = Notification content
report.visit.forecast.notice.time = Push Time
report.visit.forecast.notice.user = Pushed by
report.visit.forecast.notice.type = Push Mode
report.visit.forecast.notice.email = Notification targets
report.subject.visit.OutSizeNotCompleted = Overdue incompleted
report.subject.visit.InProgress = In progress
report.subject.visit.OutSizeCompleted = Out of window completed
report.subject.visit.Prepare = Not started
report.subject.visit.CompletedOnSchedule = Completed on time
report.subject.visit.app.notice = APP Notice
report.subject.visit.message.notice = Message Notice
simulate_random.name = Randomization Simulation
simulate_random.parameter = Randomization Simulation Parameter
simulate_random.parameter.setting.name = Setting Name:
simulate_random.parameter.site.quantity = Site Quantity:
simulate_random.parameter.number.runs = Number of Runs:
simulate_random.parameter.subject.quantity = Subject Number:
simulate_random.detail.meanStandard = Average ± Standard Deviation
simulate_random.layered.overview = Layer Overview
simulate_random.layered.overview.no = No.
simulate_random.layered.overview.layer = Layer
simulate_random.site.overview = Site Overview
simulate_random.site.overview.no = No.
simulate_random.site.overview.site = Site
simulate_random.site.details = Site Details
simulate_random.site.details.no = No.
simulate_random.site.details.site = Site
simulate_random.site.details.number.people = Number
simulate_random.site.details.unbalanced = Imbalance
simulate_random.site.details.total = Total
setting.user.list.No = No.
setting.user.list.email = Email
setting.user.list.fullName = Full Name
setting.user.list.telephone = TEL
setting.user.list.company = Company
setting.user.list.description = Description
setting.user.list.role = Role
setting.user.list.role.common = Common
setting.user.list.role.dtp = DTP
setting.roles.list.role = Role
setting.roles.list.menu = Menu
setting.roles.list.operation = Operation
export.notifications.configuration.report.type = Email Type
export.notifications.configuration.report.role = Role
export.notifications.configuration.report.content.configuration = Content Configuration
export.notifications.configuration.report.scene = Scene
export.notifications.configuration.report.excludeRecipientList = Excluded Recipients
projectSettings.basicInformation.projectProperties.projectType.basicStudy = Basic Study
projectSettings.basicInformation.projectProperties.projectType.cohortStudy = Cohort Study
projectSettings.basicInformation.projectProperties.projectType.reRandomizationStudy = Re-randomization Study
form.control.type.format.numberLength = Number Length
form.control.type.format.decimalLength = Decimal Length
calendar.button.site.visit.matter.notice.history.people.system = System
configuration.export.treatment.design.shipment.other.drug = * Is Unnumbered IP
form.control.type.options.one = Initial Dose
form.control.type.options.two = Maintain the dose from the last time
form.control.type.options.three = Dose reduction, choose to decrease the subject's current dose by one dose level
configureReport.basicInformation = Basic Information
configureReport.basicInformation.projectTimeZone = Project TimeZone
configureReport.basicInformation.projectType = Project Type
configureReport.basicInformation.projectType.basicStudy = Basic Study
configureReport.basicInformation.projectType.cohortStudy = Cohort Study
configureReport.basicInformation.projectType.reRandomizationStudy = Re-Randomization Study
configureReport.basicInformation.projectOrderCheck = Run Shipping Algorithm
configureReport.basicInformation.projectOrderCheck.timing = Fixed Time(including manual run)
configureReport.basicInformation.projectOrderCheck.realTime = Real Time
configureReport.basicInformation.projectOrderCheck.notApplicable = Not Applicable
configureReport.basicInformation.projectOrderConfirmation = Return Shipments Confirmation
configureReport.basicInformation.projectOrderConfirmation.open = Yes
configureReport.basicInformation.projectOrderConfirmation.close = No
configureReport.basicInformation.projectDeIsolationApproval = Lifting Quarantine Approval
configureReport.basicInformation.projectDeIsolationApproval.open = Yes
configureReport.basicInformation.projectDeIsolationApproval.close = No
configureReport.basicInformation.projectUnblindingControl = Unblinding Control
configureReport.basicInformation.projectUnblindingControl.false = No
configureReport.basicInformation.projectUnblindingControl.unblinding = Emergency Unblinding
configureReport.basicInformation.projectUnblindingControl.unblinding.sms = Approval Confirmation-SMS
configureReport.basicInformation.projectUnblindingControl.unblinding.process = Approval Confirmation-Process Operation
configureReport.basicInformation.projectUnblindingControl.unblinding.code = Unblinding Code
configureReport.basicInformation.projectUnblindingControl.pvUnblinding = PV Unblinding
configureReport.basicInformation.projectUnblindingControl.pvUnblinding.sms = Approval Confirmation-SMS
configureReport.basicInformation.projectUnblindingControl.pvUnblinding.process = Approval Confirmation-Process Operation
configureReport.basicInformation.projectUnblindingControl.IpUnblinding = IP Unblinding
configureReport.basicInformation.projectUnblindingControl.IpUnblinding.sms = Approval Confirmation-SMS
configureReport.basicInformation.projectUnblindingControl.IpUnblinding.process = Approval Confirmation-Process Operation
configureReport.basicInformation.projectOrderApprovalControl = Site Shipment Application
configureReport.basicInformation.projectOrderApprovalControl.sms = SMS
configureReport.basicInformation.projectOrderApprovalControl.process = Process Operation
configureReport.basicInformation.projectOrderApprovalControl.false = No
configureReport.basicInformation.projectNotice = Visit Notification
configureReport.basicInformation.projectNotice.open = Yes
configureReport.basicInformation.projectNotice.close = No
configureReport.basicInformation.projectConnectEdc = Sync EDC
configureReport.basicInformation.projectPushMode = Data Push Mode
configureReport.basicInformation.projectPushMode.real = Real-time request by EDC
configureReport.basicInformation.projectPushMode.active = Active push from IRT
configureReport.basicInformation.projectSynchronizationMode = Synchronization Mode
configureReport.basicInformation.projectSynchronizationMode.screen = Data synchronization when subject screening
configureReport.basicInformation.projectSynchronizationMode.random = Data synchronization when subjects randomizing
configureReport.cohort = Cohort Name
configureReport.stage = Stage
configureReport.generalSituation = General Situation
configureReport.allOfThem = All of them
configureReport.name = Name
configureReport.projectDetails = Project Details
configureReport.projectDetails.attributeConfigure = Properties
configureReport.projectDetails.attributeConfigure.systemConfiguration = System Configuration
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize = Randomize
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.random = Randomize
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.non-randomized = non-randomized
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID = Display Randomization ID
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.show = Display
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.notShow = Do not display
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber = Random Sequence Number Display
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.show = Display
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.notShow = Do not display
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberPrefix = Random Sequence Number Prefix
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberDigit = Random Sequence Number Digits
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberStart = Random Sequence Number Start Number
configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign = Treatment Design
configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.yes = Dispense
configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.no = With no dispensation operation
configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule = DTP Rules
configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.ip = IP
configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.visitFlow = Visit Flow
configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.notApplicable = Not Applicable
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck = Randomization Supply Check
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule1 = All groups, can randomize after with sufficient inventory
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule2 = Allocated groups, can randomize after with sufficient inventory
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3 = Force random to available inventory group
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.least = At least
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.groups = groups need to be supplied
configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign = Blind Design
configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.blind = Blind
configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.open = Open
configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess = Subject Screening Process
configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.open = Yes
configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.close = No
configureReport.projectDetails.attributeConfigure.subjectIDRules = Subject ID Rules
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule = Subject number input rule
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule1 = Customize
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule2 = Auto-incrementing and unique within the project
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule3 = Auto-incrementing and unique within the site
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix = Subject Prefix
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.have = Have
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.nothing = Not Found
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDPrefix = Subject ID Prefix
configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextForSubjectID = Replacement text for Subject ID
configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextEnForSubjectID = Replacement text for Subject ID (English)
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDDigit = Subject ID Digit
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement = Subject Replacement
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.open = Yes
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.close = No
configureReport.projectDetails.attributeConfigure.subjectIDRules.takeCare = Note:If the subject number prefix is set to {siteNO}, the actual subject number is 09001, 09 is the site number, and 001 is the subject sequence number.
configureReport.projectDetails.attributeConfigure.otherRules = Other Rules
configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects = Stop Unblinded Subjects
configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.true = Open
configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.false = Close
configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule = Quarantined-IP Counting Rule
configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.true = Open
configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.false = Close
configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging = Shipped by Package
configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.true = Open
configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.false = Close
configureReport.projectDetails.attributeConfigure.otherRules.deactivate = 【Stop Unblinded Subjects】:After enabled, dispensation will not be allowed for emergency unblinded subjects.
configureReport.projectDetails.attributeConfigure.otherRules.quarantine = 【Quarantined-IP Counting Rule】:After enabled, when running shipping algorithm, the quarantined items will be counted as available inventory.
configureReport.projectDetails.attributeConfigure.otherRules.packing = 【Shipped by Package】:Once enabled, shipments will be created by package.
configureReport.projectDetails.randomConfigure = Randomization Design
configureReport.projectDetails.randomConfigure.randomDesign = Randomization Design
configureReport.projectDetails.randomConfigure.randomDesign.randomType = Randomization Type
configureReport.projectDetails.randomConfigure.randomDesign.randomType.region = Block Randomization
configureReport.projectDetails.randomConfigure.randomDesign.randomType.min = Minimized Randomization
configureReport.projectDetails.randomConfigure.randomDesign.group = Group Name
configureReport.projectDetails.randomConfigure.randomDesign.regionFactor = Set region as stratification factor
configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.country = Country
configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.site = Site
configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.region = Region
configureReport.projectDetails.randomConfigure.randomDesign.factorOption = Stratification Factor(Option)
configureReport.projectDetails.randomConfigure.randomDesign.factor = Stratification Factor
configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldNumber = Field Code
configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldName = Field Name
configureReport.projectDetails.randomConfigure.randomDesign.factor.variable = Variable Name
configureReport.projectDetails.randomConfigure.randomDesign.factor.controlType = Control Type
configureReport.projectDetails.randomConfigure.randomDesign.factor.option = Option
configureReport.projectDetails.randomConfigure.randomDesign.factor.status = Status
configureReport.projectDetails.randomConfigure.randomDesign.factor.status.valid = Valid
configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid = Invalid
configureReport.projectDetails.randomConfigure.randomDesign.form.page = Page
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation = Stratification Calculation
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.fieldNumber = Field Code
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType = Formula Type
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.bmi = BMI
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.age = Age
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.customFormula = Custom formula
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.retainDecimals = Keep Decimal Places
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredName = Stratification Factor Name
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredOption = Stratification Option Mapping
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status = Status
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.valid = Valid
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.invalid = Invalid
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi = Stratification Calculation — BMI
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.takeCare = Note:(1)BMI Calculation Formula:(Weight [kg] / (Height [m])².
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.fieldNumber = Field Code
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.formula = Calculation Formula
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.weightName = Enter Weight Field Name
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.heightName = Enter Height Field Name
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.controlType = Control Type
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.layeredName = Stratification Factor Name
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.layeredOption = Stratification Option Mapping
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status = Status
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status.valid = Valid
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status.invalid = Invalid
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age = Stratification Calculation — Age
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.takeCare = Note:(1)Age:Calculated according to 365.25 days/year.
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.fieldNumber = Field Code
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formula = Calculation Formula
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formula.age = Age
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.fieldName = Enter Field Name
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.retainDecimals = Keep Decimal Places
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.controlType = Control Type
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formatType = Format type
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.layeredName = Stratification Factor Name
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.layeredOption = Stratification Option Mapping
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status = Status
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status.valid = Valid
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status.invalid = Invalid
configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor = randomization stratification
configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual = Actual stratification
configureReport.projectDetails.formConfigure = Form Configuration
configureReport.projectDetails.formConfigure.subjectRegistration = Subject Registration
configureReport.projectDetails.formConfigure.subjectRegistration.fieldName = Field Name
configureReport.projectDetails.formConfigure.subjectRegistration.isEditable = Editable
configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.true = Yes
configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.false = No
configureReport.projectDetails.formConfigure.subjectRegistration.required = Required
configureReport.projectDetails.formConfigure.subjectRegistration.required.true = Yes
configureReport.projectDetails.formConfigure.subjectRegistration.required.false = No
configureReport.projectDetails.formConfigure.subjectRegistration.variableId = Variable ID
configureReport.projectDetails.formConfigure.subjectRegistration.controlType = Control Type
configureReport.projectDetails.formConfigure.subjectRegistration.option = Option
configureReport.projectDetails.formConfigure.subjectRegistration.formatType = Format type
configureReport.projectDetails.formConfigure.subjectRegistration.variableFormat = Variable Format
configureReport.projectDetails.formConfigure.subjectRegistration.variableRange = Variable Range
configureReport.projectDetails.formConfigure.subjectRegistration.status = Status
configureReport.projectDetails.formConfigure.subjectRegistration.status.valid = Valid
configureReport.projectDetails.formConfigure.subjectRegistration.status.invalid = Invalid
configureReport.projectDetails.formConfigure.subjectRegistration.currentTime = Current Time
configureReport.projectDetails.formConfigure.customFormula = Custom formula
configureReport.projectDetails.formConfigure.customFormula.fieldName = Field Name
configureReport.projectDetails.formConfigure.customFormula.required = Required
configureReport.projectDetails.formConfigure.customFormula.required.true = Yes
configureReport.projectDetails.formConfigure.customFormula.required.false = No
configureReport.projectDetails.formConfigure.customFormula.variableId = Variable ID
configureReport.projectDetails.formConfigure.customFormula.controlType = Control Type
configureReport.projectDetails.formConfigure.customFormula.formatType = Format type
configureReport.projectDetails.formConfigure.customFormula.variableFormat = Variable Format
configureReport.projectDetails.formConfigure.customFormula.variableRange = Variable Range
configureReport.projectDetails.formConfigure.customFormula.status = Status
configureReport.projectDetails.formConfigure.customFormula.status.valid = Valid
configureReport.projectDetails.formConfigure.customFormula.status.invalid = Invalid
configureReport.projectDetails.formConfigure.doseAdjustment = Dose Adjustment
configureReport.projectDetails.formConfigure.doseAdjustment.fieldName = Field Name
configureReport.projectDetails.formConfigure.doseAdjustment.required = Required
configureReport.projectDetails.formConfigure.doseAdjustment.required.true = Yes
configureReport.projectDetails.formConfigure.doseAdjustment.required.false = No
configureReport.projectDetails.formConfigure.doseAdjustment.variableId = Variable ID
configureReport.projectDetails.formConfigure.doseAdjustment.controlType = Control Type
configureReport.projectDetails.formConfigure.doseAdjustment.option = Option
configureReport.projectDetails.formConfigure.doseAdjustment.status = Status
configureReport.projectDetails.formConfigure.doseAdjustment.status.valid = Valid
configureReport.projectDetails.formConfigure.doseAdjustment.status.invalid = Invalid
configureReport.projectDetails.formConfigure.layeredCalculation = Stratification Calculation
configureReport.projectDetails.formConfigure.layeredCalculation.fieldName = Field Name
configureReport.projectDetails.formConfigure.layeredCalculation.isEditable = Editable
configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.true = Yes
configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.false = No
configureReport.projectDetails.formConfigure.layeredCalculation.required = Required
configureReport.projectDetails.formConfigure.layeredCalculation.required.true = Yes
configureReport.projectDetails.formConfigure.layeredCalculation.required.false = No
configureReport.projectDetails.formConfigure.layeredCalculation.variableId = Variable ID
configureReport.projectDetails.formConfigure.layeredCalculation.controlType = Control Type
configureReport.projectDetails.formConfigure.layeredCalculation.option = Option
configureReport.projectDetails.formConfigure.layeredCalculation.formatType = Format type
configureReport.projectDetails.formConfigure.layeredCalculation.variableFormat = Variable Format
configureReport.projectDetails.formConfigure.layeredCalculation.variableRange = Variable Range
configureReport.projectDetails.formConfigure.layeredCalculation.status = Status
configureReport.projectDetails.formConfigure.layeredCalculation.status.valid = Valid
configureReport.projectDetails.formConfigure.layeredCalculation.status.invalid = Invalid
configureReport.projectDetails.formConfigure.layeredCalculation.currentTime = Current Time
configureReport.projectDetails.ipManagement = Treatment Design
configureReport.projectDetails.ipManagement.visitManagement = Visit Management
configureReport.projectDetails.ipManagement.visitManagement.cycleVersion = Cycle Version
configureReport.projectDetails.ipManagement.visitManagement.visitOffsetType = Visit Offset Type
configureReport.projectDetails.ipManagement.visitManagement.visitNumber = Visit Number
configureReport.projectDetails.ipManagement.visitManagement.visitName = Visit Name
configureReport.projectDetails.ipManagement.visitManagement.group = Group
configureReport.projectDetails.ipManagement.visitManagement.intervalDuration = Interval Duration
configureReport.projectDetails.ipManagement.visitManagement.window = Window
configureReport.projectDetails.ipManagement.visitManagement.isDispense = Allowed To Dispense
configureReport.projectDetails.ipManagement.visitManagement.isRandomize = Allowed To Randomize
configureReport.projectDetails.ipManagement.visitManagement.isDTP = Allowed To DTP
configureReport.projectDetails.ipManagement.visitManagement.isSubjectReplace = Allowed To Subject Replace
configureReport.projectDetails.ipManagement.visitManagement.isDoseAdjustment = Dose Adjustment
configureReport.projectDetails.ipManagement.treatmentDesign = Treatment Design
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp = DTP IP
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.takeCare = Note:Yellow text represents unnumbered IP.
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.ip = IP
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.dtpMode = DTP mode
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.site = Site(Site Inventory)
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.siteSubject = Site(Direct-to-Patient Shipment)
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.depotSubject = Depot(Direct-to-Patient Shipment)
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen = By label/Open Configuration
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.takeCare = Note:Yellow text represents unnumbered IP.
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.group = Group
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.visitName = Visit Name
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.IpName = IP Name
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.dispensationQuantity = Dispensation Quantity
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.customFormula = Custom formula
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.combinedDispensation = Combined Dispensation Label
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.ipSpecification = IP Specification
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.specification = Specification
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.automaticAssignment = Automatic Assignment
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.calculationUnit = Calculation Unit
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig = According to the calculation formula
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge = Age
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.takeCare = Note:(1)Age:Calculated according to 365.25 days/year;(2)Yellow text represents unnumbered IP.
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.group = Group
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.visitName = Visit Name
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.IpName = IP Name
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ipSpecification = IP Specification
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ageRange = Age Range
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.dispensationQuantity = Dispensation Quantity
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.isOpenIp = Open IP
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.keepDecimalPlaces = Keep Decimal Places
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation = The calculated weight at the last visit
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual = The actual weight at the last visit
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random = The weight at the random visit
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight = Weight
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.takeCare = Note:Yellow text represents unnumbered IP.
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.group = Group
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.visitName = Visit Name
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.IpName = IP Name
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.ipSpecification = IP Specification
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightRange = Weight Range
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.dispensationQuantity = Dispensation Quantity
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.isOpenIp = Open IP
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.keepDecimalPlaces = Keep Decimal Places
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightComparisonCalculation = Weight Comparison Calculation
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.comparedWith = Compared with
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.change = The change is
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.calculation = Is used for this calculation
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA = Simple body surface area BSA
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.takeCare = Note:(1)Simple body surface area BSA = [weight (kg) x height (cm)/3600]1/2;(2)Yellow text represents unnumbered IP.
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.group = Group
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.visitName = Visit Name
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.IpName = IP Name
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.ipSpecification = IP Specification
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCapacity = Unit capacity
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCalculationStandard = Unit Calculation Standard
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.isOpenIp = Open IP
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.weightComparisonCalculation = Weight Comparison Calculation
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.comparedWith = Compared with
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.change = The change is
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.calculation = Is used for this calculation
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA = Other body surface area BSA(Press Custom formula)
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare1 = Note:(1)Custom formula:
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare2 = (2)Yellow text represents unnumbered IP.
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.group = Group
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.visitName = Visit Name
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.IpName = IP Name
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.ipSpecification = IP Specification
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCapacity = Unit capacity
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCalculationStandard = Unit Calculation Standard
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.isOpenIp = Open IP
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.weightComparisonCalculation = Weight Comparison Calculation
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.comparedWith = Compared with
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.change = The change is
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.calculation = Is used for this calculation
form.control.type.input = Input Box
form.control.type.inputNumber = Numeric Input Box
form.control.type.textArea = Multiline Text Box
form.control.type.checkbox = Checkbox
form.control.type.radio = Radio Box
form.control.type.switch = Switch
form.control.type.date = Date Selection Box
form.control.type.dateTime = TimePickerDialog
form.control.type.select = Drop-down Box
form.control.type.format.characterLength = Character Length
form.control.type.format.inputNumber.numberLength = Number Length
form.control.type.format.inputNumber.decimalLength = Decimal Length
form.control.type.format.checkbox = Checkbox
subjectReport.title = Subject Trail
subjectReport.projectName = Project Name
subjectReport.sponsor = Sponsor
subjectReport.type.cohort = Cohort Name
subjectReport.type.stage = Stage Name
subjectReport.autograph = Investigator Signature
subjectReport.signingDate = Signature Date
subjectReport.history.serialNumber = No.
subjectReport.history.operator = Operator
subjectReport.history.operationTime = Operation Time
subjectReport.history.operationContent = Operation Content
simulateRandomReport.configureParameterDetail = Randomization Design Parameters
simulateRandomReport.configureParameterDetail.randomType = Randomization Type
simulateRandomReport.configureParameterDetail.randomType.blockRandomization = Block Randomization
simulateRandomReport.configureParameterDetail.randomType.minimizedRandomization = Minimized Randomization
simulateRandomReport.configureParameterDetail.randomizationListName = Randomization List Name
simulateRandomReport.configureParameterDetail.biasProbability = Bias Probability
simulateRandomReport.configureParameterDetail.randomCount = Number of random numbers
simulateRandomReport.configureParameterDetail.groupConfiguration = Group Configuration
simulateRandomReport.configureParameterDetail.groupConfiguration.groupName = Group Name
simulateRandomReport.configureParameterDetail.groupConfiguration.subGroupName = Sub Group
simulateRandomReport.configureParameterDetail.groupConfiguration.groupProportion = Weight Ratio
simulateRandomReport.configureParameterDetail.stratificationFactor = Stratification Factor
simulateRandomReport.configureParameterDetail.stratificationFactor.factorName = Factor Name
simulateRandomReport.configureParameterDetail.stratificationFactor.option = Option
simulateRandomReport.configureParameterDetail.stratificationFactor.weightRatio = Weight Ratio
simulateRandomReport.configureParameterDetail.isCountry = Is The Country A Stratification Factor
simulateRandomReport.configureParameterDetail.isCountry.true = Yes
simulateRandomReport.configureParameterDetail.isCountry.false = No
simulateRandomReport.configureParameterDetail.isSite = Is The Site A Stratification Factor
simulateRandomReport.configureParameterDetail.isSite.true = Yes
simulateRandomReport.configureParameterDetail.isSite.false = No
simulateRandomReport.configureParameterDetail.isRegion = Is The Region A Stratification Factor
simulateRandomReport.configureParameterDetail.isRegion.true = Yes
simulateRandomReport.configureParameterDetail.isRegion.false = No
simulateRandomReport.simulateRandomDetail = Randomization Simulation
simulateRandomReport.simulateRandomDetail.parameter = Randomization Simulation Parameters
simulateRandomReport.simulateRandomDetail.parameter.simulateRandomName = Randomization Simulation Name
simulateRandomReport.simulateRandomDetail.parameter.randomListName = Name Of The Enabled Random List
simulateRandomReport.simulateRandomDetail.parameter.siteNumber = Site Quantity
simulateRandomReport.simulateRandomDetail.parameter.regionNumber = Region Quantity
simulateRandomReport.simulateRandomDetail.parameter.countryNumber = Country Quantity
simulateRandomReport.simulateRandomDetail.parameter.runNumber = Number Of Runs
simulateRandomReport.simulateRandomDetail.parameter.subjectQuantity = Subject Quantity
simulateRandomReport.simulateRandomDetail.parameter.factorRatio = Stratification Case Numbers
simulateRandomReport.simulateRandomDetail.label.project = Project
simulateRandomReport.simulateRandomDetail.label.serial = No.
simulateRandomReport.simulateRandomDetail.label.site = Site
simulateRandomReport.simulateRandomDetail.label.region = Region
simulateRandomReport.simulateRandomDetail.label.country = Country
simulateRandomReport.simulateRandomDetail.label.factor = Layer
simulateRandomReport.simulateRandomDetail.label.combinationFactor = Portfolio Stratification
simulateRandomReport.simulateRandomDetail.label.subjectCountMin = Minimum number of subjects
simulateRandomReport.simulateRandomDetail.label.total = Total
simulateRandomReport.simulateRandomDetail.label.runCount = Number of runs
simulateRandomReport.simulateRandomDetail.label.peopleCount = Number of People
simulateRandomReport.simulateRandomDetail.label.unbalanced = Imbalance
simulateRandomReport.simulateRandomDetail.overviewDetail = Overview
simulateRandomReport.simulateRandomDetail.overviewDetail.averageStandardDeviation = Average ± Standard Deviation
simulateRandomReport.simulateRandomDetail.overviewDetail.min = Min
simulateRandomReport.simulateRandomDetail.overviewDetail.numberOfRunningImbalances = number of running imbalances
simulateRandomReport.simulateRandomDetail.detailedResults = Detailed Results
medicine.status.toBeWarehoused = To be Warehoused
medicine.status.available = Available
medicine.status.delivered = Confirmed
medicine.status.transit = In Delivery
medicine.status.quarantine = Quarantined
medicine.status.used = Dispensed
medicine.status.lose = Lost/Wasted
medicine.status.expired = Expired
medicine.status.receive = Received
medicine.status.return = Returned
medicine.status.destroy = Destroyed
medicine.status.InOrder = To be Confirmed
medicine.status.stockPending = To be warehoused
medicine.status.apply = Have Applied
medicine.status.fzn = Frozen
medicine.status.toBeApproved = To be approved
medicine.status.locked = Locked
medicine.status.dsm = To be scanned
medicine.status.dsh = To be approved
medicine.status.shsb = Approval failed
notice.exclude_recipient_list.email.account = Email Account

[operation_log]
notifications.notice.env_name = Environment
notifications.notice.basic.settings = Basic Settings
notifications.notice.basic.settings.emailLanguage_automatedTasks = Email Language-Automated Tasks
notifications.notice.basic.settings.emailLanguage_manualTasks = Email Language-Manual Tasks
notifications.notice.basic.settings.email.automatedTasks = Automated Tasks
notifications.notice.basic.settings.email.manualTasks = Manual Tasks
notifications.notice.basic.settings.emailLanguage_zh = Chinese
notifications.notice.basic.settings.emailLanguage_en = English
notifications.notice.basic.settings.emailLanguage_zh_en = English
notifications.notice.subject.add = Subject Register
notifications.notice.subject.random = Subject Randomization
notifications.notice.subject.signOut = Subject Stop
notifications.notice.subject.replace = Subject Replacement
notifications.notice.subject.update = Subject Edit
notifications.notice.subject.screen = Subject Screening
notifications.notice.subject.dispensing = Subject Dispensation
notifications.notice.subject.alarm = Subject Alert
notifications.notice.subject.unblinding = Emergency Unblinding
notifications.notice.medicine.isolation = IP Quarantine
notifications.notice.medicine.order = Supply Shipment
notifications.notice.medicine.reminder = IP Expiration
notifications.notice.medicine.alarm = Site Inventory Alert
notifications.notice.medicine.alarm.scene.no_automatic_title = IP Inventory
notifications.notice.medicine.alarm.scene.forecast_title = Inventory used time prediction   Prediction reminder lead time：
notifications.notice.storehouse.alarm = Depot Inventory Alert
notifications.notice.order.timeout = Late Shipment Alert
notifications.notice.subject.alert.threshold = Subject limit setting
notifications.notice.email.content = Email Content
notifications.roles = Role
notifications.exclusiveReceivers = Excluded Recipients
notifications.dispensing.contentConfiguration = Content Configuration
notifications.dispensing.contentConfiguration.group = Group
notifications.dispensing.contentConfiguration.randomizationNumber = Randomization Number
notifications.dispensing.contentConfiguration.projectNumber = Project Number
notifications.dispensing.contentConfiguration.projectName = Project Name
notifications.dispensing.contentConfiguration.siteNumber = Site Number
notifications.dispensing.contentConfiguration.siteName = Site Name
notifications.dispensing.scene = Scene
notifications.dispensing.scene.Dispense = Dispense
notifications.dispensing.scene.unscheduledDispense = Unscheduled Dispense
notifications.dispensing.scene.reDispense = Re-dispense
notifications.dispensing.scene.replaceIP = Replace IP
notifications.dispensing.scene.retrieveIP = Retrieve IP
notifications.dispensing.scene.actuallyUsedIP = Actually Dispensed IP
notifications.dispensing.scene.notAttend =  Do Not Attend Visit
notifications.subject.screen.success = Screening successful
notifications.subject.screen.fail = Screening failed
notifications.subject.update.form.factor = Form/Factor Information
notifications.subject.update.screen = Screen Information
notifications.subject.update.stop = Stop Information
notifications.subject.update.finish = Complete study Information
notifications.subject.update.shortname = Subject ID
notifications.isolation.scene = Scene
notifications.isolation.scene.quarantine = Quarantine
notifications.isolation.scene.release = Lift
notifications.order.scene = Scene
notifications.order.scene.apply = Apply
notifications.order.scene.applicationApprovalFailed = Application approval failed
notifications.order.scene.createManualOrder = Create (manual shipment)
notifications.order.scene.cancel = Cancel
notifications.order.scene.confirm = Confirm
notifications.order.scene.close = Close
notifications.order.scene.deliver = Deliver
notifications.order.scene.receive = Receive
notifications.order.scene.termination = Termination
notifications.order.scene.lost = Lost
notifications.order.scene.createAutomaticOrder = Create (automatic shipment)
notifications.order.scene.creationFailedAutomaticOrder = Creation failed (automatic shipment)
notifications.order.scene.automaticOrderAlarm = Automatic Shipment Alerts
notifications.notice.order.timeout.lateShipmentAlertSetting = Number of days from shipment confirmation
notifications.notice.order.timeout.lateShipmentSendAlertSetting = Number of days from shipment in delivery
notifications.notice.email.content.body.configuration = Body Configuration
notifications.notice.email.content.body.configuration.project.name = Project Name
notifications.notice.email.content.body.configuration.project.no = Project Number
notifications.notice.email.content.body.configuration.project.environment = Project Environment
notifications.notice.email.content.body.configuration.site.name = Site Name
notifications.notice.email.content.body.configuration.site.number = Site Number
notifications.notice.email.content.scene = Scene
notifications.notice.email.content.scene.subject.register = Subject Register
notifications.notice.email.content.scene.subject.randomization = Subject Randomization
notifications.notice.email.content.scene.subject.signOut = Subject Stop
notifications.notice.email.content.scene.subject.replace = Subject Replacement
notifications.notice.email.content.scene.subject.update = Subject Edit
notifications.notice.email.content.scene.subject.dispensation = Subject Dispensation
notifications.notice.email.content.scene.subject.alert = Subject Alert
notifications.notice.email.content.scene.emergency.unblinding = Emergency Unblinding
notifications.notice.email.content.scene.ip.quarantine = IP Quarantine
notifications.notice.email.content.scene.shipment.order = Supply Shipment
notifications.notice.email.content.scene.ip.expiration.reminder = IP Expiration Reminder
notifications.notice.email.content.scene.inventory.alert = Inventory Alert
notifications.notice.email.content.scene.depot.inventory.alert = Depot Inventory Alert
notifications.notice.email.content.scene.late.shipment.alert = Late Shipment Alert
notifications.notice.email.content.scene.subject.alert.threshold = Subject limit setting reminder
rest.receiveRest.assured.ip.receive = CodeTrust Platform IP receive
rest.receiveRest.assured.ip.return = CodeTrust Platform IP return
rest.receiveRest.assured.ip.destroy = CodeTrust Platform IP destroy
operation.projects.main.setting.function.admin.cloud = Cloud Admin
operation.projects.main.setting.function.admin.cloud.delete = User Deleted
operation.projects.main.setting.function.admin.cloud.disable = User Disabled
drug.configure.setting.dose.level = Dose Level
drug.configure.setting.dose.visit.judgment = Visit Judgment
drug.configure.setting.doseAdjustment.open = Open
drug.configure.setting.doseAdjustment.close = Close
drug.configure.setting.dose.visitInheritance.true = true
drug.configure.setting.dose.visitInheritance.false = false

[error]
env.copy.prod.isCopy = Copy to existing PROD environment, only support the environment copied from PROD to copy again.
env.copy.prod.isCopy.cohort.fail = Replication failed, Cohort is inconsistent, please modify and replicate the migration again.
env.copy.prod.isCopy.stage.fail = Replication failed, Stage is inconsistent, please modify and replicate the migration again.
env.copy.prod.isCopy.core = Replication failed, Core configuration is inconsistent, please modify and replicate the migration again.
