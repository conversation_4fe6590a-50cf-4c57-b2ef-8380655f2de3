package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
)

// RaveController struct
type RaveController struct {
	s service.RaveService
}

// CreateRaveSubject 创建受试者
// Method:	POST
func (c *RaveController) CreateRaveSubject(ctx *gin.Context) {
	studyOID := ctx.Query("studyOID")
	locationOID := ctx.Query("locationOID")
	subjectKey := ctx.Query("subjectKey")
	raveUrl := ctx.Query("raveUrl")
	err := c.s.CreateRaveSubject(ctx, studyOID, locationOID, subjectKey, raveUrl)
	tools.Response(ctx, err)
}

// UpdateRaveSubject 更新受试者字段
// Method:	POST
func (c *RaveController) UpdateRaveSubject(ctx *gin.Context) {
	studyOID := ctx.Query("studyOID")
	locationOID := ctx.Query("locationOID")
	subjectKey := ctx.Query("subjectKey")
	raveUrl := ctx.Query("raveUrl")
	folderOid := ctx.Query("folderOid")
	formOid := ctx.Query("formOid")
	fieldData := ctx.Query("fieldData")
	err := c.s.UpdateRaveSubject(ctx, studyOID, locationOID, subjectKey, raveUrl, folderOid, formOid, fieldData)
	tools.Response(ctx, err)
}

// GetEnvUrl 环境/URL
// Method:	GET
func (c *RaveController) GetEnvUrl(ctx *gin.Context) {
	projectId := ctx.Query("projectId")
	customerId := ctx.Query("customerId")
	data, err := c.s.GetEnvUrl(ctx, projectId, customerId)
	tools.Response(ctx, err, data)
}

// GetCohortVisitRandomization 查询:群组/阶段-访视、随机
// Method:	GET
func (c *RaveController) GetCohortVisitRandomization(ctx *gin.Context) {
	projectId := ctx.Query("projectId")
	customerId := ctx.Query("customerId")
	data, err := c.s.GetCohortVisitRandomization(ctx, projectId, customerId, models.Project{})
	tools.Response(ctx, err, data)
}
