import React, {useEffect} from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {
    Button,
    Checkbox,
    Col,
    Form,
    Input,
    InputNumber,
    message,
    Modal,
    notification,
    Radio,
    Row,
    Select,
    Spin,
    Switch,
    TimePicker
} from "antd";
import {useAuth} from "../../../../context/auth";
import {getProjectAttribute} from "../../../../api/randomization";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {add, factorRandom, getForm, getSubjectNumber, subjectEdcVerification, update,} from "../../../../api/subject";
import {CheckCircleOutlined, CloseCircleFilled, InfoCircleFilled} from "@ant-design/icons";
import styled from "@emotion/styled";
import {handleDateFormat} from "../../../../tools/dateFormat";
import DatePicker from "../../../../components/DatePicker";
import dayjs from "dayjs";
import {Title} from "../../../../components/title";
import {EdcVerificationTip} from "./edc_verification_tip";
import {useSubject} from "./context";
import {nilObjectId} from "../../../../data/data";
import {useGlobal} from "../../../../context/global";
import {CustomConfirmModal} from "../../../../components/modal";
import {inRandomIsolation} from "../../../../utils/in_random_isolation";
import _ from "lodash";
import {pushScenarioFilter} from "../../../../utils/irt_push_edc_util";

export const RegisterAdd = (props: any) => {
    let edcCoveragePromptStatus = true;
    const g = useGlobal();
    const auth = useAuth();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const DatePickers: any = DatePicker;
    const ctx = useSubject()
    const [type, setType] = useSafeState<any>(0);
    const [visible, setVisible] = useSafeState<any>(false);
    const [fields, setFields] = useSafeState<any>(null);
    const [randomInfo, setRandomInfo] = useSafeState<any>([]);
    const [id, setId] = useSafeState<any>(null);
    const [title, setTitle] = useSafeState<any>("");
    const [prefix, setPrefix] = useSafeState<any>("");
    const [attributeIsScreen, setAttributeIsScreen] = useSafeState<any>(false)
    const [isScreen, setIsScreen] = useSafeState<any>(0)
    const [form] = Form.useForm();
    const edc_verification_tip_pt: any = React.useRef();
    //标记是否在受试者列表已经单选中心
    const [singleSite, setSingSite] = useSafeState<any>(false)
    const [singleCohort, setSingCohort] = useSafeState<any>(false)
    //该用户绑定的中心
    const [selectSites, setSelectSites] = useSafeState<any[]>([])
    const [selectCohorts, setSelectCohorts] = useSafeState<any[]>([])
    //当受试者列表单选中心时显示的名称
    const [siteName, setSiteName] = useSafeState("")
    const [siteNumber, setSiteNumber] = useSafeState("")
    const [siteId, setSiteId] = useSafeState(null)
    const [cohortId, setCohortId] = useSafeState(null)
    const [cohortName, setCohortName] = useSafeState("")
    const [showSubjectNumber, setShowSubjectNumber] = useSafeState(true);
    const [send, setSend] = useSafeState<any>(true);
    const [oldSubjectNo, setOldSubjectNo] = useSafeState<any>("");

    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const roleId = auth.project.permissions.role_id;
    const envId = auth.env.id;
    const projectType = auth.project.info.type;
    const connectEdc = auth.project.info.connect_edc;
    const pushMode = auth.project.info.push_mode;
    const cohorts = auth.env?.cohorts ? auth.env.cohorts : null;
    const [subjectRecord, setSubjectRecord] = useSafeState<any>(null);
    const reRandomCohorts = cohorts.filter((item: any) => item.name === subjectRecord?.cohort?.name)
    const { runAsync: addRun, loading: addLoading } = useFetch(add, {
        manual: true,
        onError: (err: any) => {
            err.json().then((data: any) => {
                if (data.code === 1005) {
                    message.error(data.msg[0]);
                    form.setFields(
                        data.error_data.map((field: string) => ({
                            name: field,
                            errors: [
                                <div style={{ fontSize: '12px' }}>
                                    {data.msg[1]}
                                </div>
                            ]
                        }))
                    );
                } else {
                    message.error(data.msg)
                }
            });
        },
    });
    const { runAsync: getSubjectNumberRun, loading: getSubjectNumberLoading } = useFetch(getSubjectNumber, { manual: true })
    const { runAsync: updateRun, loading: updateLoading } = useFetch(update, {
        manual: true,
        onError: (err: any) => {
            err.json().then((data: any) => {
                if (data.code === 1005) {
                    message.error(data.msg[0]);
                    form.setFields(
                        data.error_data.map((field: string) => ({
                            name: field,
                            errors: [
                                <div style={{ fontSize: '12px' }}>
                                    {data.msg[1]}
                                </div>
                            ]
                        }))
                    );
                } else {
                    message.error(data.msg)
                }
            });
        },
    });
    const { runAsync: runGetProjectAttribute } = useFetch(getProjectAttribute, {
        manual: true,
    });
    const { runAsync: factorRandomRun, loading: factorRandomLoading } =
        useFetch(factorRandom, {
            manual: true,
            onError: (err: any) => {
                err.json().then((data: any) => {
                    let errMsg = data.msg;
                    if (data.code === 1005) {
                        errMsg = data.msg[0];
                        form.setFields(
                            data.error_data.map((field: string) => ({
                                name: field,
                                errors: [
                                    <div style={{ fontSize: '12px' }}>
                                        {data.msg[1]}
                                    </div>
                                ]
                            }))
                        );
                    }
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                    fontSize: "14px",
                                }}
                            >
                                <CloseCircleFilled
                                    style={{
                                        color: "#F96964",
                                        paddingRight: "8px",
                                    }}
                                />
                                {formatMessage({ id: "subject.random.fail" })}
                            </div>
                        ),
                        description: (
                            <div
                                style={{
                                    paddingLeft: "20px",
                                    color: "#646566",
                                }}
                            >
                                {errMsg}
                            </div>
                        ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            // height:"88px",
                            background: "#FEF0EF",
                            borderRadius: "4px",
                        },
                    })
                })
            },
        });


    const [subjectStatus, setSubjectStatus] = useSafeState<any>(0);
    const [subjectRecordShow, setSubjectRecordShow] = useSafeState<any>(false);
    const [factorSave, setFactorSave] = useSafeState<any>(null);
    const [attribute, setAttribute] = useSafeState<any>(null)
    const [pushScenario, setPushScenario] = useSafeState<any>(false);
    const {
        runAsync: edcSubjectVerificationRun,
        loading: edcSubjectVerificationRunLoading,
    } = useFetch(subjectEdcVerification, { manual: true });
    const edcSupplier = auth.project.info.edc_supplier;
    const [radioMap, setRadioMap] = useSafeState<any>(new Map())
    // const { runAsync: runGetProject, loading } = useFetch(() => getProject({ id: projectId }), {
    //     refreshDeps: [projectId],
    //     onSuccess: (result: any) => {
    //         setEdcSupplier(result.data.info.edcSupplier)
    //     }
    // });

    const formChange = () => {
        if (type === 2) {
            // console.log("lll==" + JSON.stringify(subjectRecord))
            const a = _.cloneDeep(convertData(subjectRecord));
            // console.log("1===" + JSON.stringify(a)); 
            const b = _.cloneDeep(form.getFieldsValue());
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }
        }
    };

    function convertData(data: any) {
        const convertedData: { [key: string]: string } = {};
        data?.info.forEach((item: { name: string | number; value: string; }) => {
            convertedData[item.name] = item.value;
        });
        convertedData["isScreen"] = data.isScreen;
        convertedData["screenTime"] = data.screenTime;
        convertedData["icfTime"] = data.icfTime;
        convertedData["signOutRealTime"] = data.signOutRealTime;
        convertedData["reason"] = data.reason;
        convertedData["finishRemark"] = data.finishRemark;
        return convertedData;
    }

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) {
            // console.log("key1===" + key);
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                // console.log("key2===" + key);
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arraysAreEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    var s2 = obj2[key]
                    // if (key === "screenTime" || key === "icfTime" ) {
                    //     s2 = convert(obj2[key])
                    // } 
                    if (isTimestamp(s2)) {
                        s2 = convert(obj2[key]);
                    }
                    // if(key === "screenTime"){
                    //     console.log(key + "==1==" + obj1[key]);
                    //     console.log(key + "==2==" + obj2[key]);
                    //     console.log("s2==" + s2)
                    // }
                    if (obj1[key] !== s2) {
                        if ((obj1[key] === "" && s2 === null) || (obj1[key] === null && s2 === "")) {
                            // return true;
                        } else {
                            return false;
                        }
                    }
                }
            }
        }
        for (let key in obj2) {
            if (!obj1.hasOwnProperty(key) && (key.includes("field") || key.includes("factor") || key === "screenTime" || key === "icfTime") && obj2[key] !== null && obj2[key] !== undefined) {
                return false;
            }
        }
        return true;
    }

    function isTimestamp(ts: any): boolean {
        // 检查数值是否是数字
        if (ts instanceof Object) {
            let timestamp = Number(ts);
            // 定义合理的时间戳范围
            const minTimestamp = 1000000000000;
            const maxTimestamp = 9999999999999;

            // if(timestamp === 1883577600000){
            //     console.log("1==" + timestamp)
            //     console.log("2==" + minTimestamp)
            //     console.log("3==" + maxTimestamp)
            // }
            // 检查数值是否在合理的时间戳范围内
            if (timestamp > minTimestamp && timestamp < maxTimestamp) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    function convert(dateString: any) {
        // 创建一个新的Date对象
        const date = new Date(dateString);

        // 获取年、月、日
        const year = date.getFullYear();
        const month = date.getMonth() + 1; // 月份从0开始，需要加1
        const day = date.getDate();

        // 将月份和日期转换为两位数的字符串
        const monthString = month < 10 ? '0' + month : month.toString();
        const dayString = day < 10 ? '0' + day : day.toString();

        // 最终的日期字符串
        const formattedDate = `${year}-${monthString}-${dayString}`;

        return formattedDate
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }



    const { runAsync: getFormRun, loading: getFormLoading } = useFetch(
        getForm,
        { manual: true }
    );
    const subjectNumberSplicing = (attribute: any, siteNumber: any) => {
        // 拼接受试者号前缀
        if (attribute?.info.prefix) {
            if (attribute.info.prefixExpression) {
                return attribute.info.prefixExpression.replace(
                    "{siteNO}",
                    siteNumber
                );
            }
        }
        return "";
    };
    const getRegisterForm = (siteId: any, chid: any) => {
        // 查询表单
        let cohortOID = cohortId;
        if (chid != null && chid != "") {
            cohortOID = chid;
        }
        getFormRun({
            customerId: auth.customerId,
            projectId: auth.project.id,
            envId: auth.env.id,
            cohortId: cohortOID,
            siteId: siteId,
        }).then((result: any) => {
            let data = result.data;
            ctx.setLabel(data.form.fields[0].label);
            let addFiles: any[] = [];
            let fields = data?.form.fields;
            if (auth.project.info.number === "LZ901-300") {
                fields = fields.filter((e: any) => e.name !== "RANDAGE");
            }
            fields.filter((e: any) => e.status !== 2)
                .forEach((field: any) => {
                    //@typescript-eslint/no-unused-expressions
                    field.disabled = false;
                    if (field.dateFormat) {
                        field.datePicker = handleDateFormat(field.dateFormat);
                    }
                    if (data.factorSign) {
                        if (!field.stratification) {
                            addFiles.push(field);
                        }
                    } else {
                        addFiles.push(field);
                    }
                });
            setFields(addFiles);

            setAttributeIsScreen(data.attribute.info.isScreen);
            setAttribute(data.attribute);
            if (data.attribute.info.subjectNumberRule === 2 || data.attribute.info.subjectNumberRule === 3) {
                setShowSubjectNumber(false);
            } else {
                setShowSubjectNumber(true);
            }
        });
    }
    // factorSign(true/false) 为true时登记时不登记分层因素字段(修改同理) 为false时全部字段都要填写
    const show = (
        subjectData: any,
        type: number,
        sites: any[],
        siteParm: any[],
        cohorts: any[],
        cohortParm: any
    ) => {
        // sign 1基本研究 2群组研究 3再随机
        //查询attribute
        runGetProjectAttribute({ env: envId, cohort: cohortParm }).then((res: any) => {
            var attribute = res.data
            if (attribute.info.subjectNumberRule === 2 || attribute.info.subjectNumberRule === 3) {
                setShowSubjectNumber(false);
            } else {
                setShowSubjectNumber(true);
            }
        });
        let chid = null;
        if (cohorts.length > 0) {
            setSelectCohorts(cohorts);
            if (cohortParm != null) {
                setSingCohort(true);
                if (projectType === 3 && type === 1 && !inRandomIsolation(auth.project.info.number)) {
                    setSingCohort(false);
                }
                let cohort = cohorts.find((item) => item.id === cohortParm)
                setCohortName(cohort.name);
                if (cohort.type === 1){
                    setCohortName(cohort.name + " - " + cohort.re_random_name);
                }
                setCohortId(cohortParm);
                chid = cohortParm;
                form.setFieldsValue({ cohortId: cohortParm });
            }
            if (subjectData != null && subjectData.cohortId != nilObjectId) {
                setSingCohort(true);
                setCohortName(subjectData.cohort.name);
                if (subjectData.cohort.type === 1){
                    setCohortName(subjectData.cohort.name + " - " + subjectData.cohort.reRandomName);
                }
                setCohortId(subjectData.cohort.id);
                chid = subjectData.cohort.id;
                form.setFieldsValue({ cohortId: subjectData.cohort.id });
            }
        }
        if (type === 1) {
            if (projectType === 1 || cohortParm != null) {
                getRegisterForm(siteParm[0], chid);
            }
        } else {
            setAttribute(subjectData.attribute)
        }
        setSelectSites(sites)
        if (siteParm != null && siteParm.length === 1) {
            setSingSite(true)
            setSiteName(sites.find((item) => item.id === siteParm[0]).name)
            setSiteNumber(sites.find((item) => item.id === siteParm[0]).number)
            setSiteId(siteParm[0])
            form.setFieldsValue({ projectSiteId: siteParm[0] });
        } else if (subjectData) {
            setSiteNumber(subjectData.siteNumber)
            setSiteName(subjectData.siteName)
            setSiteId(subjectData.projectSiteID)
        }
        setSubjectRecord(subjectData);
        setType(type);
        // runGetProject().then();
        setVisible(true);
        if (type === 1) {
            // 添加
            setTitle(formatMessage({ id: "subject.register.add" }));
            if (auth.project.info.push_scenario != undefined) {
                setPushScenario(auth.project.info.push_scenario.register_push);
            }

        } else if (type === 2) {
            // 修改
            setSubjectStatus(subjectData.status);

            // 受试者随机后的状态
            if (subjectData.status === 3 || subjectData.status === 4 || subjectData.status === 5 || subjectData.status === 6 || subjectData.status === 9) {
                if (auth.project.info.push_scenario != undefined) {
                    setPushScenario(auth.project.info.push_scenario.update_random_after_push);
                }
            } else {      // 否则就是随机前的状态
                if (auth.project.info.push_scenario != undefined) {
                    setPushScenario(auth.project.info.push_scenario.update_random_front_push);
                }
            }

            // EDC对接项目记录修改前的受试者号
            if (connectEdc === 1 && pushMode === 2 && edcSupplier === 1) {
                setOldSubjectNo(subjectData.shortname);
            }
            setTitle(formatMessage({ id: "subject.register.update" }));
            let updateFiles: any[] = []; // 如果是修改，只修改设置了可修改的字段
            let fields = subjectData?.form.fields;
            if (auth.project.info.number === "LZ901-300") {
                fields = fields.filter((e: any) => e.name !== "RANDAGE");
            }
            fields.forEach((field: any) => {
                field.disabled = false;
                if (field.dateFormat) {
                    field.datePicker = handleDateFormat(field.dateFormat);
                }
                if (!field.modifiable) {
                    field.disabled = true;
                    field.required = false;
                }
                if (field.name === "shortname") {
                    if (subjectData.status !== 1 &&
                        subjectData.status !== 2 &&
                        subjectData.status !== 7 &&
                        subjectData.status !== 8) {
                        if (subjectData.attribute.info.isScreen === true) {
                            if (!(subjectData.status === 4 && subjectData.isScreen !== false && subjectData.group == "")) {
                                field.disabled = true;
                                field.required = false;
                            }
                        } else {
                            if (!(subjectData.status === 4 && subjectData.group == "")) {
                                field.disabled = true;
                                field.required = false;
                            }
                        }
                    }
                }
                if (field.status === 2) {
                    field.disabled = true;
                    field.required = false;
                }
                if (subjectData.factorSign) {
                    if (subjectData.status === 1 ||
                        subjectData.status === 2 ||
                        subjectData.status === 4 ||
                        subjectData.status === 5 ||
                        subjectData.status === 7 ||
                        subjectData.status === 8) {
                        if (!field.stratification) {
                            updateFiles.push(field);
                        }
                        if (field.stratification && subjectData.group !== "") {
                            field.disabled = true;
                            field.required = false;
                            updateFiles.push(field);
                        }
                    } else {
                        if (field.stratification) {
                            field.disabled = true;
                            field.required = false;
                        }
                        updateFiles.push(field);
                    }
                } else {
                    if (
                        field.stratification &&
                        subjectData.status !== 1 &&
                        subjectData.status !== 2 &&
                        subjectData.status !== 7 &&
                        subjectData.status !== 8
                    ) {
                        if (subjectData.attribute.info.isScreen === true) {
                            if (!(subjectData.status === 4 && subjectData.isScreen !== false && subjectData.group == "")) {
                                field.disabled = true;
                                field.required = false;
                            }
                        } else {
                            if (!(subjectData.status === 4 && subjectData.group == "")) {
                                field.disabled = true;
                                field.required = false;
                            }
                        }
                    }
                    // 特殊逻辑处理 HPV-PRO-015 随机后不允许修改分层计算的表单
                    if (!field.stratification && field.isCustomFormula && subjectData.group !== "" && auth.project.info.number === "HPV-PRO-015"){
                        field.disabled = true;
                    }
                    updateFiles.push(field);
                }
            });

            if (projectType === 3 || !inRandomIsolation(auth.project.info.number)||(projectType === 2 && subjectData?.cohort.type === 1)) {
                setCohortId(cohortParm);
            } else {
                setCohortId(subjectData.cohortId);
            }
            setIsScreen(subjectData.isScreen);
            setFields(updateFiles);
            setId(subjectData.id);
            setAttributeIsScreen(subjectData.attribute.info.isScreen)
            handleSubjectData(subjectData, subjectData?.form.fields);
        } else if (type === 3) {
            setTitle(formatMessage({ id: "subject.random" }));
            if (auth.project.info.push_scenario != undefined) {
                setPushScenario(auth.project.info.push_scenario.random_push);
            }
            let randomFiles: any[] = [];
            let randomInfo: any[] = []; // 展示已经登记的表单信息
            let fields = subjectData?.form.fields;
            if (auth.project.info.number === "LZ901-300") {
                fields = fields.filter((e: any) => e.name !== "RANDAGE");
            }
            fields.forEach((field: any) => {
                field.disabled = false;
                if (field.status === 2) {           // 无效字段
                    field.disabled = true;
                    field.required = false;
                }

                if (field.dateFormat) {
                    field.datePicker = handleDateFormat(field.dateFormat);
                }
                // 受试者号
                if (field.name !== "shortname") {
                    randomFiles.push(field);

                } else {
                    randomInfo.push(field);
                }
            });
            if (projectType === 3 || !inRandomIsolation(auth.project.info.number)||(projectType === 2 && subjectData?.cohort.type === 1)) {
                if (cohortParm != null) {
                    setCohortId(cohortParm);
                }
                let subjectFormData = {};
                randomFiles.forEach((field: any) => {
                    if (!field.stratification) {
                        subjectData?.reRandomInfo.forEach((rdi: any) => {
                            if (rdi.cohortId === subjectData.cohortId && rdi.name === field.name && field.type !== "datePicker" && field.type !== "timePicker") {
                                Object.assign(subjectFormData, { [rdi.name]: rdi.value });
                            }else if (rdi.cohortId === subjectData.cohortId && rdi.name === field.name && field.type === "datePicker") {
                                Object.assign(subjectFormData, { [rdi.name]: rdi.value ? dayjs(rdi.value, "YYYY-MM-DD") : undefined });
                            } else if (rdi.cohortId === subjectData.cohortId && rdi.name === field.name && field.type === "timePicker") {
                                Object.assign(subjectFormData, { [rdi.name]: rdi.value ? dayjs(rdi.value, "YYYY-MM-DD HH:mm:ss") : undefined });
                            }
                        })
                    }
                })
                form.setFieldsValue({ ...subjectFormData });
9            } else {
                setCohortId(subjectData?.cohortId);
                // 给表单放默认值
                let subjectFormData = {};
                randomFiles.forEach((field: any) => {
                    if (!field.stratification) {
                        subjectData?.info?.forEach((rdi: any) => {
                            if (rdi.name === field.name && field.type !== "datePicker" && field.type !== "timePicker") {
                                Object.assign(subjectFormData, { [rdi.name]: rdi.value });
                            } else if (rdi.name === field.name && field.type === "datePicker") {
                                Object.assign(subjectFormData, { [rdi.name]: rdi.value ? dayjs(rdi.value, "YYYY-MM-DD") : undefined });
                            } else if (rdi.name === field.name && field.type === "timePicker") {
                                Object.assign(subjectFormData, { [rdi.name]: rdi.value ? dayjs(rdi.value, "YYYY-MM-DD HH:mm:ss") : undefined });
                            }
                        })
                    }
                })
                form.setFieldsValue({ ...subjectFormData });
            }
            setFields(randomFiles);
            setRandomInfo(randomInfo);
            setId(subjectData?.id);
        }
    };

    useEffect(() => {
        let prefix = subjectNumberSplicing(attribute, siteNumber)
        setPrefix(prefix)
        if (subjectRecord == null) {
            if (siteId !== null) {
                getSubjectNumberRun({
                    customerId: auth.customerId,
                    projectId: auth.project.id,
                    envId: auth.env.id,
                    cohortId: cohortId,
                    siteId: siteId,
                }).then((result: any) => {
                    let data = result.data;
                    form.setFieldsValue({ shortname: data });
                    console.log(data)
                });
            }
            // form.setFieldsValue({ shortname: prefix });
        }
    }, [attribute, siteNumber])

    const hide = () => {
        setVisible(false);
        setFields(null);
        setId(null);
        setTitle("");
        setPrefix("");
        setSend(true);
        form.resetFields();
        setSubjectRecord(null);
        setSingSite(false)
        setIsScreen(false)
        setSingCohort(false)
        setSiteName("")
        setSiteNumber("")
        setSiteId(null)
        setCohortName("")
        setCohortId(null)
        setAttributeIsScreen(false)
        setShowSubjectNumber(true)
        setAttribute(null)
        setRandomInfo([])
        edcCoveragePromptStatus = true
        setOldSubjectNo("");
        setRadioMap(new Map())
    };

    const handleSubjectData = (subjectData: any, fields: any) => {
        let res: any = {};
        subjectData.info.forEach((info: any) => {
            fields.forEach((field: any) => {
                if (field.isCalc === false) {
                    if (info.name === field.name && field.type === "datePicker") {
                        res[info.name] = info.value ? dayjs(info.value, "YYYY-MM-DD") : undefined;
                    } else if (info.name === field.name && field.type === "timePicker") {
                        res[info.name] = info.value
                            ? dayjs(info.value, "YYYY-MM-DD HH:mm:ss") : undefined;
                    } else if (info.name === field.name) {
                        res[info.name] = info.value;
                        if (field.type === "radio") {
                            let updateMap = new Map(radioMap)
                            updateMap.set(field.name, info.value)
                            setRadioMap(updateMap)
                        }
                    }
                }
            });
        });
        if (subjectData.isScreen != null) {
            res["isScreen"] = subjectData.isScreen
        }
        if (subjectData.screenTime !== "") {
            res["screenTime"] = dayjs(subjectData.screenTime, "YYYY-MM-DD")
        }
        if (subjectData.icfTime !== "") {
            res["icfTime"] = dayjs(subjectData.icfTime, "YYYY-MM-DD")
        }
        if (subjectData.signOutRealTime !== "") {
            res["signOutRealTime"] = dayjs(subjectData.signOutRealTime, "YYYY-MM-DD")
        }
        if (subjectData.reason !== "") {
            res["reason"] = subjectData.reason
        }
        if (subjectData.finishRemark !== "") {
            res["finishRemark"] = subjectData.finishRemark
        }
        form.setFieldsValue(res);
    };

    // 处理存入数据库的数据（日期）
    const handleFromData = (fields: any, fromData: any) => {
        fields.forEach((field: any) => {
            if (field.type === "datePicker" && field.isCalc === false) {
                // 日期类型
                fromData[field.name] = fromData[field.name]
                    ? dayjs(fromData[field.name]).format("YYYY-MM-DD")
                    : "";
            } else if (field.type === "timePicker" && field.isCalc === false) {
                // 时间类型
                fromData[field.name] = fromData[field.name]
                    ? dayjs(fromData[field.name]).format("YYYY-MM-DD HH:mm:ss")
                    : "";
            }
        });
        if (fromData["screenTime"] != null) {
            fromData["screenTime"] = dayjs(fromData["screenTime"]).format("YYYY-MM-DD")
        }
        if (fromData["icfTime"] != null) {
            fromData["icfTime"] = dayjs(fromData["icfTime"]).format("YYYY-MM-DD")
        }
        if (fromData["signOutRealTime"] != null) {
            fromData["signOutRealTime"] = dayjs(fromData["signOutRealTime"]).format("YYYY-MM-DD")
        }
        return fromData;
    };
    //LZ901-300 项目特殊处理
    const calculateYearDifference = (date1: string, date2: string) => {
        var y1 = new Date(date1).getFullYear();
        var y2 = new Date(date2).getFullYear();
        var m1 = new Date(date1).getMonth();
        var m2 = new Date(date2).getMonth();
        var d1 = new Date(date1).getDate();
        var d2 = new Date(date2).getDate();
        if (y1 < y2) {
            return -1;
        }
        if (y1 === y2) {
            return 0;
        }
        if (y1 > y2) {
            if (m1 > m2) {
                return y1 - y2;
            }
            if (m1 === m2) {
                if (d1 > d2) {
                    return y1 - y2;
                }
                if (d1 == d2) {
                    return y1 - y2;
                }
                if (d1 < d2) {
                    return y1 - y2 - 1;
                }
            }
            if (m1 < m2) {
                return y1 - y2 - 1;
            }
        }
        return 0;
    };
    //LZ901-300 项目特殊处理
    const getRandageValue = (year: number) => {
        if (40 <= year && year <= 49) {
            return "1";
        } else if (50 <= year && year <= 59) {
            return "2";
        } else if (60 <= year && year <= 69) {
            return "3";
        } else if (70 <= year) {
            return "4";
        }
        return "0";
    };

    const edc_push_check = (resp: any) => {
        let pushTip = "";
        if (resp.data.subjectRegisterPushIn) {     // 登记推送中
            pushTip = formatMessage({ id: "subject.edc.register.push.centre" });
        } else if (resp.data.subjectPushIn) {   // 非登记推送中
            pushTip = formatMessage({ id: "subject.edc.push.centre" });
        }

        if (pushTip !== "") {  // 存在推送中的数据
            Modal.confirm({
                centered: true,
                title: formatMessage({ id: "common.please.confirm" }),
                icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                content: pushTip,
                onOk: () => {
                    save();
                },
                cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                okText: formatMessage({ id: "subject.edc.continue.update" }),
            });
            edcCoveragePromptStatus = false;
        } else {     // 不存在推送中的数据直接弹数据对比框
            edcTip(resp.data, "update");
        }
    };

    const edc_random_verification_dj = () => {
        form.validateFields()
            .then((values) => {
                if (showSubjectNumber && (
                    values.shortname === null ||
                    values.shortname === undefined ||
                    values.shortname === "" ||
                    values.shortname === prefix)
                ) {
                    message.error(
                        // fields[0].label +
                        ((fields[0].labelEn === "" && fields[0].label === "") ? formatMessage({ id: "subject.number" }) : ((fields[0].labelEn !== "" && fields[0].label === "") ? (g.lang === "en" ? fields[0].labelEn : formatMessage({ id: "subject.number" })) : ((fields[0].labelEn === "" && fields[0].label !== "") ? fields[0].label : ((g.lang === "en") ? fields[0].labelEn : fields[0].label)))) +
                            formatMessage({ id: "subject.number.no.empty" })
                    );
                    return;
                }
                if (showSubjectNumber && fields[0].accuracy === 2) {
                    if (values.shortname.length !== parseInt(fields[0].digit)) {
                        message.error(
                            // fields[0].label +
                            ((fields[0].labelEn === "" && fields[0].label === "") ? formatMessage({ id: "subject.number" })  : ((fields[0].labelEn !== "" && fields[0].label === "") ? (g.lang === "en" ? fields[0].labelEn : formatMessage({ id: "subject.number" })) : ((fields[0].labelEn === "" && fields[0].label !== "") ? fields[0].label : ((g.lang === "en") ? fields[0].labelEn : fields[0].label)))) +
                                formatMessage({ id: "subject.number.digit.no" })
                        );
                        return;
                    }
                }
                // 数据处理好后执行修改或者添加操作
                let formData = handleFromData(fields, values);
                //LZ901-300 项目特殊处理
                if (auth.project.info.number === "LZ901-300") {
                    let now = dayjs().format("YYYY-MM-DD");
                    let year = calculateYearDifference(now, formData.field0);
                    let randageValue = getRandageValue(year);
                    if (randageValue !== "0") {
                        formData["RANDAGE"] = randageValue;
                    }
                }
                let lastId = "";
                if (selectCohorts.length > 0) {
                    lastId = selectCohorts.find((item) => item.id === cohortId) ? selectCohorts.find((item) => item.id === cohortId).last_id : nilObjectId;
                }
                if (id) {
                    // 请求校验接口
                    edcSubjectVerificationRun({
                        ...formData,
                        id: id,
                        lastId: lastId
                        // mark: 'register_update'
                    }).then((resp: any) => {
                        if (!resp.data.linkStatus) {         // 受试者接口请求异常
                            save();
                            notification.open({
                                message: (
                                    <div
                                        style={{
                                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                            fontSize: "14px",
                                        }}
                                    >
                                        <CheckCircleOutlined
                                            style={{
                                                color: "#00BB00",
                                                paddingRight: "8px",
                                            }}
                                        />
                                        {formatMessage({ id: "common.success" })}
                                    </div>
                                ),
                                description: (
                                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                                        {formatMessage({
                                            id: "subject.edc.interface.error",
                                        })}
                                    </div>
                                ),
                                duration: 5,
                                placement: "top",
                                style: {
                                    width: "720px",
                                    background: "#F0FFF0",
                                    borderRadius: "4px",
                                },
                            });
                        } else {
                            if (resp.data.subjectIsExist) {   // EDC存在当前修改的受试者
                                if (!resp.data.edcMapping) {   // EDC没配置映射关系
                                    Modal.confirm({
                                        centered: true,
                                        title: formatMessage({ id: "common.please.confirm" }),
                                        icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                        content: formatMessage({ id: "subject.edc.no.mapping" }),
                                        onOk: () => {
                                            save(true);
                                        },
                                        cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                        okText: formatMessage({ id: "subject.edc.continue.update" }),
                                    });
                                } else {   // EDC配置了映射关系
                                    let errText = "";
                                    if (resp.data.edcSubjectStatus === 2) {    // 筛选失败
                                        errText = formatMessage({ id: "subject.status.screen.fail" });
                                    } else if (resp.data.edcSubjectStatus === 5) {  // 已退出
                                        errText = formatMessage({ id: "subject.edc.subject.exited" });
                                    } else if (resp.data.edcSubjectStatus === 6) {  // 完成研究
                                        errText = formatMessage({ id: "subject.status.finish" });
                                    } else if (resp.data.edcSubjectStatus === 8) {  // 筛选中-不可随机
                                        errText = formatMessage({ id: "subject.edc.subject.screen.no.random" });
                                    }

                                    // 6.未开启筛选流程的项目或开启筛选流程后的已随机状态，在受试者修改环节，与EDC对接的受试者状态判断，拿掉。
                                    let skipFlag = attributeIsScreen == false || (attributeIsScreen == true && subjectStatus == 3)

                                    if (!skipFlag && errText !== "") {
                                        let content = formatMessage({ id: "subject.edc.subject" }) +
                                            errText +
                                            formatMessage({ id: "subject.edc.subject.continue.update" });

                                        Modal.confirm({
                                            centered: true,
                                            title: formatMessage({ id: "common.please.confirm" }),
                                            icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                            content: content,
                                            onOk: () => {
                                                edc_push_check(resp);
                                            },
                                            cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                            okText: formatMessage({ id: "subject.edc.continue.update" }),
                                        });
                                    } else {
                                        edc_push_check(resp);
                                    }
                                }
                            } else {  // EDC不存在当前修改的受试者
                                let pushTip = "";
                                if (resp.data.subjectRegisterPushIn) {     // 登记推送中
                                    pushTip = formatMessage({ id: "subject.edc.register.push.centre" });
                                } else {   // 非登记推送中
                                    pushTip = formatMessage({ id: "subject.edc.no.subject.push.centre" });
                                }
                                if (pushTip !== "") {
                                    // TODO content判断方式，多语言预览模式下存在风险
                                    Modal.confirm({
                                        centered: true,
                                        title: <span style={{fontWeight: "bold"}}>{formatMessage({id: "common.tips"})}</span>, // 请确认！ 改为  提示
                                        icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                        content: (pushTip === formatMessage({ id: "subject.edc.no.subject.push.centre" }) ?
                                        <>
                                            <span style={{color: "#ff4d4f"}}>{formatMessage({ id: "subject.edc.no.subject" })}</span>
                                            <span>{formatMessage({ id: "subject.edc.no.subject.push.centre" })}</span>
                                        </>
                                                :pushTip
                                        ),
                                        onOk: () => {
                                            save();
                                        },
                                        cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                        okText: formatMessage({ id: "subject.edc.continue.update" }),
                                    });
                                    edcCoveragePromptStatus = false;
                                } else {
                                    Modal.confirm({
                                        centered: true,
                                        title: <span style={{fontWeight: "bold"}}>{formatMessage({id: "common.tips"})}</span>, // 请确认！ 改为  提示
                                        icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                        content: (
                                            <>
                                                <span style={{color: "#ff4d4f"}}>{formatMessage({ id: "subject.edc.no.subject" })}</span>
                                                <span>{formatMessage({ id: "subject.edc.update.no.subject" })}</span>
                                            </>
                                        ),
                                        onOk: () => {
                                            save();
                                        },
                                        cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                        okText: formatMessage({ id: "subject.edc.continue.update" }),
                                    });
                                }
                            }
                        }
                    });
                } else {
                    edcSubjectVerificationRun({
                        ...values,
                        customerId: customerId,
                        projectId: projectId,
                        envId: envId,
                        cohortId: cohortId,
                        lastId: lastId,
                        roleId: roleId
                        // mark: 'register_update',
                        // codeStr: 'add'
                    }).then((resp: any) => {
                        if (!resp.data.linkStatus) {              // 受试者接口请求异常
                            save();
                            notification.open({
                                message: (
                                    <div
                                        style={{
                                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                            fontSize: "14px",
                                        }}
                                    >
                                        <CheckCircleOutlined
                                            style={{
                                                color: "#00BB00",
                                                paddingRight: "8px",
                                            }}
                                        />
                                        {formatMessage({ id: "common.success" })}
                                    </div>
                                ),
                                description: (
                                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                                        {formatMessage({
                                            id: "subject.edc.interface.error",
                                        })}
                                    </div>
                                ),
                                duration: 5,
                                placement: "top",
                                style: {
                                    width: "720px",
                                    background: "#F0FFF0",
                                    borderRadius: "4px",
                                },
                            });
                        } else if (!resp.data.subjectIsExist) {    // EDC不存在此受试者
                            Modal.confirm({
                                centered: true,
                                title: <span style={{fontWeight: "bold"}}>{formatMessage({id: "common.tips"})}</span>, // 请确认！ 改为  提示
                                icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                content: (
                                    <>
                                        <span style={{color: "#ff4d4f"}}>{formatMessage({ id: "subject.edc.no.subject" })}</span>
                                        <span>{formatMessage({ id: "subject.edc.register.no.subject" })}</span>
                                    </>
                                ),
                                onOk: () => {
                                    save();
                                },
                                cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                okText: formatMessage({ id: "subject.edc.continue.register" }),
                            });
                        } else if (!resp.data.edcMapping) {    // EDC未配置映射关系
                            Modal.confirm({
                                centered: true,
                                title: formatMessage({ id: "common.please.confirm" }),
                                icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                content: formatMessage({ id: "subject.edc.no.mapping" }),
                                onOk: () => {
                                    save(true);
                                },
                                cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                okText: formatMessage({ id: "subject.edc.continue.register" }),
                            });
                        } else {
                            if (resp.data.edcOpenStratification || resp.data.edcOpenForm) { // 配置了表单/分层
                                edcTip(resp.data, "register");
                            } else {
                                if (resp.data.edcOpenCohort && !resp.data.cohortIsAlike) {        // 配置了cohort并且cohort不一致
                                    Modal.confirm({
                                        centered: true,
                                        title: formatMessage({ id: "common.please.confirm" }),
                                        icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                        content: formatMessage(
                                            { id: "subject.edc.complete.cohort.registration" },
                                            {
                                                subjectNo: resp.data.irtSubjectNo,
                                                cohortName: resp.data.cohortName === "" ? " " : resp.data.cohortName,
                                            }
                                        ),
                                        onOk: () => {
                                            save();
                                        },
                                        cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                        okText: formatMessage({ id: "subject.edc.continue.register" }),
                                    });
                                } else {
                                    Modal.confirm({
                                        centered: true,
                                        title: formatMessage({ id: "common.please.confirm" }),
                                        icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                        content: formatMessage({ id: "subject.edc.complete.registration" }, { subjectNo: resp.data.irtSubjectNo }),
                                        onOk: () => {
                                            save();
                                        },
                                        cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                        okText: formatMessage({ id: "subject.edc.continue.register" }),
                                    });
                                }
                            }
                        }
                    });
                }
            });
    };

    // 受试者登记
    const save = (edcNoMapping = false) => {// 默认为false, 代表edc 配置了映射
        form.validateFields()
            .then((values) => {
                if (showSubjectNumber && (
                    values.shortname === null ||
                    values.shortname === undefined ||
                    values.shortname === "" ||
                    values.shortname === prefix)
                ) {
                    message.error(
                        // fields[0].label +
                        ((fields[0].labelEn === "" && fields[0].label === "") ?
                            (formatMessage({ id: "subject.number" })) :
                            ((fields[0].labelEn !== "" && fields[0].label === "") ? (g.lang === "en" ? fields[0].labelEn : formatMessage({ id: "subject.number" })) : ((fields[0].labelEn === "" && fields[0].label !== "") ? fields[0].label : ((g.lang === "en") ? fields[0].labelEn : fields[0].label))))
                        + formatMessage({ id: "subject.number.no.empty" })
                    );
                    return;
                }
                if (showSubjectNumber && fields[0].accuracy === 2) {
                    if (values.shortname.length !== parseInt(fields[0].digit)) {
                        message.error(
                            // fields[0].label +
                            ((fields[0].labelEn === "" && fields[0].label === "") ?

                                (formatMessage({ id: "subject.number" })) :
                                ((fields[0].labelEn !== "" && fields[0].label === "") ?
                                    (g.lang === "en" ? fields[0].labelEn : formatMessage({ id: "subject.number" })) :
                                    ((fields[0].labelEn === "" && fields[0].label !== "") ? fields[0].label : ((g.lang === "en") ? fields[0].labelEn : fields[0].label))))

                            + formatMessage({ id: "subject.number.digit.no" })
                        );
                        return;
                    }
                }
                // 数据处理好后执行修改或者添加操作
                let formData = handleFromData(fields, values);
                //LZ901-300 项目特殊处理
                if (auth.project.info.number === "LZ901-300") {
                    let now = dayjs().format("YYYY-MM-DD");
                    let year = calculateYearDifference(now, formData.field0);
                    let randageValue = getRandageValue(year);
                    if (randageValue !== "0") {
                        formData["RANDAGE"] = randageValue;
                    }
                }
                let lastId = "";
                if (selectCohorts.length > 0) {
                    lastId = selectCohorts.find((item) => item.id === cohortId) ? selectCohorts.find((item) => item.id === cohortId).last_id : nilObjectId;
                }
                if (id) {
                    updateRun({
                        ...formData,
                        id: id,
                        lastId: lastId,
                    }).then((resp: any) => {
                        if (resp.code === 0) {
                            let tip = resp.msg;
                            if (edcCoveragePromptStatus && connectEdc === 1 && pushMode === 2 && edcSupplier === 1 && pushScenario && !edcNoMapping) {
                                tip = formatMessage({ id: "subject.edc.cover.tip" });
                            }
                            message.success(tip);
                            props.refresh();
                            hide();
                        } else {
                            message.success(resp.msg);
                        }
                    });
                } else {
                    addRun({
                        ...values,
                        customerId: customerId,
                        projectId: projectId,
                        envId: envId,
                        cohortId: cohortId,
                        lastId: lastId,
                        roleId: roleId,
                    }).then((resp: any) => {
                        if (resp.data !== "") {
                            notification.open({
                                message: (
                                    <div
                                        style={{
                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                            fontSize: "14px",
                                            marginTop: "-8px",
                                        }}
                                    >
                                        <Row >
                                            <svg className="iconfont mouse" width={16} height={16} style={{ marginTop: "4px", marginRight: "10px" }}>
                                                <use xlinkHref="#icon-wanchengyanjiu" />
                                            </svg>
                                            <span style={{ fontWeight: "600" }}> {resp.data} </span>{formatMessage({ id: "subject.registration.success" })}
                                        </Row>
                                    </div>
                                ),
                                duration: 7,
                                placement: "top",
                                style: {
                                    width: "720px",
                                    height: "40px",
                                    background: "#E2F7EC",
                                    borderStyle: "solid",
                                    border: "1px",
                                    borderColor: "#41CC82",
                                    borderRadius: "4px",
                                },
                            });
                        } else {
                            message.success(resp.msg);
                        }
                        if (projectType !== 3 || inRandomIsolation(auth.project.info.number)) {
                            props.refresh(values.cohortId, values.projectSiteId, resp.data);
                        } else {
                            props.refresh(null, values.projectSiteId, resp.data);
                        }
                        hide();
                    });
                }
            })
            .catch((error) => { });
    };

    // 随机前表单校验
    const checkRandomForm = (values: any) => {
        let bl = true;
        fields.forEach((field: any) => {
            if (
                field.isCalc === false &&
                (values[field.name] === null || values[field.name] === undefined) &&
                field.stratification === true
            ) {
                message.error(
                    formatMessage({ id: "subject.check.random.form" })
                );
                bl = false;
            }
        });

        return bl;
    };

    // 随机方法(填写分层因素)
    const factor_random = () => {
        form.validateFields()
            .then((values) => {
                if (checkRandomForm(values)) {
                    let objectKey = Object.keys(values);
                    objectKey.map(
                        (it) => {
                            //在随机
                            if ((projectType === 3 && !inRandomIsolation(auth.project.info.number))||(projectType ===2 && subjectRecord?.cohort?.type === 1)) {
                                let formField = fields.find((item: any) => it == item.name);

                                if (fields.find((item: any) => it == item.name && (item.type === "radio" || item.type === "select"))) {
                                    if (values[it] != null && values[it]) {
                                        subjectRecord[it] = fields.find((item: any) => it == item.name).options.find(
                                            (option: any) => option.value == values[it]
                                        ).label
                                    }
                                }

                                if (fields.find((item: any) => it == item.name && (item.type === "checkbox"))) {
                                    if (values[it] != null && values[it]) {
                                        let opt = values[it];
                                        let opts = "";

                                        let options = fields.find((item: any) => it == item.name).options;
                                        if (options != null && Array.isArray(options)) {
                                            opt.forEach((item: any) => {
                                                let a = options.find((it: any) => it.value === item).label;
                                                opts = opts + a + " ";
                                            });
                                        }
                                        subjectRecord[it] = opts;
                                    }
                                }

                                if (fields.find((item: any) => it == item.name && (item.type === "switch"))) {
                                    if (values[it] != null && values[it]) {
                                        if (values[it]) {
                                            subjectRecord[it] = "yes"
                                        } else {
                                            subjectRecord[it] = "no"
                                        }
                                    }
                                }

                                if (fields.find((item: any) => it == item.name && (item.type === "datePicker"))) {
                                    if (values[it] != null && values[it]) {
                                        let format = formField.dateFormat;
                                        if (format === null || !format) {
                                            format = "YYYY-MM-DD";
                                        }
                                        subjectRecord[it] = dayjs(values[it]).format(format);
                                    }
                                }

                                if (fields.find((item: any) => it == item.name && (item.type === "timePicker"))) {
                                    if (values[it] != null && values[it]) {
                                        let format = formField.timeFormat;
                                        if (format === null || !format) {
                                            format = "YYYY-MM-DD HH:mm:ss";
                                        }
                                        subjectRecord[it] = dayjs(values[it]).format(format);
                                    }
                                }

                                if (fields.find((item: any) => it == item.name && (item.type == "input" || item.type == "inputNumber" || item.type == "textArea"))) {
                                    if (values[it] != null && values[it]) {
                                        subjectRecord[it] = values[it]
                                    }
                                }
                            }
                        }
                    );
                    setSubjectRecord(subjectRecord);
                    values["shortname"] = subjectRecord.shortname;
                    let formData = handleFromData(fields, values);
                    setFactorSave(formData);
                    setSubjectRecordShow(true);
                }
            })
    };

    const hide_confirm = () => {
        setSubjectRecordShow(false);
    };

    const edcTip = (record: any, type: string) => {
        edc_verification_tip_pt.current.show(record, type);
    };

    // 随机校验（随机前发药项目）
    const edc_random_verification = () => {
        hide_confirm();
        let subjectData = {
            ...factorSave,
            id: id,
            lastId: selectCohorts.find((item) => item.id === cohortId) ? selectCohorts.find((item) => item.id === cohortId).last_id : nilObjectId,
        };
        edcSubjectVerificationRun(subjectData).then((resp: any) => {
            if (!resp.data.linkStatus) {                 // 请求接口响应异常
                factor_random_save();
                notification.open({
                    message: (
                        <div
                            style={{
                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                fontSize: "14px",
                            }}
                        >
                            <CheckCircleOutlined
                                style={{
                                    color: "#00BB00",
                                    paddingRight: "8px",
                                }}
                            />
                            {formatMessage({ id: "common.success" })}
                        </div>
                    ),
                    description: (
                        <div style={{ paddingLeft: "20px", color: "#646566" }}>
                            {formatMessage({
                                id: "subject.edc.interface.error",
                            })}
                        </div>
                    ),
                    duration: 5,
                    placement: "top",
                    style: {
                        width: "720px",
                        background: "#F0FFF0",
                        borderRadius: "4px",
                    },
                });
            } else if (!resp.data.subjectIsExist) {       // EDC不存在当前受试者
                Modal.confirm({
                    centered: true,
                    title: formatMessage({ id: "common.please.confirm" }),
                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                    content: formatMessage({ id: "subject.edc.create.subject" }),
                    onOk: () => {
                        if (auth.project.info.push_scenario.random_block_push || auth.project.info.push_scenario.form_random_block_push || (projectType !== 1 && auth.project.info.push_scenario.cohort_random_block_push)) {
                            let labelList = [];
                            if (auth.project.info.push_scenario.random_block_push) {
                                labelList.push(formatMessage({ id: "projects.subject.stratification" }));
                            }
                            if (auth.project.info.push_scenario.form_random_block_push) {
                                labelList.push(formatMessage({ id: "projects.subject.form" }));
                            }
                            if (projectType !== 1 && auth.project.info.push_scenario.cohort_random_block_push) {
                                if (projectType === 2) {
                                    labelList.push(formatMessage({ id: "projects.subject.cohortName" }));
                                } else if (projectType === 3) {
                                    labelList.push(formatMessage({ id: "projects.subject.stageName" }));
                                }

                            }

                            let labelStr = labelList.join("/");
                            message.error(formatMessage({ id: "subject.edc.inconsistent.information1" }, { label: labelStr }));
                        } else {
                            factor_random_save();
                        }
                    },
                    cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                    okText: formatMessage({ id: "subject.edc.continue.submitting" }),
                });
            } else if (resp.data.siteStatus == 1) {     // 1中心匹配
                edcRandomLogic(resp);
            } else if (resp.data.siteStatus == 2) {     // 2中心不匹配
                CustomConfirmModal({
                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                    title: formatMessage({ id: "common.tips" }),
                    content: formatMessage({ id: "subject.edc.site.inconsistent" }),
                    okText: formatMessage({ id: "subject.edc.continue.submitting" }),
                    cancelText: formatMessage({ id: "common.cancel" }),
                    onOk: () =>
                        edcRandomLogic(resp)
                });
            } else {                                    // 3未获取到中心编号或者名称
                CustomConfirmModal({
                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                    title: formatMessage({ id: "common.tips" }),
                    content: formatMessage({ id: "subject.edc.site.empty" }),
                    okText: formatMessage({ id: "subject.edc.continue.submitting" }),
                    cancelText: formatMessage({ id: "common.cancel" }),
                    onOk: () =>
                        edcRandomLogic(resp)
                });
            }
        });
    };

    const edcRandomLogic = (resp: any) => {
        if (resp.data.edcSubjectStatus === 2) {          // 筛选失败
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "subject.random.fail" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.random.failure.filter.failed",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        } else if (resp.data.edcSubjectStatus === 5) {    // 已退出
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "subject.random.fail" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.random.failure.exit",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        } else if (resp.data.edcSubjectStatus === 6) {    // 完成研究
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "subject.random.fail" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.random.failure.complete.the.study",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        } else if (resp.data.edcSubjectStatus === 8) {    // 筛选中不可随机
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "subject.random.fail" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.random.failure.screen.failed",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        } else {
            // EDC如果配置了分层、表单、受试者号、cohort
            if (resp.data.edcOpenStratification || resp.data.edcOpenForm || resp.data.edcOpenSubjectNo || resp.data.edcOpenCohort) {
                edcTip(resp.data, "random");
            } else {
                factor_random_save();
            }
        }
    };

    const changeSite = (e: any) => {
        let siteNumber = selectSites.find((item) => item.id === e).number;
        setSiteNumber(siteNumber)
        setSiteId(e)
        form.setFieldsValue({ projectSiteId: e });

        // 查询表单
        getRegisterForm(e, null);
    }

    // 保存分层因素并随机
    const factor_random_save = () => {
        factorRandomRun({
            ...factorSave,
            id: id,
            lastId: selectCohorts.find((item) => item.id === cohortId) ? selectCohorts.find((item) => item.id === cohortId).last_id : nilObjectId,
        }).then((resp: any) => {
            message.success(formatMessage({ id: "subject.random.success" }));
            props.refresh();
            hide();
            setSubjectRecordShow(false);
        });
    };
    const checkPrefix = (value: any) => {
        if (value.target.value.indexOf(prefix) !== 0) {
            form.setFieldsValue({ shortname: prefix });
        }
    };
    const handlePrecision = (field:any) => {
        if (field.formatType === "numberLength"){
            return 0
        }else if (field.formatType === "decimalLength"){
            if (field.length) {
                // 将数字转换为字符串
                var str = field.length.toString();
                // 使用split()方法将整数部分和小数部分分开
                var parts = str.split('.');
                return  parts[1];
            }
        }else {
            return 0
        }
    };

    const handleStep = (field:any) => {
        if (field.formatType === "numberLength"){
            return 1
        }else if (field.formatType === "decimalLength"){
            if (field.length) {
                // 将数字转换为字符串
                var str = field.length.toString();
                // 使用split()方法将整数部分和小数部分分开
                var parts = str.split('.');

                return  10 ** (-parts[1]);
            }
        }else {
            return 1
        }
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <>
            <Modal
                className="custom-medium-modal"
                title={title}
                open={visible}
                onCancel={hide}
                centered
                maskClosable={false}
                keyboard={false}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{ marginRight: "12px" }}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            {
                                type === 3 ? (
                                    <Button
                                        onClick={factor_random}
                                        type="primary"
                                        loading={factorRandomLoading}
                                    >
                                        <FormattedMessage id="common.confirm" />
                                    </Button>
                                ) :
                                    pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario) ? (
                                        <Button
                                            loading={edcSubjectVerificationRunLoading}
                                            onClick={edc_random_verification_dj}
                                            type={"primary"}
                                            disabled={type === 2 ? send : false}
                                        >
                                            {formatMessage({ id: "common.ok" })}
                                        </Button>
                                    ) : (
                                        <Button
                                            onClick={save}
                                            type="primary"
                                            disabled={type === 2 ? send : false}
                                            loading={addLoading || updateLoading || getFormLoading}
                                        >
                                            <FormattedMessage id="common.ok" />
                                        </Button>
                                    )
                            }
                        </Col>
                    </Row>
                }
            >
                <StyleFrom
                    form={form}
                    onValuesChange={formChange}
                    layout={"vertical"}
                >
                    {
                        (projectType === 2 || projectType === 3) ?
                            (
                                (type === 1 && singleCohort === false) ?
                                    <>
                                        {
                                            projectType === 2 || inRandomIsolation(auth.project.info.number) ?
                                                <Form.Item rules={[{ required: true, message: formatMessage({ id: 'placeholder.select.common' }) + " " + formatMessage({ id: "projects.second" }) }]} name={"cohortId"}
                                                    style={{
                                                        marginBottom: "16px",
                                                    }} label={<FormattedMessage id="projects.second" />}
                                                >
                                                    <Select allowClear={true} onChange={(e) => {
                                                        setCohortId(e)
                                                        form.resetFields()
                                                        form.setFieldsValue({"cohortId":e})
                                                        getRegisterForm(siteId, e);
                                                    }}
                                                        placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                                    >
                                                        {
                                                            inRandomIsolation(auth.project.info.number) ?
                                                                selectCohorts?.map((item: any) => {
                                                                    return <Select.Option value={item.id}>{item.name}</Select.Option>
                                                                })
                                                                :
                                                            selectCohorts?.filter((it: any) => it.last_id === nilObjectId && (it.status === 2 || it.status === 5))?.map((item: any) => {
                                                                return <Select.Option value={item.id}>{item.name}</Select.Option>
                                                            })
                                                        }
                                                    </Select>
                                                </Form.Item>
                                                :
                                                <Form.Item
                                                    label={formatMessage({ id: "common.stage" })}
                                                    name={"cohortId"}
                                                    style={{
                                                        marginBottom: "16px",
                                                    }}
                                                >
                                                    {cohortName}
                                                </Form.Item>
                                        }
                                    </>
                                    :
                                    <Form.Item
                                        label={projectType === 2 || inRandomIsolation(auth.project.info.number) ? formatMessage({ id: "projects.second" }) : formatMessage({ id: "common.stage" })}
                                        name={"cohortId"}
                                        style={{
                                            marginBottom: "16px",
                                        }}
                                    >
                                        {cohortName}
                                    </Form.Item>
                            )
                            : null
                    }

                    {type === 3 ? (
                        <>
                            {randomInfo.length !== 0
                                ? randomInfo.map((field: any) => (
                                    <Form.Item
                                        style={{ marginBottom: "16px" }}
                                        key={field.label}
                                        label={(field.labelEn === "" && field.label === "") ? (<FormattedMessage id={"subject.number"} />) : ((field.labelEn !== "" && field.label === "") ? (g.lang === "en" ? field.labelEn : formatMessage({ id: "subject.number" })) : ((field.labelEn === "" && field.label !== "") ? field.label : ((g.lang === "en") ? field.labelEn : field.label)))}
                                        className="mar-ver-5"
                                    >
                                        <Row>{subjectRecord == null ? "" : subjectRecord[field.label]}</Row>
                                    </Form.Item>
                                ))
                                : null}
                        </>
                    ) : null}


                    {
                        type === 1 ? (singleSite === true ?
                            <>
                                <Form.Item
                                    label={formatMessage({ id: "common.site" })}
                                    name={"projectSiteId"}
                                    style={{
                                        marginBottom: "16px",
                                    }}
                                >
                                    {siteNumber + "-" + siteName}
                                </Form.Item>
                            </> :
                            <>
                                <Form.Item rules={[{ required: true, message: formatMessage({ id: 'placeholder.select.common' }) + " " + formatMessage({ id: 'common.site' }) }]} name={"projectSiteId"}
                                    style={{
                                        marginBottom: "16px",
                                    }} label={<FormattedMessage id="common.site" />}>
                                    <Select allowClear={true} onChange={(e) => changeSite(e)} placeholder={formatMessage({ id: 'placeholder.select.common' })}>
                                        {
                                            selectSites?.map((item: any) => {
                                                return <Select.Option value={item.id}>{item.number + "-" + item.name}</Select.Option>
                                            })
                                        }
                                    </Select>
                                </Form.Item>
                            </>) :
                            <>
                                <Form.Item
                                    label={formatMessage({ id: "common.site" })}
                                    name={"projectSiteId"}
                                    style={{
                                        marginBottom: "16px",
                                    }}
                                >
                                    {siteNumber + "-" + siteName}
                                </Form.Item>
                            </>
                    }

                    <Spin spinning={getFormLoading}>
                        {fields !== null
                            ? fields.map((field: any, index: number) => (
                                <>
                                    {field.type === "input" && field.calcType == null ? (
                                        <div>
                                            {field.name === "shortname" ?
                                                (
                                                    <Form.Item
                                                        label={(field.labelEn === "" && field.label === "") ? (<FormattedMessage id={"subject.number"} />) : ((field.labelEn !== "" && field.label === "") ? (g.lang === "en" ? field.labelEn : formatMessage({ id: "subject.number" })) : ((field.labelEn === "" && field.label !== "") ? field.label : ((g.lang === "en") ? field.labelEn : field.label)))}
                                                        name={field.name}
                                                        style={{
                                                            marginBottom:
                                                                index + 1 !==
                                                                    fields.length
                                                                    ? "16px"
                                                                    : "0px",
                                                        }}
                                                    >
                                                        <Input
                                                            disabled={field.disabled || !showSubjectNumber || (attribute?.info.isRandom === false && attribute?.info.dispensing === true && subjectRecord?.hasDispensing)|| (projectType === 3 && cohorts[0].id !== cohortId && !inRandomIsolation(auth.project.info.number)) ||(projectType === 2 && subjectRecord?.cohort?.type === 1 && reRandomCohorts[0]?.id !== cohortId)}
                                                            onChange={(
                                                                value: any
                                                            ) => {
                                                                checkPrefix(value);
                                                            }}
                                                            maxLength={field.digit}
                                                            allowClear
                                                            className="full-width"
                                                            placeholder={formatMessage({ id: 'common.required.prefix' })}
                                                        />
                                                    </Form.Item>
                                                ) : (
                                                    <Form.Item
                                                        style={{
                                                            marginBottom:
                                                                index + 1 !==
                                                                    fields.length
                                                                    ? "16px"
                                                                    : "0px",
                                                        }}
                                                        rules={[
                                                            {
                                                                required:
                                                                    field.required,
                                                            },
                                                            {
                                                                max: field.length,
                                                                message:
                                                                    formatMessage(
                                                                        {
                                                                            id: "form.control.type.format.limit",
                                                                        },
                                                                        {
                                                                            length: field.length,
                                                                        }
                                                                    ),
                                                            },
                                                        ]}
                                                        label={field.label}
                                                        name={field.name}
                                                    >
                                                        <Input
                                                            disabled={field.disabled}
                                                            allowClear
                                                            className="full-width"
                                                        />
                                                    </Form.Item>
                                                )}
                                        </div>
                                    ) : null}
                                    {field.type === "inputNumber" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[
                                                { required: field.required },
                                                {
                                                    validator: (_, value) => {
                                                        if (value !== null && value != undefined) {
                                                            if (field.range && field.range.min && field.range.min > value) {
                                                                return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                            }
                                                            if (field.range && field.range.max && field.range.max < value) {
                                                                return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                            }
                                                            if (field.formatType !== undefined && field.formatType === "decimalLength") {
                                                                if (field.length) {
                                                                    // 将数字转换为字符串
                                                                    var str = field.length.toString();
                                                                    // 使用split()方法将整数部分和小数部分分开
                                                                    var parts = str.split('.');
                                                                    //取整数部分
                                                                    let numberPart = parts[0];
                                                                    var valueDecimalPart = value.toString().split('.');
                                                                    if (valueDecimalPart && valueDecimalPart[0] && valueDecimalPart[0].length > numberPart) {
                                                                        return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                    }
                                                                    if (valueDecimalPart && valueDecimalPart[1] && parts[1] && valueDecimalPart[1].length > parts[1]) {
                                                                        return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                    }
                                                                }
                                                            } else if (field.formatType !== undefined && field.formatType === "numberLength") {
                                                                if (
                                                                    field.length &&
                                                                    value != null &&
                                                                    value.toString()
                                                                        .length >
                                                                    field.length
                                                                ) {
                                                                    return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                }
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                },
                                            ]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <InputNumber
                                                disabled={field.disabled}
                                                min={0}
                                                precision={handlePrecision(field)}
                                                step={handleStep(field)}
                                                className="full-width"
                                                placeholder={formatMessage({ id: 'common.required.prefix' })}

                                            />
                                        </Form.Item>
                                    ) : null}
                                    {field.type === "textArea" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[
                                                { required: field.required },
                                                {
                                                    max: field.length,
                                                    message: formatMessage(
                                                        {
                                                            id: "form.control.type.format.limit",
                                                        },
                                                        { length: field.length }
                                                    ),
                                                },
                                            ]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <Input.TextArea
                                                disabled={field.disabled}
                                                allowClear
                                                className="full-width"
                                                placeholder={formatMessage({ id: 'common.required.prefix' })}

                                            />
                                        </Form.Item>
                                    ) : null}

                                    {field.type === "select" ?
                                        <>
                                            {((projectType === 3 && !inRandomIsolation(auth.project.info.number) && cohorts[0].id !== cohortId)||(projectType === 2 && subjectRecord?.cohort?.type === 1 && reRandomCohorts[0]?.id !== cohortId))  && type === 2 && field.stratification  && (subjectRecord?.status === 1 || subjectRecord?.status === 4 || subjectRecord?.status === 7) && (form.getFieldValue(field.name) === undefined || form.getFieldValue(field.name) === null || form.getFieldValue(field.name) === "") ?
                                                null
                                                :
                                                (
                                                    <Form.Item
                                                        style={{
                                                            marginBottom:
                                                                index + 1 !== fields.length
                                                                    ? "16px"
                                                                    : "0px",
                                                        }}
                                                        rules={[{ required: field.required }]}
                                                        label={field.label}
                                                        name={field.name}
                                                    >
                                                        <Select
                                                            allowClear={true}
                                                            disabled={field.disabled}
                                                            className="full-width"
                                                            options={field.options}
                                                            placeholder={formatMessage({ id: 'placeholder.select.common' })}

                                                        ></Select>
                                                    </Form.Item>
                                                )
                                            }
                                        </>
                                        : null
                                    }

                                    {field.type === "checkbox" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <Checkbox.Group
                                                disabled={field.disabled}
                                                options={field.options}
                                            ></Checkbox.Group>
                                        </Form.Item>
                                    ) : null}

                                    {field.type === "radio" ?
                                        <>
                                            {((projectType === 3 && !inRandomIsolation(auth.project.info.number) && cohorts[0].id !== cohortId)||(projectType === 2 && subjectRecord?.cohort?.type === 1 && reRandomCohorts[0]?.id !== cohortId)) && type === 2 && (subjectRecord?.status === 1 || subjectRecord?.status === 4 || subjectRecord?.status === 7) && (form.getFieldValue(field.name) === undefined || form.getFieldValue(field.name) === null || form.getFieldValue(field.name) === "") ?
                                                null
                                                :
                                                (
                                                    <Form.Item
                                                        style={{
                                                            marginBottom:
                                                                index + 1 !== fields.length
                                                                    ? "16px"
                                                                    : "0px",
                                                        }}
                                                        rules={[{ required: field.required }]}
                                                        label={field.label}
                                                        name={field.name}
                                                    >
                                                        <div
                                                            onDoubleClick={() => {
                                                                form.resetFields([field.name])
                                                                let updateMap = new Map(radioMap)
                                                                updateMap.set(field.name, undefined)
                                                                setRadioMap(updateMap)
                                                            }}
                                                        >
                                                            <Radio.Group
                                                                value={form.getFieldValue(field.name)}
                                                                onChange={(e) => {
                                                                    form.setFieldsValue({ [field.name]: e.target.value })
                                                                    let updateMap = new Map(radioMap)
                                                                    updateMap.set(field.name, e.target.value)
                                                                    setRadioMap(updateMap)
                                                                }}
                                                                disabled={field.disabled}
                                                                options={field.options}
                                                            ></Radio.Group>
                                                        </div>
                                                    </Form.Item>
                                                )
                                            }
                                        </>
                                        : null
                                    }

                                    {field.type === "switch" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                            valuePropName="checked"
                                        >
                                            <Switch
                                                disabled={field.disabled}
                                                defaultChecked={false}
                                            />
                                        </Form.Item>
                                    ) : null}
                                    {field.type === "datePicker" && field.calcType == null ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <DatePickers
                                                showTime={field.dateFormat.includes("HH")}
                                                className="full-width"
                                                value={(e: any) => e}
                                                onChange={(e: any) => { }}
                                                disabled={field.disabled}
                                                disabledDate={(current: any) => {
                                                    if (field.dateRange?.min || field.dateRange?.max) {
                                                        let maxBool = false
                                                        let minBool = false
                                                        if (field.dateRange.max === "currentTime") {
                                                            if (field.dateFormat === "YYYY") {
                                                                maxBool = current.isAfter(dayjs(), 'year')
                                                            } else if (field.dateFormat === "YYYY-MM" || field.dateFormat === "MM-YYYY" || field.dateFormat === "MMM-YYYY") {
                                                                maxBool = current.isAfter(dayjs(), 'month')
                                                            } else if (field.dateFormat === "YYYY-MM-DD" || field.dateFormat === "DD-MM-YYYY" || field.dateFormat === "MM-DD-YYYY" || field.dateFormat === "DD-MMM-YYYY" || field.dateFormat === "MMM-DD-YYYY") {
                                                                maxBool = current.isAfter(dayjs(), 'day')
                                                            }
                                                        } else if (field.dateRange.max !== "") {
                                                            if (field.dateFormat === "YYYY") {
                                                                maxBool = current.isAfter(dayjs(field.dateRange.max, field.dateFormat), 'year')
                                                            } else if (field.dateFormat === "YYYY-MM" || field.dateFormat === "MM-YYYY" || field.dateFormat === "MMM-YYYY") {
                                                                maxBool = current.isAfter(dayjs(field.dateRange.max, field.dateFormat), 'month')
                                                            } else if (field.dateFormat === "YYYY-MM-DD" || field.dateFormat === "DD-MM-YYYY" || field.dateFormat === "MM-DD-YYYY" || field.dateFormat === "DD-MMM-YYYY" || field.dateFormat === "MMM-DD-YYYY") {
                                                                maxBool = current.isAfter(dayjs(field.dateRange.max, field.dateFormat), 'day')
                                                            }
                                                        }
                                                        if (field.dateRange.min !== "") {
                                                            minBool = current < dayjs(field.dateRange.min, field.dateFormat)
                                                        }
                                                        return maxBool || minBool
                                                    }
                                                    return false
                                                }}
                                                picker={field.datePicker}
                                                format={field.dateFormat}
                                            />
                                        </Form.Item>
                                    ) : null}
                                    {field.type === "timePicker" && field.timeFormat !== "HH:mm:ss" && field.timeFormat !== "HH:mm" && field.calcType == null ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <DatePickers
                                                showTime={field.timeFormat.includes("HH")}
                                                className="full-width"
                                                value={(e: any) => e}
                                                onChange={(e: any) => { }}
                                                disabled={field.disabled}
                                                disabledDate={(current: any) => {
                                                    if (field.dateRange?.min || field.dateRange?.max) {
                                                        let maxBool = false
                                                        let minBool = false
                                                        if (field.dateRange.max === "currentTime") {
                                                            maxBool = current > dayjs()
                                                        } else if (field.dateRange.max !== "") {
                                                            maxBool = current > dayjs(field.dateRange.max, field.timeFormat)
                                                        }
                                                        if (field.dateRange.min !== "") {
                                                            minBool = current < dayjs(field.dateRange.min, field.timeFormat)
                                                        }
                                                        return maxBool || minBool
                                                    }
                                                    return false
                                                }}
                                                style={{ width: "100%" }}
                                                picker={field.datePicker}
                                                format={field.timeFormat}
                                            />
                                        </Form.Item>
                                    ) : null}
                                    {field.type === "timePicker" && (field.timeFormat === "HH:mm:ss" || field.timeFormat === "HH:mm") && field.calcType == null ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <TimePicker
                                                disabled={field.disabled}
                                                className="full-width"
                                                format={field.timeFormat}
                                                disabledDate={(current: any) => {
                                                    if (field.dateRange?.min || field.dateRange?.max) {
                                                        let maxBool = false
                                                        let minBool = false
                                                        if (field.dateRange.max === "currentTime") {
                                                            maxBool = current > dayjs()
                                                        } else if (field.dateRange.max !== "") {
                                                            maxBool = current > dayjs(field.dateRange.max, field.timeFormat)
                                                        }
                                                        if (field.dateRange.min !== "") {
                                                            minBool = current < dayjs(field.dateRange.min, field.timeFormat)
                                                        }
                                                        return maxBool || minBool
                                                    }
                                                    return false
                                                }}
                                            />
                                        </Form.Item>
                                    ) : null}
                                </>
                            ))
                            : null}
                        {
                            attributeIsScreen &&
                                type === 2 && !((subjectRecord?.status === 4 && subjectRecord?.isScreen === null) || subjectRecord?.status === 1)
                                ?
                                (projectType === 3 && !inRandomIsolation(auth.project.info.number) || (projectType === 2 && subjectRecord?.cohort?.type === 1)) ?
                                    (projectType === 3 ?cohorts[0].id == cohortId : reRandomCohorts[0]?.id == cohortId) ?
                                        <>
                                            <Form.Item rules={[{ required: subjectRecord?.status === 7 || subjectRecord?.status === 8 }]}
                                                label={formatMessage({ id: "subject.screen.field" })} name={"isScreen"} style={{ marginTop: 16 }}>
                                                <Radio.Group disabled={!(subjectRecord?.status === 7 || subjectRecord?.status === 8)} onChange={e => setIsScreen(e.target.value)}>
                                                    <Radio value={true}><FormattedMessage id="common.yes" /></Radio>
                                                    <Radio value={false}><FormattedMessage id="common.no" /></Radio>
                                                </Radio.Group>
                                            </Form.Item>
                                        </>
                                        :
                                        <>
                                            <Form.Item label={formatMessage({ id: "subject.screen.field" })} name={"isScreen"} style={{ marginTop: 16 }}>
                                                <Radio.Group disabled={true} onChange={e => setIsScreen(e.target.value)}>
                                                    <Radio value={true}><FormattedMessage id="common.yes" /></Radio>
                                                    <Radio value={false}><FormattedMessage id="common.no" /></Radio>
                                                </Radio.Group>
                                            </Form.Item>
                                        </>
                                    :
                                    <>
                                        <Form.Item rules={[{ required: subjectRecord?.status === 7 || subjectRecord?.status === 8 }]}
                                            label={formatMessage({ id: "subject.screen.field" })} name={"isScreen"} style={{ marginTop: 16 }}>
                                            <Radio.Group disabled={!(subjectRecord?.status === 7 || subjectRecord?.status === 8)} onChange={e => setIsScreen(e.target.value)}>
                                                <Radio value={true}><FormattedMessage id="common.yes" /></Radio>
                                                <Radio value={false}><FormattedMessage id="common.no" /></Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                    </>
                                : null

                        }
                        {
                            attributeIsScreen &&
                                type === 2 &&
                                (
                                    subjectRecord?.status === 3 ||
                                    (subjectRecord?.status === 4 && subjectRecord?.isScreen !== null) ||
                                    subjectRecord?.status === 5 ||
                                    subjectRecord?.status === 6 ||
                                    subjectRecord?.status === 7 ||
                                    subjectRecord?.status === 8 ||
                                    subjectRecord?.status === 9) ?
                                <>
                                    <Form.Item label={formatMessage({ id: 'subject.screen.time.field' })} name="screenTime"
                                        style={{ marginBottom: 16 }}>
                                        <DatePickers
                                            disabledDate={(current: any) => {
                                                return current < dayjs().year(2020).month(0).date(0) || current > dayjs();
                                            }}
                                            format={"YYYY-MM-DD"}
                                            placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                            className="full-width" />
                                    </Form.Item>
                                    <Form.Item label={formatMessage({ id: 'subject.screen.ICF.field' })} name="icfTime"
                                        className="mar-ver-5">
                                        <DatePickers
                                            disabledDate={(current: any) => {
                                                return current < dayjs().year(2020).month(0).date(0) || current > dayjs();
                                            }}
                                            placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                            className="full-width" />
                                    </Form.Item>
                                </>
                                : null

                        }
                        {
                            (subjectRecord?.signOutPeople !== nilObjectId && (subjectRecord?.status === 4 || subjectRecord?.status === 6 || subjectRecord?.status === 9)) || subjectRecord?.status === 5 || (subjectRecord?.status === 4 && subjectRecord?.pvUnblindingStatus) ?
                                <Form.Item label={formatMessage({ id: 'subject.stop.time' })} name="signOutRealTime"
                                    className="mar-ver-5">
                                    <DatePickers
                                        disabledDate={(current: any) => {
                                            return current < dayjs().year(2020).month(0).date(0) || current > dayjs();
                                        }}
                                        format={"YYYY-MM-DD"} placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                        className="full-width" />
                                </Form.Item>
                                : null}
                        {
                            (subjectRecord?.signOutPeople !== nilObjectId && (subjectRecord?.status === 4 || subjectRecord?.status === 6 || subjectRecord?.status === 9)) || subjectRecord?.status === 5 || (subjectRecord?.status === 4 && subjectRecord?.pvUnblindingStatus) ?
                                <Form.Item label={formatMessage({ id: 'subject.reason' })} name="reason" rules={[{ required: true }]} >
                                    <Input.TextArea placeholder={formatMessage({ id: "common.required.prefix" })} allowClear className="full-width" showCount />
                                </Form.Item> : null
                        }
                        {
                            (subjectRecord?.finishPeople !== nilObjectId && subjectRecord?.status === 9) ?
                                <Form.Item label={formatMessage({ id: 'report.attributes.random.finish.remark' })} name="finishRemark" >
                                    <Input.TextArea placeholder={formatMessage({ id: "common.required.prefix" })} allowClear className="full-width" showCount />
                                </Form.Item> : null
                        }
                    </Spin>
                </StyleFrom>
            </Modal>

            <Modal
                className="custom-small-modal"
                centered
                keyboard={false}
                title={formatMessage({ id: "common.tips" })}
                closable={false}
                maskClosable={false}
                open={subjectRecordShow}
                destroyOnClose={true}
                footer={
                    <Row gutter={8} justify="end">
                        <Col>
                            <Button onClick={hide_confirm}>
                                {formatMessage({ id: "common.cancel" })}
                            </Button>
                            {pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario) ? (
                                <Button
                                    loading={edcSubjectVerificationRunLoading}
                                    onClick={edc_random_verification}
                                    type={"primary"}
                                >
                                    {formatMessage({ id: "common.ok" })}
                                </Button>
                            ) : (
                                <Button
                                    loading={factorRandomLoading}
                                    onClick={factor_random_save}
                                    type={"primary"}
                                >
                                    {formatMessage({
                                        id: "subject.confirm.random.button",
                                    })}
                                </Button>
                            )}
                        </Col>
                    </Row>
                }
            >
                {/*<Row> <Text strong><InfoCircleTwoTone className="mar-top-5 mar-rgt-5 mar-lft-5"/> {formatMessage({ id: 'subject.random.select.confirm' })}</Text></Row>*/}
                <TitleContent style={{ height: intl.locale === "zh" ? "" : "54px" }}>
                    <Col
                        style={{
                            fontSize: "14px",
                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                            marginTop: "6px",
                            marginLeft: "8px",
                            marginRight: "8px",
                            fontWeight: "400",
                        }}
                    >
                        {" "}
                        <InfoCircleFilled style={{ color: "#FFAE00" }} />{" "}
                        {formatMessage({ id: "subject.confirm.random" })}{" "}
                    </Col>
                </TitleContent>
                <StyleFrom
                    layout={"vertical"}
                    style={{ marginTop: "24px" }}
                // labelCol={{style:{width:"180px", whiteSpace: "pre-line"}}}
                >
                    <Title
                        name={formatMessage({ id: "subject.random.info" })}
                    />
                    {cohortName !== null &&
                        cohortName !== "" &&
                        projectType === 2 && (
                            <Form.Item
                                label={formatMessage({
                                    id: "projects.second",
                                })}
                                style={{
                                    marginBottom: "10px",
                                    marginLeft: "24px",
                                }}
                            >
                                {cohortName}
                            </Form.Item>
                        )}
                    {cohortName !== null &&
                        cohortName !== "" &&
                        projectType === 3 && (
                            <Form.Item
                                label={formatMessage({
                                    id: "common.stage",
                                })}
                                style={{
                                    marginBottom: "10px",
                                    marginLeft: "24px",
                                }}
                            >
                                {cohortName}
                            </Form.Item>
                        )}
                    {subjectRecord
                        ? subjectRecord.form?.fields.map((it: any, index: any) => (
                            <>
                                <Form.Item
                                    label={(it.labelEn === "" && it.label === "") ? (<FormattedMessage id={"subject.number"} />) : ((it.labelEn !== "" && it.label === "") ? (g.lang === "en" ? it.labelEn : formatMessage({ id: "subject.number" })) : ((it.labelEn === "" && it.label !== "") ? it.label : ((g.lang === "en") ? it.labelEn : it.label)))}
                                    style={{
                                        marginBottom: "10px",
                                        marginLeft: "24px",
                                    }}
                                >
                                    {/* {(subjectRecord[it.labelEn] === "" && subjectRecord[it.label] === "")?(<FormattedMessage id={"subject.number"} />):((subjectRecord[it.labelEn] !== "" && subjectRecord[it.label] === "")?(g.lang === "en"?subjectRecord[it.labelEn]:formatMessage({ id: "subject.number" })):((subjectRecord[it.labelEn] === "" && subjectRecord[it.label] !== "")?subjectRecord[it.label]:((g.lang === "en")?subjectRecord[it.labelEn]:subjectRecord[it.label])))} */}
                                    {subjectRecord[it.name]}
                                </Form.Item>
                            </>
                        ))
                        : null}
                </StyleFrom>
            </Modal>

            <EdcVerificationTip
                bind={edc_verification_tip_pt}
                refresh={props.refresh}
                random_save={factor_random_save}
                register_update={save}
            />
        </>
    );
};

const StyleFrom = styled(Form)`
    .ant-form-item-label label {
        height: auto;
        color: #4e5969;
        opacity: 0.8;
    }
`;
const TitleContent = styled.div`
    height: 32px;
    width: 552px;
    left: 444px;
    top: 322px;
    border-radius: 2px;
    background: rgba(255, 174, 0, 0.06);
    border: 0.5px solid rgba(255, 174, 0, 0.5);
    border-radius: 2px;
`;

const ItemDiv = styled.div`
    ant-row ant-form-item {
        margin-bottom: 24px;
    }
`;
