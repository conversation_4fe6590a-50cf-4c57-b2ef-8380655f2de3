package test

import (
	"clinflash-irt/service"
	"context"
	"testing"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestCheckOutWindowApprovalProcess 测试超窗审批流程判断
func TestCheckOutWindowApprovalProcess(t *testing.T) {
	// 创建gin上下文
	ctx, _ := gin.CreateTestContext(nil)
	
	// 模拟用户信息
	// user := models.User{
	// 	ID:         primitive.NewObjectID(),
	// 	CustomerID: primitive.NewObjectID(),
	// 	Name:       "测试用户",
	// 	Email:      "<EMAIL>",
	// }
	// ctx.Set("user", user)

	// 测试参数
	subjectID, _ := primitive.ObjectIDFromHex("请替换为实际的受试者ID")
	visitCycleInfoID, _ := primitive.ObjectIDFromHex("请替换为实际的访视周期信息ID")
	dispensingID := primitive.NilObjectID // 可选

	// 模拟请求数据
	requestData := map[string]interface{}{
		"visitLabels":    []string{"label1", "label2"},
		"medicineInfo":   []map[string]interface{}{},
		"formulaMedicine": []map[string]interface{}{},
		"remark":         "测试发药",
	}

	reason := "受试者因特殊情况需要超窗发药"
	remark := "经医生确认，受试者状态良好，可以进行超窗发药"

	// 调用方法
	canProceed, response, err := service.CheckOutWindowApprovalProcess(
		ctx, subjectID, visitCycleInfoID, dispensingID, requestData, reason, remark)

	if err != nil {
		t.Fatalf("检查超窗审批流程失败: %v", err)
	}

	t.Logf("是否可以继续处理: %v", canProceed)
	t.Logf("审批响应: %+v", response)

	if !canProceed && response.NeedApproval {
		t.Logf("需要走审批流程，审批编号: %s", response.ApprovalNumber)
	} else if canProceed {
		t.Logf("可以直接处理，无需审批")
	}
}

// TestOutWindowApprovalScenarios 测试不同场景下的超窗审批
func TestOutWindowApprovalScenarios(t *testing.T) {
	scenarios := []struct {
		name             string
		subjectID        string
		visitCycleInfoID string
		dispensingID     string
		expectedResult   bool // true表示期望可以继续处理，false表示期望需要审批
		description      string
	}{
		{
			name:             "正常窗口期内访视",
			subjectID:        "请替换为窗口期内的受试者ID",
			visitCycleInfoID: "请替换为对应的访视周期信息ID",
			dispensingID:     "",
			expectedResult:   true,
			description:      "访视在正常窗口期内，应该直接允许",
		},
		{
			name:             "超窗访视需要审批",
			subjectID:        "请替换为超窗的受试者ID",
			visitCycleInfoID: "请替换为对应的访视周期信息ID",
			dispensingID:     "",
			expectedResult:   false,
			description:      "访视超出窗口期，需要走审批流程",
		},
		{
			name:             "已有待审批申请",
			subjectID:        "请替换为已有待审批申请的受试者ID",
			visitCycleInfoID: "请替换为对应的访视周期信息ID",
			dispensingID:     "",
			expectedResult:   false,
			description:      "已有待审批的申请，不应该重复创建",
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			ctx, _ := gin.CreateTestContext(nil)
			
			// 转换ID
			subjectID, err := primitive.ObjectIDFromHex(scenario.subjectID)
			if err != nil {
				t.Skipf("跳过测试 %s: 无效的受试者ID", scenario.name)
				return
			}
			
			visitCycleInfoID, err := primitive.ObjectIDFromHex(scenario.visitCycleInfoID)
			if err != nil {
				t.Skipf("跳过测试 %s: 无效的访视周期信息ID", scenario.name)
				return
			}
			
			var dispensingID primitive.ObjectID
			if scenario.dispensingID != "" {
				dispensingID, err = primitive.ObjectIDFromHex(scenario.dispensingID)
				if err != nil {
					t.Skipf("跳过测试 %s: 无效的发药记录ID", scenario.name)
					return
				}
			}

			// 模拟请求数据
			requestData := map[string]interface{}{
				"scenario": scenario.name,
				"test":     true,
			}

			// 调用方法
			canProceed, response, err := service.CheckOutWindowApprovalProcess(
				ctx, subjectID, visitCycleInfoID, dispensingID, requestData, 
				"测试原因", "测试备注")

			if err != nil {
				t.Errorf("测试 %s 失败: %v", scenario.name, err)
				return
			}

			t.Logf("测试 %s:", scenario.name)
			t.Logf("  描述: %s", scenario.description)
			t.Logf("  期望结果: %v, 实际结果: %v", scenario.expectedResult, canProceed)
			t.Logf("  审批响应: %+v", response)

			// 这里可以添加更具体的断言
			// if canProceed != scenario.expectedResult {
			//     t.Errorf("测试 %s 结果不符合期望: 期望 %v, 实际 %v", 
			//         scenario.name, scenario.expectedResult, canProceed)
			// }
		})
	}
}

// TestOutWindowApprovalIntegration 集成测试：完整的超窗审批流程
func TestOutWindowApprovalIntegration(t *testing.T) {
	ctx, _ := gin.CreateTestContext(nil)
	
	// 测试参数 - 请根据实际情况修改
	subjectID, _ := primitive.ObjectIDFromHex("请替换为实际的受试者ID")
	visitCycleInfoID, _ := primitive.ObjectIDFromHex("请替换为实际的访视周期信息ID")
	
	// 模拟完整的发药请求数据
	requestData := map[string]interface{}{
		"visitLabels": []string{},
		"medicineInfo": []map[string]interface{}{
			{
				"medicineId": "药物ID1",
				"count":      1,
				"label":      "发药标签1",
			},
		},
		"formulaMedicine": []map[string]interface{}{},
		"remark":          "集成测试发药",
		"outVisit":        true, // 标记为计划外访视
	}

	reason := "受试者因医疗需要进行超窗访视"
	remark := "经主治医生评估，受试者需要在窗口期外进行访视和发药"

	t.Log("开始集成测试...")

	// 第一次调用：应该创建审批申请
	canProceed1, response1, err := service.CheckOutWindowApprovalProcess(
		ctx, subjectID, visitCycleInfoID, primitive.NilObjectID, requestData, reason, remark)

	if err != nil {
		t.Fatalf("第一次调用失败: %v", err)
	}

	t.Logf("第一次调用结果:")
	t.Logf("  可以继续处理: %v", canProceed1)
	t.Logf("  审批响应: %+v", response1)

	// 如果需要审批，记录审批编号
	var approvalNumber string
	if !canProceed1 && response1.NeedApproval {
		approvalNumber = response1.ApprovalNumber
		t.Logf("  创建了审批申请，编号: %s", approvalNumber)
	}

	// 第二次调用：应该返回已有的审批申请
	canProceed2, response2, err := service.CheckOutWindowApprovalProcess(
		ctx, subjectID, visitCycleInfoID, primitive.NilObjectID, requestData, reason, remark)

	if err != nil {
		t.Fatalf("第二次调用失败: %v", err)
	}

	t.Logf("第二次调用结果:")
	t.Logf("  可以继续处理: %v", canProceed2)
	t.Logf("  审批响应: %+v", response2)

	// 验证第二次调用不会创建新的审批申请
	if !canProceed1 && response1.NeedApproval && !canProceed2 && response2.NeedApproval {
		if response1.ApprovalNumber == response2.ApprovalNumber {
			t.Log("✓ 验证通过：第二次调用返回了相同的审批编号，没有重复创建")
		} else {
			t.Errorf("✗ 验证失败：第二次调用创建了新的审批申请")
		}
	}

	t.Log("集成测试完成")
}

// BenchmarkCheckOutWindowApprovalProcess 性能测试
func BenchmarkCheckOutWindowApprovalProcess(b *testing.B) {
	ctx, _ := gin.CreateTestContext(nil)
	
	subjectID, _ := primitive.ObjectIDFromHex("请替换为实际的受试者ID")
	visitCycleInfoID, _ := primitive.ObjectIDFromHex("请替换为实际的访视周期信息ID")
	
	requestData := map[string]interface{}{
		"test": "benchmark",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := service.CheckOutWindowApprovalProcess(
			ctx, subjectID, visitCycleInfoID, primitive.NilObjectID, 
			requestData, "性能测试", "基准测试")
		
		if err != nil {
			b.Fatalf("性能测试失败: %v", err)
		}
	}
}
