package service

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// OutWindowApprovalRequest 超窗审批请求结构
type OutWindowApprovalRequest struct {
	SubjectID       primitive.ObjectID `json:"subjectId" bson:"subject_id"`
	VisitCycleInfoID primitive.ObjectID `json:"visitCycleInfoId" bson:"visit_cycle_info_id"`
	DispensingID    primitive.ObjectID `json:"dispensingId" bson:"dispensing_id"`
	RequestData     interface{}        `json:"requestData" bson:"request_data"` // 原始请求数据
	Reason          string             `json:"reason" bson:"reason"`            // 申请原因
	Remark          string             `json:"remark" bson:"remark"`            // 备注
	ApprovalType    int                `json:"approvalType" bson:"approval_type"` // 审批类型：1-超窗发药审批
}

// OutWindowApprovalResponse 超窗审批响应结构
type OutWindowApprovalResponse struct {
	NeedApproval    bool   `json:"needApproval"`    // 是否需要审批
	ApprovalNumber  string `json:"approvalNumber"`  // 审批编号（如果需要审批）
	Message         string `json:"message"`         // 提示信息
}

// CheckOutWindowApprovalProcess 判断是否超窗走审批申请流程
// 参数:
//   - ctx: gin上下文
//   - subjectID: 受试者ID
//   - visitCycleInfoID: 访视周期信息ID
//   - dispensingID: 发药记录ID（可选）
//   - requestData: 请求数据（如果是申请流程则保存）
//   - reason: 申请原因（如果是申请流程）
//   - remark: 备注（如果是申请流程）
// 返回:
//   - bool: true表示可以继续处理，false表示需要走审批流程
//   - *OutWindowApprovalResponse: 审批响应信息
//   - error: 错误信息
func CheckOutWindowApprovalProcess(ctx *gin.Context, subjectID, visitCycleInfoID primitive.ObjectID, 
	dispensingID primitive.ObjectID, requestData interface{}, reason, remark string) (bool, *OutWindowApprovalResponse, error) {
	
	// 1. 获取受试者信息
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(ctx, bson.M{"_id": subjectID}).Decode(&subject)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	// 2. 获取访视周期信息
	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(ctx, bson.M{
		"env_id":   subject.EnvironmentID,
		"cohort_id": subject.CohortID,
	}).Decode(&visitCycle)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	// 3. 查找对应的访视信息
	var visitInfo models.VisitCycleInfo
	found := false
	for _, info := range visitCycle.Infos {
		if info.ID == visitCycleInfoID {
			visitInfo = info
			found = true
			break
		}
	}
	if !found {
		return false, &OutWindowApprovalResponse{
			NeedApproval: false,
			Message:      "访视信息不存在",
		}, nil
	}

	// 4. 获取属性配置
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(ctx, bson.M{
		"env_id":   subject.EnvironmentID,
		"cohort_id": subject.CohortID,
	}).Decode(&attribute)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	// 5. 获取项目时区信息
	timeZone, err := tools.GetTimeZone(subject.ProjectID)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	// 6. 计算访视窗口期
	period, err := calculateVisitPeriod(ctx, subject, visitInfo, visitCycle, attribute, timeZone)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	// 7. 判断是否超窗
	if !period.OutSize && !period.OutSizeWindow {
		// 不是超窗访视，直接返回true
		return true, &OutWindowApprovalResponse{
			NeedApproval: false,
			Message:      "访视在窗口期内，无需审批",
		}, nil
	}

	// 8. 检查是否已有待审批的申请
	existingApproval, err := checkExistingApproval(ctx, subjectID, visitCycleInfoID, dispensingID)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	if existingApproval != nil {
		// 已有待审批的申请
		return false, &OutWindowApprovalResponse{
			NeedApproval:   true,
			ApprovalNumber: existingApproval.Number,
			Message:        "已有待审批的超窗访视申请",
		}, nil
	}

	// 9. 检查是否配置了超窗审批流程
	needApproval, err := checkOutWindowApprovalConfig(ctx, subject.ProjectID, subject.EnvironmentID)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	if !needApproval {
		// 未配置审批流程，直接允许
		return true, &OutWindowApprovalResponse{
			NeedApproval: false,
			Message:      "超窗访视但未配置审批流程，直接允许",
		}, nil
	}

	// 10. 创建审批申请
	approvalNumber, err := createOutWindowApproval(ctx, OutWindowApprovalRequest{
		SubjectID:       subjectID,
		VisitCycleInfoID: visitCycleInfoID,
		DispensingID:    dispensingID,
		RequestData:     requestData,
		Reason:          reason,
		Remark:          remark,
		ApprovalType:    1, // 超窗发药审批
	})
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	return false, &OutWindowApprovalResponse{
		NeedApproval:   true,
		ApprovalNumber: approvalNumber,
		Message:        "超窗访视需要审批，已创建审批申请",
	}, nil
}

// calculateVisitPeriod 计算访视窗口期
func calculateVisitPeriod(ctx *gin.Context, subject models.Subject, visitInfo models.VisitCycleInfo, 
	visitCycle models.VisitCycle, attribute models.Attribute, timeZone float64) (models.Period, error) {
	
	// 获取受试者的发药记录来计算窗口期
	var dispensings []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Find(ctx, bson.M{
		"subject_id": subject.ID,
	})
	if err != nil {
		return models.Period{}, errors.WithStack(err)
	}
	defer cursor.Close(ctx)
	
	err = cursor.All(ctx, &dispensings)
	if err != nil {
		return models.Period{}, errors.WithStack(err)
	}

	// 使用现有的窗口期计算逻辑
	var lastTime time.Duration
	var interval time.Duration
	afterRandom := false

	// 找到最后一次发药时间
	for _, dispensing := range dispensings {
		if dispensing.Status == 2 && dispensing.DispensingTime > lastTime {
			lastTime = dispensing.DispensingTime
		}
		if dispensing.VisitInfo.Random {
			afterRandom = true
		}
	}

	// 计算窗口期
	period := tools.HandleVisitDate(afterRandom, visitCycle.VisitType, visitInfo, 
		subject.RandomTime, lastTime, 0, timeZone, &interval)

	return period, nil
}

// checkExistingApproval 检查是否已有待审批的申请
func checkExistingApproval(ctx *gin.Context, subjectID, visitCycleInfoID, dispensingID primitive.ObjectID) (*models.ApprovalProcess, error) {
	filter := bson.M{
		"type":   4, // 假设4为超窗访视审批类型
		"status": 0, // 待审批状态
		"out_window_approval_data.subject_id": subjectID,
		"out_window_approval_data.visit_cycle_info_id": visitCycleInfoID,
	}

	if !dispensingID.IsZero() {
		filter["out_window_approval_data.dispensing_id"] = dispensingID
	}

	var approval models.ApprovalProcess
	err := tools.Database.Collection("approval_process").FindOne(ctx, filter).Decode(&approval)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, errors.WithStack(err)
	}

	return &approval, nil
}

// checkOutWindowApprovalConfig 检查是否配置了超窗审批流程
func checkOutWindowApprovalConfig(ctx *gin.Context, projectID, envID primitive.ObjectID) (bool, error) {
	// 这里可以根据实际的配置表结构来查询
	// 假设有一个配置表存储审批流程配置
	var config map[string]interface{}
	err := tools.Database.Collection("approval_config").FindOne(ctx, bson.M{
		"project_id": projectID,
		"env_id":     envID,
		"type":       "out_window_visit", // 超窗访视审批配置
		"enabled":    true,
	}).Decode(&config)
	
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return false, nil // 未配置审批流程
		}
		return false, errors.WithStack(err)
	}

	return true, nil
}

// createOutWindowApproval 创建超窗审批申请
func createOutWindowApproval(ctx *gin.Context, req OutWindowApprovalRequest) (string, error) {
	// 获取当前用户信息
	userInterface, exists := ctx.Get("user")
	if !exists {
		return "", errors.New("用户信息不存在")
	}
	user := userInterface.(models.User)

	// 生成审批编号
	approvalNumber, err := GetApprovalNumber(ctx, req.SubjectID) // 假设这个函数存在
	if err != nil {
		return "", errors.WithStack(err)
	}

	now := time.Now()
	applicationTime := time.Duration(now.Unix())

	// 将请求数据序列化为JSON
	requestDataJSON, err := json.Marshal(req.RequestData)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 创建审批流程记录
	approvalProcess := models.ApprovalProcess{
		Number:                  approvalNumber,
		Name:                    "超窗访视发药审批",
		Type:                    4, // 假设4为超窗访视审批类型
		Status:                  0, // 待审批
		EstimatedCompletionTime: applicationTime + time.Duration(24*time.Hour), // 预期24小时内完成
		ApplicationTime:         applicationTime,
		ApplicationBy:           user.ID,
		ApplicationByEmail:      user.Email,
		ApprovalStatus:          0, // 提交申请
		OutWindowApprovalData: bson.M{
			"subject_id":         req.SubjectID,
			"visit_cycle_info_id": req.VisitCycleInfoID,
			"dispensing_id":      req.DispensingID,
			"request_data":       string(requestDataJSON),
			"reason":             req.Reason,
			"remark":             req.Remark,
			"approval_type":      req.ApprovalType,
		},
	}

	// 插入审批流程记录
	_, err = tools.Database.Collection("approval_process").InsertOne(ctx, approvalProcess)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 保存审批编号
	approvalNumberSave := models.ApprovalNumber{
		ID:         primitive.NewObjectID(),
		CustomerID: user.CustomerID,
		ProjectID:  req.SubjectID, // 这里可能需要调整为实际的项目ID
		Number:     approvalNumber,
	}
	_, err = tools.Database.Collection("approval_number").InsertOne(ctx, approvalNumberSave)
	if err != nil {
		return "", errors.WithStack(err)
	}

	return approvalNumber, nil
}
