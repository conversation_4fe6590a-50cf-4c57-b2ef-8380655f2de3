package service

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strconv"
	"time"
)

type ProjectDynamicsService struct{}

func (s ProjectDynamicsService) All(ctx *gin.Context) (interface{}, error) {
	envID := ctx.Query("envId")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	envOID, _ := primitive.ObjectIDFromHex(envID)
	type result struct {
		Total int           `json:"total"`
		Data  []interface{} `json:"data"`
	}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envP
	if env.Name != "PROD" {
		return result{
			Total: 0,
			Data:  []interface{}{},
		}, nil
	}

	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}
	roleId, err := tools.Role(ctx)
	if err != nil {
		return nil, err
	}
	//查询角色绑定中心/库房过滤数据
	role := models.ProjectRolePermission{}
	err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleId}).Decode(&role)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	_, needShowForecast := slice.Find(role.Permissions, func(index int, item string) bool {
		return item == "operation.supply.site.medicine.summary.formula"
	})

	// cohort 合并查询

	OIDs := []primitive.ObjectID{envOID}
	for _, cohort := range env.Cohorts {
		OIDs = append(OIDs, cohort.ID)
	}
	filter := bson.M{"oid": bson.M{"$in": OIDs}}
	if !needShowForecast {
		filter["type_tran"] = bson.M{"$ne": "project_dynamics_type_forecast"}
	}
	opt := &options.FindOptions{
		Sort: bson.D{{"time", -1}},
	}
	dynamics := make([]models.ProjectDynamics, 0)
	cursor, err := tools.Database.Collection("project_dynamics").Find(nil, filter, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &dynamics)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	siteOrDepotIds := make([]primitive.ObjectID, 0)
	userIds := make([]primitive.ObjectID, 0)
	userDepots := make([]models.UserDepot, 0)
	userSites := make([]models.UserSite, 0)
	if role.Scope != "study" {
		if role.Scope == "site" {
			cursor, err = tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": envOID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			sites := slice.Filter(userSites, func(index int, item models.UserSite) bool {
				return item.UserID == me.ID
			})
			for _, site := range sites {
				siteOrDepotIds = append(siteOrDepotIds, site.SiteID)
			}
			users := slice.Filter(userSites, func(index int, item models.UserSite) bool {
				siteIds := slice.Map(sites, func(index int, item models.UserSite) primitive.ObjectID {
					return item.SiteID
				})
				siteIds = slice.Unique(siteIds)
				return slice.Contain(siteIds, item.SiteID)
			})
			for _, user := range users {
				userIds = append(userIds, user.UserID)
			}
		}
		if role.Scope == "depot" {
			cursor, err = tools.Database.Collection("user_depot").Find(nil, bson.M{"env_id": envOID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userDepots)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			depots := slice.Filter(userDepots, func(index int, item models.UserDepot) bool {
				return item.UserID == me.ID
			})
			for _, depot := range depots {
				siteOrDepotIds = append(siteOrDepotIds, depot.DepotID)
			}
			users := slice.Filter(userDepots, func(index int, item models.UserDepot) bool {
				depotIds := slice.Map(depots, func(index int, item models.UserDepot) primitive.ObjectID {
					return item.DepotID
				})
				depotIds = slice.Unique(depotIds)
				return slice.Contain(depotIds, item.DepotID)
			})
			for _, user := range users {
				userIds = append(userIds, user.UserID)
			}
		}

		//过滤过期订单相关
		orderDy := slice.Filter(dynamics, func(index int, item models.ProjectDynamics) bool {
			return item.TypeTran == "project_dynamics_type_overtime"
		})
		orderNumbers := make([]string, 0)
		orderNumbers = slice.Map(orderDy, func(index int, item models.ProjectDynamics) string {
			return item.ContentData["orderNumber"].(string)
		})
		orderNumbers = slice.Unique(orderNumbers)
		orders := make([]models.MedicineOrder, 0)
		orderOpt := &options.FindOptions{Projection: bson.M{
			"_id":          1,
			"order_number": 1,
			"type":         1,
			"send_id":      1,
			"receive_id":   1,
		}}
		if len(orderNumbers) > 0 {
			cursor, err = tools.Database.Collection("medicine_order").Find(nil, bson.M{"order_number": bson.M{"$in": orderNumbers}}, orderOpt)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &orders)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		//过滤受试者相关
		subjectDy := slice.Filter(dynamics, func(index int, item models.ProjectDynamics) bool {
			return item.TypeTran == "project_dynamics_type_emergency_unblinding"
		})
		subjectIds := make([]primitive.ObjectID, 0)
		subjectIds = slice.Map(subjectDy, func(index int, item models.ProjectDynamics) primitive.ObjectID {
			return item.ContentData["subjectId"].(primitive.ObjectID)
		})
		subjects := make([]models.Subject, 0)
		if len(subjectIds) > 0 {
			cursor, err = tools.Database.Collection("subject").Find(nil, bson.M{"_id": bson.M{"$in": subjectIds}, "deleted": bson.M{"$ne": true}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err := cursor.All(nil, &subjects)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//过滤受试者相关(pv揭盲)
		subjectDyPv := slice.Filter(dynamics, func(index int, item models.ProjectDynamics) bool {
			return item.TypeTran == "project_dynamics_type_emergency_unblinding_pv"
		})
		subjectPvIds := make([]primitive.ObjectID, 0)
		subjectPvIds = slice.Map(subjectDyPv, func(index int, item models.ProjectDynamics) primitive.ObjectID {
			return item.ContentData["subjectId"].(primitive.ObjectID)
		})
		subjectsPv := make([]models.Subject, 0)
		if len(subjectIds) > 0 {
			cursor, err = tools.Database.Collection("subject").Find(nil, bson.M{"_id": bson.M{"$in": subjectPvIds}, "deleted": bson.M{"$ne": true}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err := cursor.All(nil, &subjectsPv)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//过滤中心/库房数据权限
		dynamics = slice.Filter(dynamics, func(index int, item models.ProjectDynamics) bool {
			if item.TypeTran == "project_dynamics_type_enter_site" {
				siteId := item.ContentData["siteId"].(primitive.ObjectID)
				_, ok := slice.Find(userSites, func(index int, i models.UserSite) bool {
					return i.SiteID == siteId
				})
				return ok
			} else if item.TypeTran == "project_dynamics_type_bind_storehouse" {
				storehouseId := item.ContentData["storehouseId"].(primitive.ObjectID)
				_, ok := slice.Find(userDepots, func(index int, i models.UserDepot) bool {
					return i.DepotID == storehouseId
				})
				return ok
			} else if item.TypeTran == "project_dynamics_type_role_assignment" {
				if role.Scope == "study" {
					return true
				} else {
					userId := item.ContentData["userId"].(primitive.ObjectID)
					_, ok := slice.Find(userIds, func(index int, i primitive.ObjectID) bool {
						return i == userId
					})
					return ok
				}
			} else if item.TypeTran == "project_dynamics_type_overtime" {
				op, ok := slice.Find(orders, func(index int, i models.MedicineOrder) bool {
					return i.OrderNumber == item.ContentData["orderNumber"].(string)
				})
				if ok {
					order := *op
					_, is := slice.Find(siteOrDepotIds, func(index int, i primitive.ObjectID) bool {
						return order.SendID == i || order.ReceiveID == i
					})
					return is
				} else {
					return false
				}
			} else if item.TypeTran == "project_dynamics_type_emergency_unblinding" {
				subjectId := item.ContentData["subjectId"].(primitive.ObjectID)
				subjectP, ok := slice.Find(subjects, func(index int, i models.Subject) bool {
					return i.ID == subjectId
				})
				subject := *subjectP
				if ok {
					_, is := slice.Find(userSites, func(index int, s models.UserSite) bool {
						return s.SiteID == subject.ProjectSiteID
					})
					return is
				} else {
					return false
				}
			} else if item.TypeTran == "project_dynamics_type_emergency_unblinding_pv" {
				subjectId := item.ContentData["subjectId"].(primitive.ObjectID)
				subjectP, ok := slice.Find(subjects, func(index int, i models.Subject) bool {
					return i.ID == subjectId
				})
				if subjectP != nil {
					subject := *subjectP
					if ok {
						_, is := slice.Find(userSites, func(index int, s models.UserSite) bool {
							return s.SiteID == subject.ProjectSiteID
						})
						return is
					} else {
						return false
					}
				} else {
					return false
				}
			} else if item.TypeTran == "project_dynamics_type_alert_storehouse" {
				storehouseId := item.ContentData["storehouseId"].(primitive.ObjectID)
				_, is := slice.Find(userDepots, func(index int, i models.UserDepot) bool {
					return i.DepotID == storehouseId
				})
				return is
			} else if item.TypeTran == "project_dynamics_type_forecast" {
				noticeSite := item.ContentData["result"].(primitive.A)
				if role.Scope != "study" {
					matchNoticeSite := slice.Filter(noticeSite, func(index int, item interface{}) bool {
						_, ok := slice.Find(siteOrDepotIds, func(index int, siteOID primitive.ObjectID) bool {
							return item.(map[string]interface{})["id"] == siteOID
						})
						return ok
					})
					if len(matchNoticeSite) > 0 {
						sites := primitive.A{}
						for _, site := range matchNoticeSite {
							sites = append(sites, site)
						}
						item.ContentData["result"] = sites
						return true
					}
				} else {
					return true
				}

			} else if item.TypeTran == "project_dynamics_type_visit" {
				siteId := item.ContentData["siteId"].(primitive.ObjectID)
				_, ok := slice.Find(siteOrDepotIds, func(index int, i primitive.ObjectID) bool {
					return i == siteId
				})
				return ok
			}
			return false
		})
	}
	siteDy := slice.Filter(dynamics, func(index int, item models.ProjectDynamics) bool {
		return item.TypeTran == "project_dynamics_type_enter_site"
	})
	siteIds := slice.Map(siteDy, func(index int, item models.ProjectDynamics) primitive.ObjectID {
		return item.ContentData["siteId"].(primitive.ObjectID)
	})
	siteIds = slice.Unique(siteIds)
	sites := make([]models.ProjectSite, 0)
	if len(siteIds) > 0 {
		cursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"_id": bson.M{"$in": siteIds}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &sites)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	resp := slice.Map(dynamics, func(index int, item models.ProjectDynamics) interface{} {
		highlight := ""
		var tooltip interface{}
		if item.TypeTran == "project_dynamics_type_enter_site" {
			highlight = item.ContentData["email"].(string)
			siteId := item.ContentData["siteId"].(primitive.ObjectID)
			sp, b := slice.Find(sites, func(index int, item models.ProjectSite) bool {
				return siteId == item.ID
			})
			if b {
				site := *sp
				siteName := models.GetProjectSiteName(ctx, site)
				item.ContentData["siteName"] = siteName
			}
		} else if item.TypeTran == "project_dynamics_type_bind_storehouse" {
			highlight = item.ContentData["email"].(string)
		} else if item.TypeTran == "project_dynamics_type_role_assignment" {
			highlight = item.ContentData["email"].(string)
		} else if item.TypeTran == "project_dynamics_type_overtime" {
			highlight = item.ContentData["orderNumber"].(string)
		} else if item.TypeTran == "project_dynamics_type_overtime_recovery" {
			highlight = item.ContentData["orderNumber"].(string)
		} else if item.TypeTran == "project_dynamics_type_emergency_unblinding" {
			highlight = item.ContentData["subjectName"].(string)
		} else if item.TypeTran == "project_dynamics_type_emergency_unblinding_pv" {
			highlight = item.ContentData["subjectName"].(string)
		} else if item.TypeTran == "project_dynamics_type_alert_storehouse" {
			highlight = item.ContentData["storehouseName"].(string)
		} else if item.TypeTran == "project_dynamics_type_forecast" {
			noticeSite := item.ContentData["result"].(primitive.A)
			siteName := noticeSite[0].(map[string]interface{})["name"]
			if ctx.GetHeader("Accept-Language") == "en" {
				siteName = noticeSite[0].(map[string]interface{})["name_en"]
			}
			item.ContentData["siteName"] = siteName
			highlight = siteName.(string)
			tooltip = noticeSite

		}
		return models.ProjectDynamicsResp{
			ID:        item.ID,
			TypeTran:  item.TypeTran,
			Time:      item.Time,
			Content:   locales.Tr(ctx, item.ContentTran, item.ContentData),
			Highlight: highlight,
			Tooltip:   tooltip,
		}
	})

	r := result{
		Total: len(dynamics),
		Data:  resp,
	}
	if len(dynamics) > start*limit {
		r.Data = resp[(start-1)*limit : start*limit]
	} else {
		r.Data = resp[(start-1)*limit:]
	}
	return r, nil
}

func (s ProjectDynamicsService) Analysis(ctx *gin.Context, envID string, roleID string) (models.ProjectAnalysisResp, error) {

	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{"env_id": envOID}

	allTasksMatch := bson.M{"env_id": envOID}

	timeOutTaskMatch := bson.M{
		"env_id":                    envOID,
		"estimated_completion_time": bson.M{"$lt": time.Now().Unix()},
		"status":                    0,
		"approval_status":           0,
	}
	todoTaskCountMatch := bson.M{
		"env_id":          envOID,
		"status":          0,
		"approval_status": 0,
	}

	//当前系统操作人
	user, err := tools.Me(ctx)
	isApprovalPermission, err := tools.IsRoleHasPermission(roleID, "operation.supply.shipment.approval")
	if err != nil {
		return models.ProjectAnalysisResp{}, errors.WithStack(err)

	}
	roleOID, _ := primitive.ObjectIDFromHex(roleID)
	//判断当前的人是否有审批流程权限,如果没有只可以查看自己的数据
	if !isApprovalPermission {
		timeOutTaskMatch["application_by"] = user.ID
		timeOutTaskMatch["application_role_id"] = roleOID
		allTasksMatch["application_by"] = user.ID
		allTasksMatch["application_role_id"] = roleOID
		todoTaskCountMatch["application_by"] = user.ID
		todoTaskCountMatch["application_role_id"] = roleOID
	}
	// study 角色不过滤
	study, err := tools.RoleIsStudy(roleID)
	siteOID := []primitive.ObjectID{}
	if !study {
		siteOIDs, err := tools.GetRoleSite(ctx, envID)
		DepotOIDs, err := tools.GetRoleDepot(ctx, envID)
		siteOID = append(siteOID, siteOIDs...)
		siteOID = append(siteOID, DepotOIDs...)
		if err != nil {
			return models.ProjectAnalysisResp{}, errors.WithStack(err)

		}

		if len(siteOID) > 0 {
			match["receive_id"] = bson.M{"$in": siteOID}
			//workTimeOutMatch["data.receive_id"] = bson.M{"$in": siteOID}
			//todoTaskCountMatch["data.receive_id"] = bson.M{"$in": siteOID}
			//allTaksMatch["data.receive_id"] = bson.M{"$in": siteOID}
		} else {
			return models.ProjectAnalysisResp{}, nil

		}

	}

	orderCount, err := tools.Database.Collection("medicine_order").CountDocuments(nil, match)
	if err != nil {
		return models.ProjectAnalysisResp{}, errors.WithStack(err)
	}
	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.order.timeout"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.ProjectAnalysisResp{}, errors.WithStack(err)
	}
	// 超时时间为0时 不需要检查超时订单
	var timeOutCount int64
	if noticeConfig.TimeoutDays != 0 {
		//eg: 超时时间为2时  查询超过两天前的已确认 运送中订单
		now := time.Duration(time.Now().AddDate(0, 0, noticeConfig.TimeoutDays*-1).Unix())
		timeOutCountMatch := bson.M{
			"env_id":          envOID,
			"meta.updated_at": bson.M{"$lt": now},
			"status":          bson.M{"$in": bson.A{1, 2}},
		}

		pipeline := mongo.Pipeline{
			{{"$match", timeOutCountMatch}},
			{{"$lookup", bson.M{
				"from": "subject", // 关联的集合名称
				"let": bson.M{
					"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
				},
				"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
					bson.M{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": bson.A{
									bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
									bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
								},
							},
						},
					},
				},
				"as": "subject", // 关联结果的字段名
			}}},
		}
		if len(siteOID) > 0 {
			pipeline = append(pipeline, bson.D{{"$match", bson.M{"$or": bson.A{
				bson.M{"receive_id": bson.M{"$in": siteOID}},
				bson.M{"subject.project_site_id": bson.M{"$in": siteOID}},
				bson.M{"send_id": bson.M{"$in": siteOID}, "type": bson.M{"$in": bson.A{3, 4}}},
			}}}})
		}
		cursor, err := tools.Database.Collection("medicine_order").Aggregate(nil, pipeline)
		if err != nil {
			return models.ProjectAnalysisResp{}, err
		}
		var res []map[string]interface{}
		err = cursor.All(nil, &res)
		if err != nil {
			return models.ProjectAnalysisResp{}, errors.WithStack(err)
		}
		timeOutCount = int64(len(res))
	}

	workCount, err := tools.Database.Collection("approval_process").CountDocuments(nil, allTasksMatch)
	if err != nil {
		return models.ProjectAnalysisResp{}, errors.WithStack(err)
	}

	workTimeOutCounts, err := tools.Database.Collection("approval_process").CountDocuments(nil, timeOutTaskMatch)

	todoTaskCount, err := tools.Database.Collection("approval_process").CountDocuments(nil, todoTaskCountMatch)
	if err != nil {
		return models.ProjectAnalysisResp{}, errors.WithStack(err)
	}

	r := models.ProjectAnalysisResp{
		OrderCount:        orderCount,
		OrderTimeOutCount: timeOutCount,
		WorkCount:         workCount,
		WorkTimeOutCount:  workTimeOutCounts,
		TodoTaskCount:     todoTaskCount,
	}

	return r, nil
}

func (s ProjectDynamicsService) SubjectStatistics(ctx *gin.Context, envID string, roleID string, cohortID []string) (models.SubjectStatisticsResp, error) {
	return GetSubjectStatisticsData(ctx, envID, roleID, cohortID)
}

func (s ProjectDynamicsService) SubjectSiteStatistics(ctx *gin.Context, envID string, roleID string, cohortID []string) ([]models.SubjectSiteStatisticsResp, error) {
	return GetSubjectSiteStatisticsData(ctx, envID, roleID, cohortID)
}

func GetSubjectSiteStatisticsData(ctx *gin.Context, envID string, roleID string, cohortIDs []string) ([]models.SubjectSiteStatisticsResp, error) {

	envOID, _ := primitive.ObjectIDFromHex(envID)
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	match := bson.M{"env_id": envOID, "deleted": bson.M{"$ne": true}}
	if len(cohortIDs) > 0 {
		var cohortOIDs []primitive.ObjectID
		for _, cohortID := range cohortIDs {
			cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
			cohortOIDs = append(cohortOIDs, cohortOID)
		}
		match["cohort_id"] = bson.M{"$in": cohortOIDs}
	}
	// study 角色不过滤
	study, err := tools.RoleIsStudy(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	depot, err := tools.RoleIsDepot(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if depot {
		return nil, nil
	}
	if !study {
		siteOID, err := tools.GetRoleSite(ctx, envID)
		if err != nil {
			return nil, errors.WithStack(err)

		}
		if len(siteOID) > 0 {
			match["project_site_id"] = bson.M{"$in": siteOID}
		} else {
			return nil, nil
		}
	}

	subjectSiteStatisticsResp := make([]models.SubjectSiteStatisticsResp, 0)

	type subjectSiteStatistic struct {
		ID                     primitive.ObjectID `bson:"_id"`
		ProjectSiteID          primitive.ObjectID `bson:"project_site_id"`
		CohortID               primitive.ObjectID `bson:"cohort_id"`
		Number                 string             `bson:"number"`
		Name                   string             `bson:"name"`
		Status                 int                `bson:"status"`
		Group                  string             `bson:"group"`
		IsScreen               *bool              `bson:"is_screen"`
		PvUnblindingStatus     int                `bson:"pv_unblinding_status"`
		UrgentUnblindingStatus int                `bson:"urgent_unblinding_status"`
	}
	subjectSiteStatistics := make([]subjectSiteStatistic, 0)
	pipeline := mongo.Pipeline{
		{{"$match", match}},
		{{"$lookup", bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{"$unwind", "$project_site"}},
		{{"$project", bson.M{
			"_id":                      1,
			"project_site_id":          "$project_site_id",
			"cohort_id":                1,
			"number":                   "$project_site.number",
			"name":                     models.ProjectSiteNameLookUpBson(ctx),
			"status":                   1,
			"is_screen":                1,
			"pv_unblinding_status":     1,
			"group":                    1,
			"urgent_unblinding_status": 1,
		}}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipeline)
	err = cursor.All(nil, &subjectSiteStatistics)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	numberSort := slice.Map(subjectSiteStatistics, func(index int, item subjectSiteStatistic) string {
		return item.Number
	})
	numberSort = slice.Unique(numberSort)
	env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	cohorts := env.Cohorts
	reqCohorts := slice.Filter(cohorts, func(index int, item models.Cohort) bool {
		return slice.Contain(cohortIDs, item.ID.Hex())
	})
	haveReRandom := false
	if project.Type == 3 && !tools.InRandomIsolation(project.Number) {
		haveReRandom = true
	} else if project.Type == 2 {
		_, b := slice.Find(reqCohorts, func(index int, item models.Cohort) bool {
			return item.Type == 1
		})
		if b {
			haveReRandom = true
		}
	}
	stageCohort1 := make([]primitive.ObjectID, 0)
	stageCohort2 := make([]primitive.ObjectID, 0)
	commonCohort := make([]primitive.ObjectID, 0)
	if haveReRandom && !tools.InRandomIsolation(project.Number) {
		if project.Type == 3 {
			cohort1P, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.LastID.IsZero()
			})
			cohort2P, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return !item.LastID.IsZero()
			})
			if cohort1P != nil {
				cohort1 := *cohort1P
				stageCohort1 = append(stageCohort1, cohort1.ID)
			}
			if cohort2P != nil {
				cohort2 := *cohort2P
				stageCohort2 = append(stageCohort2, cohort2.ID)

			}
		} else {
			commonCohorts := slice.Filter(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.Type == 0
			})
			commonCohort = slice.Map(commonCohorts, func(index int, item models.Cohort) primitive.ObjectID {
				return item.ID
			})
			stageCohort1, _ = models.GetReRandomCohortFirstIds(project, envID)
			stageCohort2, _ = models.GetReRandomCohortSecondIds(project, envID)
		}
	}
	isDispensingSubjectIds := make([]primitive.ObjectID, 0)
	attributes := make([]models.Attribute, 0)
	cursor, err = tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	registerSubjects := slice.Filter(subjectSiteStatistics, func(index int, item subjectSiteStatistic) bool {
		attributeP, _ := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
			return attribute.CohortID == item.CohortID
		})
		return attributeP.AttributeInfo.Dispensing && !attributeP.AttributeInfo.Random
	})
	registerSubjectIds := slice.Map(registerSubjects, func(index int, item subjectSiteStatistic) primitive.ObjectID {
		return item.ID
	})
	dispensings := make([]models.Dispensing, 0)
	opts := &options.FindOptions{
		Projection: bson.M{
			"_id":        1,
			"subject_id": 1,
		},
	}
	cursor, err = tools.Database.Collection("dispensing").Find(nil, bson.M{"subject_id": bson.M{"$in": registerSubjectIds}, "status": 2}, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	isDispensingSubjectIds = slice.Map(dispensings, func(index int, item models.Dispensing) primitive.ObjectID {
		return item.SubjectID
	})
	isDispensingSubjectIds = slice.Unique(isDispensingSubjectIds)

	for _, number := range numberSort {
		v := slice.Filter(subjectSiteStatistics, func(index int, item subjectSiteStatistic) bool {
			return item.Number == number
		})
		if haveReRandom {
			tobeRandom := 0
			register := 0
			register1 := 0
			randomCount := 0
			randomCount1 := 0
			randomCount2 := 0
			exit := 0
			exit1 := 0
			exit2 := 0
			beforeRandomExit := 0
			beforeRandomExit1 := 0
			beforeRandomExit2 := 0
			beforeScreenSuccessExit := 0
			beforeScreenSuccessExit1 := 0
			unBlind := 0
			unBlind1 := 0
			unBlind2 := 0
			unBlindFinish := 0
			unBlindFinish1 := 0
			unBlindFinish2 := 0
			pv := 0
			pv1 := 0
			pv2 := 0
			screenSuccess := 0
			screenSuccess1 := 0
			screenFail := 0
			screenFail1 := 0
			finish := 0
			finish1 := 0
			finish2 := 0
			tobeRandom = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 1 || item.Status == 7) && slice.Contain(stageCohort2, item.CohortID)
			})
			register = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 1 && slice.Contain(commonCohort, item.CohortID)
			})
			register1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 1 && slice.Contain(stageCohort1, item.CohortID)
			})
			randomCount = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 3 && slice.Contain(commonCohort, item.CohortID)
			})
			randomCount1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 3 && slice.Contain(stageCohort1, item.CohortID)
			})
			randomCount2 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 3 && slice.Contain(stageCohort2, item.CohortID)
			})
			exit = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && slice.Contain(commonCohort, item.CohortID)
			})
			exit1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && slice.Contain(stageCohort1, item.CohortID)
			})
			exit2 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && slice.Contain(stageCohort2, item.CohortID)
			})
			beforeRandomExit = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && item.Group == "" && slice.Contain(commonCohort, item.CohortID)
			})
			beforeRandomExit1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && item.Group == "" && slice.Contain(stageCohort1, item.CohortID)
			})
			beforeRandomExit2 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && item.Group == "" && slice.Contain(stageCohort2, item.CohortID)
			})
			beforeScreenSuccessExit = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && item.IsScreen == nil && item.Group == "" && slice.Contain(commonCohort, item.CohortID)
			})
			beforeScreenSuccessExit1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && item.IsScreen == nil && item.Group == "" && slice.Contain(stageCohort1, item.CohortID)
			})
			unBlind = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.UrgentUnblindingStatus == 1 && slice.Contain(commonCohort, item.CohortID)
			})
			unBlind1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.UrgentUnblindingStatus == 1 && slice.Contain(stageCohort1, item.CohortID)
			})
			unBlind2 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.UrgentUnblindingStatus == 1 && slice.Contain(stageCohort2, item.CohortID)
			})
			unBlindFinish = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.UrgentUnblindingStatus == 1 && item.Status == 9 && slice.Contain(commonCohort, item.CohortID)
			})
			unBlindFinish1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.UrgentUnblindingStatus == 1 && item.Status == 9 && slice.Contain(stageCohort1, item.CohortID)
			})
			unBlindFinish2 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.UrgentUnblindingStatus == 1 && item.Status == 9 && slice.Contain(stageCohort2, item.CohortID)
			})
			pv = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.PvUnblindingStatus == 1 && slice.Contain(commonCohort, item.CohortID)
			})
			pv1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.PvUnblindingStatus == 1 && slice.Contain(stageCohort1, item.CohortID)
			})
			pv2 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.PvUnblindingStatus == 1 && slice.Contain(stageCohort2, item.CohortID)
			})
			screenSuccess = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 7 && slice.Contain(commonCohort, item.CohortID)
			})
			screenSuccess1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 7 && slice.Contain(stageCohort1, item.CohortID)
			})
			screenFail = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 8 && slice.Contain(commonCohort, item.CohortID)
			})
			screenFail1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 8 && slice.Contain(stageCohort1, item.CohortID)
			})
			finish = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 9 && slice.Contain(commonCohort, item.CohortID)
			})
			finish1 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 9 && slice.Contain(stageCohort1, item.CohortID)
			})
			finish2 = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 9 && slice.Contain(stageCohort2, item.CohortID)
			})
			var subjectStatusCount []models.SubjectStatusCount
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 10,
				Count:  int32(tobeRandom),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 1,
				Count:  int32(register + register1 + randomCount + randomCount1 + exit + exit1 + unBlind + unBlind1 + screenSuccess + screenSuccess1 + screenFail + screenFail1 + finish + finish1 - unBlindFinish - unBlindFinish1),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 3,
				Count:  int32(randomCount + randomCount1 + exit + exit1 + unBlind + unBlind1 - beforeRandomExit - beforeRandomExit1 + finish + finish1 + randomCount2 + exit2 + unBlind2 - beforeRandomExit2 + finish2 - unBlindFinish - unBlindFinish1 - unBlindFinish2),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 5,
				Count:  int32(exit + exit1 + exit2),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 6,
				Count:  int32(unBlind + unBlind1 + unBlind2),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 7,
				Count:  int32(screenSuccess + screenSuccess1 + randomCount + randomCount1 + exit + exit1 - beforeScreenSuccessExit - beforeScreenSuccessExit1 + unBlind + unBlind1 + finish + finish1 - unBlindFinish - unBlindFinish1),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 8,
				Count:  int32(screenFail + screenFail1),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 9,
				Count:  int32(finish + finish1 + finish2),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 12,
				Count:  int32(pv + pv1 + pv2),
			})
			subjectSiteStatisticsResp = append(subjectSiteStatisticsResp, models.SubjectSiteStatisticsResp{
				ID:                 primitive.NilObjectID,
				Number:             v[0].Number,
				Name:               v[0].Name,
				SubjectStatusCount: subjectStatusCount,
			})
		} else {
			register := 0
			randomCount := 0
			exit := 0
			beforeRandomExit := 0
			beforeScreenSuccessExit := 0
			unBlind := 0
			unBlindFinish := 0
			pv := 0
			screenSuccess := 0
			screenFail := 0
			finish := 0
			join := 0
			register = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 1
			})
			randomCount = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 3
			})
			exit = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 4 || item.Status == 5
			})
			beforeRandomExit = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && item.Group == ""
			})
			beforeScreenSuccessExit = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return (item.Status == 4 || item.Status == 5) && item.IsScreen == nil && item.Group == ""
			})
			unBlind = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.UrgentUnblindingStatus == 1
			})
			unBlindFinish = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.UrgentUnblindingStatus == 1 && item.Status == 9
			})
			pv = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.PvUnblindingStatus == 1
			})
			screenSuccess = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 7
			})
			screenFail = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 8
			})
			finish = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				return item.Status == 9
			})
			join = slice.Count(v, func(index int, item subjectSiteStatistic) bool {
				_, b := slice.Find(isDispensingSubjectIds, func(index int, id primitive.ObjectID) bool {
					return id == item.ID
				})
				return (item.Status == 1 || item.Status == 7) && b
			})
			var subjectStatusCount []models.SubjectStatusCount
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 1,
				Count:  int32(register + randomCount + exit + unBlind + screenSuccess + screenFail + finish - unBlindFinish),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 3,
				Count:  int32(randomCount + exit + unBlind - beforeRandomExit + finish - unBlindFinish),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 5,
				Count:  int32(exit),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 6,
				Count:  int32(unBlind),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 7,
				Count:  int32(screenSuccess + randomCount + exit - beforeScreenSuccessExit + unBlind + finish - unBlindFinish),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 8,
				Count:  int32(screenFail),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 9,
				Count:  int32(finish),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 11,
				Count:  int32(join),
			})
			subjectStatusCount = append(subjectStatusCount, models.SubjectStatusCount{
				Status: 12,
				Count:  int32(pv),
			})
			subjectSiteStatisticsResp = append(subjectSiteStatisticsResp, models.SubjectSiteStatisticsResp{
				ID:                 primitive.NilObjectID,
				Number:             v[0].Number,
				Name:               v[0].Name,
				SubjectStatusCount: subjectStatusCount,
			})
		}
	}
	return subjectSiteStatisticsResp, nil
}
func GetSubjectStatisticsData(ctx *gin.Context, envID string, roleID string, cohortIDs []string) (models.SubjectStatisticsResp, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{"env_id": envOID, "deleted": bson.M{"$ne": true}}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return models.SubjectStatisticsResp{}, errors.WithStack(err)
	}
	env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	cohorts := env.Cohorts
	reqCohorts := slice.Filter(cohorts, func(index int, item models.Cohort) bool {
		return slice.Contain(cohortIDs, item.ID.Hex())
	})
	haveReRandom := false
	if project.Type == 3 && !tools.InRandomIsolation(project.Number) {
		haveReRandom = true
	} else if project.Type == 2 {
		_, b := slice.Find(reqCohorts, func(index int, item models.Cohort) bool {
			return item.Type == 1
		})
		if b {
			haveReRandom = true
		}
	}
	stageCohort1 := make([]primitive.ObjectID, 0)
	stageCohort2 := make([]primitive.ObjectID, 0)
	commonCohort := make([]primitive.ObjectID, 0)

	if haveReRandom && !tools.InRandomIsolation(project.Number) {
		if project.Type == 3 {
			cohort1P, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.LastID.IsZero()
			})
			cohort2P, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return !item.LastID.IsZero()
			})
			if cohort1P != nil {
				cohort1 := *cohort1P
				stageCohort1 = append(stageCohort1, cohort1.ID)
			}
			if cohort2P != nil {
				cohort2 := *cohort2P
				stageCohort2 = append(stageCohort2, cohort2.ID)

			}
		} else {
			commonCohorts := slice.Filter(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.Type == 0
			})
			commonCohort = slice.Map(commonCohorts, func(index int, item models.Cohort) primitive.ObjectID {
				return item.ID
			})
			stageCohort1, _ = models.GetReRandomCohortFirstIds(project, envID)
			stageCohort2, _ = models.GetReRandomCohortSecondIds(project, envID)
		}
	}
	var resp models.SubjectStatisticsResp
	if len(cohortIDs) > 0 {
		var cohortOIDs []primitive.ObjectID
		for _, cohortID := range cohortIDs {
			cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
			cohortOIDs = append(cohortOIDs, cohortOID)
		}
		match["cohort_id"] = bson.M{"$in": cohortOIDs}
	}
	// study 角色不过滤
	study, err := tools.RoleIsStudy(roleID)
	if err != nil {
		return resp, errors.WithStack(err)
	}
	depot, err := tools.RoleIsDepot(roleID)
	if err != nil {
		return resp, errors.WithStack(err)
	}
	if depot {
		return resp, nil
	}
	if !study {
		siteOID, err := tools.GetRoleSite(ctx, envID)
		if err != nil {
			return resp, errors.WithStack(err)

		}
		if len(siteOID) > 0 {
			match["project_site_id"] = bson.M{"$in": siteOID}
		} else {
			return resp, nil

		}
	}
	pipeline := mongo.Pipeline{
		{{"$match", match}},
		{{"$project", bson.M{
			"_id":                      1,
			"cohort_id":                1,
			"status":                   1,
			"is_screen":                1,
			"group":                    1,
			"pv_unblinding_status":     1,
			"urgent_unblinding_status": 1,
		}}},
	}
	subjects := make([]models.Subject, 0)
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipeline)
	err = cursor.All(nil, &subjects)
	if err != nil {
		return resp, errors.WithStack(err)
	}
	attributes := make([]models.Attribute, 0)
	cursor, err = tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return resp, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return resp, errors.WithStack(err)
	}

	registerSubjects := slice.Filter(subjects, func(index int, item models.Subject) bool {
		attributeP, _ := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
			return attribute.CohortID == item.CohortID
		})
		return attributeP.AttributeInfo.Dispensing && !attributeP.AttributeInfo.Random
	})
	registerSubjectIds := slice.Map(registerSubjects, func(index int, item models.Subject) primitive.ObjectID {
		return item.ID
	})
	dispensings := make([]models.Dispensing, 0)
	opts := &options.FindOptions{
		Projection: bson.M{
			"_id":        1,
			"subject_id": 1,
		},
	}
	cursor, err = tools.Database.Collection("dispensing").Find(nil, bson.M{"subject_id": bson.M{"$in": registerSubjectIds}, "status": bson.M{"$in": []int{2, 3}}}, opts)
	if err != nil {
		return resp, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return resp, errors.WithStack(err)
	}
	isDispensingSubjectIds := slice.Map(dispensings, func(index int, item models.Dispensing) primitive.ObjectID {
		return item.SubjectID
	})
	isDispensingSubjectIds = slice.Unique(isDispensingSubjectIds)

	if haveReRandom {
		toBeRandom := 0
		register := 0
		register1 := 0
		randomCount := 0
		randomCount1 := 0
		randomCount2 := 0
		exit := 0
		exit1 := 0
		exit2 := 0
		beforeRandomExit := 0
		beforeRandomExit1 := 0
		beforeRandomExit2 := 0
		beforeScreenSuccessExit := 0
		beforeScreenSuccessExit1 := 0
		//beforeScreenSuccessExit2 := 0
		unBlind := 0
		unBlind1 := 0
		unBlind2 := 0
		unBlindFinish := 0
		unBlindFinish1 := 0
		unBlindFinish2 := 0
		pv := 0
		pv1 := 0
		pv2 := 0
		screenSuccess := 0
		screenSuccess1 := 0
		screenFail := 0
		screenFail1 := 0
		finish := 0
		finish1 := 0
		finish2 := 0

		toBeRandom = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 1 || item.Status == 7) && slice.Contain(stageCohort2, item.CohortID)
		})
		register = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 1 && slice.Contain(commonCohort, item.CohortID)
		})
		register1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 1 && slice.Contain(stageCohort1, item.CohortID)
		})
		screenSuccess = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 7 && slice.Contain(commonCohort, item.CohortID)
		})
		screenSuccess1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 7 && slice.Contain(stageCohort1, item.CohortID)
		})
		screenFail = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 8 && slice.Contain(commonCohort, item.CohortID)
		})
		screenFail1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 8 && slice.Contain(stageCohort1, item.CohortID)
		})
		randomCount = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 3 && slice.Contain(commonCohort, item.CohortID)
		})
		randomCount1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 3 && slice.Contain(stageCohort1, item.CohortID)
		})
		randomCount2 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 3 && slice.Contain(stageCohort2, item.CohortID)
		})
		exit = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && slice.Contain(commonCohort, item.CohortID)
		})
		exit1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && slice.Contain(stageCohort1, item.CohortID)
		})
		exit2 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && slice.Contain(stageCohort2, item.CohortID)
		})
		beforeRandomExit = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && item.Group == "" && slice.Contain(commonCohort, item.CohortID)
		})
		beforeRandomExit1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && item.Group == "" && slice.Contain(stageCohort1, item.CohortID)
		})
		beforeRandomExit2 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && item.Group == "" && slice.Contain(stageCohort2, item.CohortID)
		})
		beforeScreenSuccessExit = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && item.IsScreen == nil && item.Group == "" && slice.Contain(commonCohort, item.CohortID)
		})
		beforeScreenSuccessExit1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && item.IsScreen == nil && item.Group == "" && slice.Contain(stageCohort1, item.CohortID)
		})
		unBlind = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.UrgentUnblindingStatus == 1 && slice.Contain(commonCohort, item.CohortID)
		})
		unBlind1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.UrgentUnblindingStatus == 1 && slice.Contain(stageCohort1, item.CohortID)
		})
		unBlind2 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.UrgentUnblindingStatus == 1 && slice.Contain(stageCohort2, item.CohortID)
		})
		unBlindFinish = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.UrgentUnblindingStatus == 1 && item.Status == 9 && slice.Contain(commonCohort, item.CohortID)
		})
		unBlindFinish1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.UrgentUnblindingStatus == 1 && item.Status == 9 && slice.Contain(stageCohort1, item.CohortID)
		})
		unBlindFinish2 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.UrgentUnblindingStatus == 1 && item.Status == 9 && slice.Contain(stageCohort2, item.CohortID)
		})
		pv = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.PvUnblindingStatus == 1 && slice.Contain(commonCohort, item.CohortID)
		})
		pv1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.PvUnblindingStatus == 1 && slice.Contain(stageCohort1, item.CohortID)
		})
		pv2 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.PvUnblindingStatus == 1 && slice.Contain(stageCohort2, item.CohortID)
		})
		finish = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 9 && slice.Contain(commonCohort, item.CohortID)
		})
		finish1 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 9 && slice.Contain(stageCohort1, item.CohortID)
		})
		finish2 = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 9 && slice.Contain(stageCohort2, item.CohortID)
		})
		resp.Register = register + register1 + randomCount + randomCount1 + exit + exit1 + unBlind + unBlind1 + screenSuccess + screenSuccess1 + screenFail + screenFail1 + finish + finish1 - unBlindFinish - unBlindFinish1
		resp.Random = randomCount + randomCount1 + exit + exit1 + unBlind + unBlind1 + finish + finish1 - beforeRandomExit - beforeRandomExit1 + randomCount2 + exit2 + unBlind2 + finish2 - beforeRandomExit2 - unBlindFinish - unBlindFinish1 - unBlindFinish2
		resp.Exit = exit + exit1 + exit2
		resp.UnBlind = unBlind + unBlind1 + unBlind2
		resp.ScreenSuccess = screenSuccess + screenSuccess1 + randomCount + randomCount1 + exit + exit1 - beforeScreenSuccessExit - beforeScreenSuccessExit1 + unBlind + unBlind1 + finish + finish1 - unBlindFinish - unBlindFinish1
		resp.ScreenFail = screenFail + screenFail1
		resp.Finish = finish + finish1 + finish2
		resp.ToBeRandom = toBeRandom
		resp.PvUnBlind = pv + pv1 + pv2
	} else {
		register := 0
		randomCount := 0
		exit := 0
		beforeRandomExit := 0
		beforeScreenSuccessExit := 0
		unBlind := 0
		unBlindFinish := 0
		pv := 0
		screenSuccess := 0
		screenFail := 0
		finish := 0
		toBeRandom := 0
		join := 0

		register = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 1
		})
		screenSuccess = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 7
		})
		screenFail = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 8
		})
		randomCount = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 3
		})
		exit = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 4 || item.Status == 5
		})
		beforeRandomExit = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && item.Group == ""
		})
		beforeScreenSuccessExit = slice.Count(subjects, func(index int, item models.Subject) bool {
			return (item.Status == 4 || item.Status == 5) && item.IsScreen == nil && item.Group == ""
		})
		unBlind = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.UrgentUnblindingStatus == 1
		})
		unBlindFinish = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.UrgentUnblindingStatus == 1 && item.Status == 9
		})
		pv = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.PvUnblindingStatus == 1
		})
		finish = slice.Count(subjects, func(index int, item models.Subject) bool {
			return item.Status == 9
		})
		join = slice.Count(subjects, func(index int, item models.Subject) bool {
			_, b := slice.Find(isDispensingSubjectIds, func(index int, id primitive.ObjectID) bool {
				return id == item.ID
			})
			return b
		})

		resp.Register = register + randomCount + exit + unBlind + screenSuccess + screenFail + finish - unBlindFinish
		resp.Random = randomCount + exit - beforeRandomExit + unBlind + finish - unBlindFinish
		resp.Exit = exit
		resp.UnBlind = unBlind
		resp.ScreenSuccess = screenSuccess + randomCount + exit - beforeScreenSuccessExit + unBlind + finish - unBlindFinish
		resp.ScreenFail = screenFail
		resp.Finish = finish
		resp.ToBeRandom = toBeRandom
		resp.Join = join
		resp.PvUnBlind = pv
	}

	return resp, nil
}
