import React, { useEffect, useImperativeHandle, useRef } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Form, Modal, Select, Col, Row, Button, Cascader, ConfigProvider, Input, Space } from "antd";
import { useSafeState } from "ahooks";
import { useGlobal } from "../../../../context/global";
import { useFetch } from "../../../../hooks/request";
import { getLocalesData } from "../../../common/multilingual/util";
import { getUserInfoFromCloud } from "../../../../api/user";
import { useAtom } from "jotai";
import { customLanguageAtom } from "../../../common/multilingual/context";
import { LanguageLibraryOption, TranslationStatusOption } from "./data";
import { SystemTable } from "./table-system";
import { TranslateUpload } from "./upload";

export const Show = (props: any) => {
    const intl = useIntl();
    const { formatMessage } = intl;
    const g = useGlobal();
    const [visible, setVisible] = useSafeState<any>(false);
    const [form] = Form.useForm();

    const [projectType, setProjectType] = useSafeState<any>(null);
    const [permissionList, setPermissionList] = useSafeState<any[]>([]);
    const [pagePathOptionList, setPagePathOptionList] = useSafeState<any[]>([]);
    const [typeOptionList, setTypeOptionList] = useSafeState<any[]>([]);
    const [envOptionList, setEnvOptionList] = useSafeState<any[]>([]);
    const [envList, setEnvList] = useSafeState<any[]>([]);
    const [cohortOptionList, setCohortOptionList] = useSafeState<any[]>([]);
    const [pagePath, setPagePath] = useSafeState<string[]>([]);
    const [timeZone, setTimeZone] = useSafeState<string>("");

    const [search, setSearch] = useSafeState<any>({});
    const [languageInfo, setLanguageInfo] = useAtom(customLanguageAtom);
    const systemTableRef = useRef<any>();
    const uploadRef = useRef<any>();

    const { runAsync: getUserInfoFromCloudRun } = useFetch(getUserInfoFromCloud, { manual: true });

    useImperativeHandle(props.bind, () => ({ show }));

    const show = (project: any, initOptions: any, languageNameList: any, record: any, permissionList: any) => {
        setLanguageInfo({
            id: record.id,
            customerId: project?.customerId,
            projectId: project?.id,
            projectNumber: project?.info.number,
            languageName: record.language,
            languageCode: record.code,
        });
        setVisible(true);
        setPermissionList(permissionList);

        // 环境
        const envList = project?.envs?.map((env: any) => ({ label: env.name, value: env.id })) || [];
        setEnvOptionList(envList);
        setProjectType(project?.info.type);
        setEnvList(project?.envs);

        // 设置默认值
        initLibrary(1);
    };

    const initLibrary = (libraryType: number) => {
        form.setFieldValue("languageLibrary", libraryType);
        if (libraryType === 1) {
            const { modules, types } = getLocalesData(g.lang);
            setPagePathOptionList(modules);
            setTypeOptionList(types);
            form.setFieldValue("env", null);
            form.setFieldValue("cohort", null);
        } else if (libraryType === 2) {
            const envId = envList[0].id;
            form.setFieldValue("env", envId);
            onEnvChange(envId);
            setPagePathOptionList([]);
            setTypeOptionList([]);
        } else {
            form.setFieldValue("env", null);
            form.setFieldValue("cohort", null);
            setPagePathOptionList([]);
            setTypeOptionList([]);
        }
    };

    const hide = () => {
        setVisible(false);
        setPermissionList([]);
        setEnvOptionList([]);
        setPagePathOptionList([]);
        setProjectType(null);
        setEnvList([]);
        setCohortOptionList([]);
        setTypeOptionList([]);
        setPagePath([]);
        setSearch({});
        form.resetFields();
        props.refresh();
    };

    // 获取时区
    useEffect(() => {
        getUserInfoFromCloudRun().then((userInfo: any) => {
            setTimeZone(userInfo?.data?.settings?.tz || Intl.DateTimeFormat().resolvedOptions().timeZone);
        });
    }, []);

    // 查询
    const searchClick = () => {
        form.validateFields().then((values) => {
            setSearch(values);
        });
    };

    //重置
    const resetClick = () => {
        setPagePathOptionList([]);
        setCohortOptionList([]);
        setTypeOptionList([]);
        setPagePath([]);
        setSearch({});
        form.resetFields();
        initLibrary(1);
    };

    const onPagePathChange = (path: any[]) => {
        setPagePath(path || []);
    };

    const onEnvChange = (value: string) => {
        if (projectType === 1) {
            setCohortOptionList([]);
        } else {
            const cohortList = envList.find((env: any) => env.id === value)?.cohorts?.map((cohort: any) => ({
                label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                value: cohort.id,
            }))
            setCohortOptionList(cohortList)
            form.setFieldValue("cohort", cohortList[0]?.value)
        }
    };

    // 批量导入
    const batchUploadClick = () => {
        uploadRef?.current?.show({
            language: languageInfo.languageName,
            projectType: projectType,
            customerId: languageInfo.customerId,
            projectId: languageInfo.projectId,
            envId: form.getFieldValue("env"),
            cohortId: form.getFieldValue("cohort"),
            languageId: languageInfo.id,
            languageLibrary: form.getFieldValue("languageLibrary"),
            envLabel: envOptionList.find((env) => env.value === form.getFieldValue("env"))?.label,
            cohortLabel: cohortOptionList.find((env) => env.value === form.getFieldValue("cohort"))?.label,
        });
    };

    const onUploadComplete = () => {
        systemTableRef?.current?.refresh();
    };

    const searchRowSpan = { lg: 6, md: 8, sm: 12, xs: 24 };
    const searchBtnSpan =
        form.getFieldValue("languageLibrary") === 2 && projectType !== 1
            ? { lg: 12, md: 24, sm: 24, xs: 24 }
            : { lg: 18, md: 8, sm: 12, xs: 24 };

    return (
        <React.Fragment>
            <Modal
                title={languageInfo.languageName + "-" + languageInfo.projectNumber}
                open={visible}
                onCancel={hide}
                zIndex={1}
                maskClosable={false}
                centered
                destroyOnClose
                className="custom-medium-modal-multiLanguage"
                okText={formatMessage({ id: "common.ok" })}
                onOk={hide}
            >
                <div style={{ backgroundColor: "#F2F3F5", padding: "16px 16px 0 16px" }}>
                    <Form form={form} labelCol={{ span: g.lang === "zh" ? 6 : 10 }}>
                        <Row gutter={16} style={{ display: "flex" }}>
                            <Col {...searchRowSpan}>
                                <Form.Item
                                    label={formatMessage({ id: "multiLanguage.language.library" })}
                                    name="languageLibrary"
                                    rules={[{ required: true }]}
                                >
                                    <Select
                                        showSearch
                                        onChange={initLibrary}
                                        placeholder={formatMessage({ id: "placeholder.select.common" })}
                                        options={LanguageLibraryOption}
                                    />
                                </Form.Item>
                            </Col>

                            <Col {...searchRowSpan}>
                                <Form.Item
                                    label={formatMessage({ id: "multiLanguage.page.path" })}
                                    rules={[{ required: true }]}
                                >
                                    <Cascader
                                        options={pagePathOptionList}
                                        onChange={onPagePathChange}
                                        value={pagePath}
                                        placeholder={formatMessage({ id: "placeholder.select.common" })}
                                        showSearch={{
                                            filter: (inputValue: string, path: any[]) => {
                                                return path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
                                            },
                                        }}
                                        changeOnSelect
                                        expandTrigger="click"
                                    />
                                </Form.Item>
                            </Col>
                            {form.getFieldValue("languageLibrary") === 2 && (
                                <>
                                    <Col {...searchRowSpan}>
                                        <Form.Item
                                            label={formatMessage({ id: "projects.env" })}
                                            name="env"
                                            rules={[{ required: true }]}
                                        >
                                            <Select
                                                showSearch
                                                optionFilterProp="children"
                                                filterOption={(input: any, option: any) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
                                                onChange={onEnvChange}
                                                disabled={form.getFieldValue("languageLibrary") !== 2}
                                                placeholder={formatMessage({ id: "placeholder.select.common" })}
                                                options={envOptionList}
                                                allowClear
                                            />
                                        </Form.Item>
                                    </Col>
                                    {projectType !== 1 && (
                                        <Col {...searchRowSpan}>
                                            <Form.Item
                                                label={formatMessage({ id: "check.cohort" })}
                                                name="cohort"
                                                rules={[{ required: true }]}
                                            >
                                                <Select
                                                    showSearch
                                                    optionFilterProp="children"
                                                    filterOption={(input: any, option: any) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
                                                    disabled={form.getFieldValue("languageLibrary") !== 2}
                                                    placeholder={formatMessage({ id: "placeholder.select.common" })}
                                                    options={cohortOptionList}
                                                    allowClear
                                                />
                                            </Form.Item>
                                        </Col>
                                    )}
                                </>
                            )}
                            {form.getFieldValue("languageLibrary") !== 2 && (
                                <Col {...searchRowSpan}>
                                    <Form.Item label={formatMessage({ id: "common.type" })} name="type">
                                        <Select
                                            showSearch
                                            optionFilterProp="children"
                                            filterOption={(input: any, option: any) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
                                            placeholder={formatMessage({ id: "placeholder.select.common" })}
                                            options={typeOptionList}
                                            allowClear
                                        />
                                    </Form.Item>
                                </Col>
                            )}
                            <Col {...searchRowSpan}>
                                <Form.Item label={formatMessage({ id: "common.name" })} name="name">
                                    <Input placeholder={formatMessage({ id: "placeholder.input.common" })} />
                                </Form.Item>
                            </Col>
                            <Col {...searchRowSpan}>
                                <Form.Item
                                    label={formatMessage({ id: "multiLanguage.translation.status" })}
                                    name="translationStatus"
                                >
                                    <Select
                                        showSearch
                                        placeholder={formatMessage({ id: "placeholder.select.common" })}
                                        options={TranslationStatusOption}
                                        allowClear
                                    />
                                </Form.Item>
                            </Col>
                            <Col {...searchBtnSpan}>
                                <div style={{flex: 1, display: "flex", alignItems: "start", justifyContent: "flex-end"}}>
                                    <Space>
                                        <Button type="primary" onClick={searchClick}>
                                            <FormattedMessage id="check.search" />
                                        </Button>
                                        <Button onClick={resetClick}>
                                            <FormattedMessage id="common.reset" />
                                        </Button>
                                        {permissionList.includes("operation.projects.project.multiLanguage.details.batchExport") && !["zh", "en"].includes(languageInfo.languageCode) && (
                                            <Button type="primary" onClick={batchUploadClick}>
                                                {formatMessage({ id: "multiLanguage.translation.import" })}
                                            </Button>
                                        )}
                                    </Space>
                                </div>
                            </Col>
                        </Row>
                    </Form>
                </div>
                <ConfigProvider
                    renderEmpty={() => {
                        return (
                            <div
                                style={{
                                    backgroundColor: "#F9F9FA",
                                    height: "100px",
                                    width: "calc(100% + 24px)",
                                    marginLeft: "-16px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                {formatMessage({ id: "multiLanguage.translation.no.data" })}
                            </div>
                        );
                    }}
                >
                    <SystemTable
                        bind={systemTableRef}
                        pagePath={pagePath}
                        envId={form.getFieldValue("env")}
                        cohortId={form.getFieldValue("cohort")}
                        permissionList={permissionList}
                        search={search}
                        timeZone={timeZone}
                    />
                </ConfigProvider>
                <TranslateUpload bind={uploadRef} onUploadComplete={onUploadComplete} />
            </Modal>
        </React.Fragment>
    );
};
