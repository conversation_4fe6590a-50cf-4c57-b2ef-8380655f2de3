package service

import (
	"bytes"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strconv"
	"strings"
	"time"
)

type RemoteReportService struct {
}

func (s *RemoteReportService) GetReportData(ctx *gin.Context, project *models.Project, env *models.Environment, start int, limit int) (map[string]interface{}, error) {
	// 查询项目下所有中心
	siteMatch := bson.M{
		"project_id": project.ID,
		"env_id":     env.ID,
	}

	projectSites := make([]models.ProjectSite, 0)
	cursor, err := tools.Database.Collection("project_site").Find(nil, siteMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	siteIds := slice.Map(projectSites, func(index int, item models.ProjectSite) primitive.ObjectID {
		return item.ID
	})

	match := bson.M{
		"project_id":      project.ID,
		"env_id":          env.ID,
		"status":          bson.M{"$in": []int{1, 2, 3, 6, 7, 8, 9}},
		"project_site_id": bson.M{"$in": siteIds},
	}

	countPipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing", // 关联的集合名称
			"let": bson.M{
				"subject_id": "$_id", // 将 subject 集合的 _id 赋值给变量 subject_id
			},
			"pipeline": []bson.M{
				{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": []bson.M{
								{"$eq": []interface{}{"$subject_id", "$$subject_id"}}, // 关联条件
								{"$eq": []interface{}{"$status", 2}},                  // 状态为已发药
							},
						},
					},
				},
			},
			"as": "dispensing_data", // 关联结果的字段名
		}}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$group", Value: bson.M{"_id": "$info.value"}}},
		{{Key: "$count", Value: "total"}},
	}

	var countResult struct{ Total int }
	if cursor, err := tools.Database.Collection("subject").Aggregate(ctx, countPipeline); err == nil {
		defer cursor.Close(ctx)
		if cursor.Next(ctx) {
			cursor.Decode(&countResult)
		}
	} else {
		return nil, errors.WithStack(err)
	}
	totalCount := countResult.Total

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing", // 关联的集合名称
			"let": bson.M{
				"subject_id": "$_id", // 将 subject 集合的 _id 赋值给变量 subject_id
			},
			"pipeline": []bson.M{
				{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": []bson.M{
								{"$eq": []interface{}{"$subject_id", "$$subject_id"}}, // 关联条件
								{"$eq": []interface{}{"$status", 2}},                  // 状态为已发药
							},
						},
					},
				},
			},
			"as": "dispensing_data", // 关联结果的字段名
		}}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$sort", Value: bson.M{
			"project_site_id": 1,
			"_id":             1,
		}}},
		{{Key: "$group", Value: bson.M{
			"_id":             "$info.value",
			"project_site_id": bson.M{"$first": "$project_site_id"}, // 添加项目中心ID字段
			"documents": bson.M{"$push": bson.M{
				"subjectId":      "$_id", // 保留原始文档的 _id
				"dispensingData": "$dispensing_data",
				// 其他需要的字段
			}},
		}}},

		//{{Key: "$match", Value: bson.M{"_id": "01-288"}}},

		{{Key: "$sort", Value: bson.D{
			{Key: "project_site_id", Value: 1},
			{Key: "_id", Value: 1},
		}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
	}

	cursor, err = tools.Database.Collection("subject").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	datas := make([]models.GroupSubject, 0)
	err = cursor.All(nil, &datas)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	subjectIds := make([]primitive.ObjectID, 0)
	for _, data := range datas {
		for _, document := range data.Subjects {
			subjectIds = append(subjectIds, document.SubjectID)
		}
	}

	var subjectData []models.Subject
	cursor, err = tools.Database.Collection("subject").Find(nil, bson.M{"_id": bson.M{"$in": subjectIds}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 构造为map
	subjectMap := make(map[primitive.ObjectID]models.Subject)
	for _, subject := range subjectData {
		subjectMap[subject.ID] = subject
	}

	// 属性map
	attributeMap := map[string]models.Attribute{}

	// 随机列表map
	randomListMap := map[string]models.RandomList{}

	// 表单map
	randomFormMap := map[string]models.Form{}

	// 中心map
	projectSiteMap := map[string]models.ProjectSite{}

	// 访视map
	visitCycleMap := map[string]models.VisitCycle{}

	// 遍历并组装数据
	var result []models.EDCReportData
	for _, data := range datas {
		edcReportData := models.EDCReportData{}
		for _, swd := range data.Subjects {
			edcReportData.Shortname = data.Shortname
			subjectWithDispensing := models.SubjectWithDispensingData{}

			// 构造受试者的数据
			subject := subjectMap[swd.SubjectID]
			subjectReportData, err := getSubjectReportData(ctx, &subject, project, env, attributeMap, randomListMap, randomFormMap, projectSiteMap)
			if err != nil {
				return nil, err
			}
			subjectWithDispensing.SubjectReportData = *subjectReportData

			// 遍历该受试者下的所有发药数据
			dispensingDatas := swd.DispensingData
			var dispensingResults []models.EdcPush
			for _, dispensing := range dispensingDatas {
				// 构造发药数据
				dispensingReportData, err := getDispensingReportData(ctx, &subject, &dispensing, project, env, attributeMap, visitCycleMap, projectSiteMap)
				if err != nil {
					return nil, err
				}
				dispensingResults = append(dispensingResults, *dispensingReportData)
			}
			subjectWithDispensing.DispensingReportData = dispensingResults
			edcReportData.Datas = append(edcReportData.Datas, subjectWithDispensing)
		}

		result = append(result, edcReportData)
	}

	return map[string]interface{}{
		"total": totalCount,
		"items": result,
	}, nil

}

func getSubjectReportData(
	ctx *gin.Context,
	subject *models.Subject,
	project *models.Project,
	env *models.Environment,
	attributeMap map[string]models.Attribute,
	randomListMap map[string]models.RandomList,
	randomFormMap map[string]models.Form,
	projectSiteMap map[string]models.ProjectSite,
) (*models.EdcPush, error) {
	// 查询属性
	var attribute models.Attribute
	if value, exists := attributeMap[subject.CohortID.Hex()]; exists {
		attribute = value
	} else {
		match := bson.M{
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"customer_id": subject.CustomerID,
		}
		if subject.CohortID != primitive.NilObjectID {
			match = bson.M{
				"project_id":  subject.ProjectID,
				"env_id":      subject.EnvironmentID,
				"customer_id": subject.CustomerID,
				"cohort_id":   subject.CohortID,
			}
		}
		err := tools.Database.Collection("attribute").FindOne(ctx, match).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		attributeMap[subject.CohortID.Hex()] = attribute
	}

	// 查询RandomList
	randomFilter := bson.M{
		"customer_id": subject.CustomerID,
		"project_id":  subject.ProjectID,
		"env_id":      subject.EnvironmentID,
		"status":      1,
		"$or": bson.A{
			bson.M{"site_ids": nil},
			bson.M{"site_ids": subject.ProjectSiteID},
		}}
	if subject.CohortID != primitive.NilObjectID {
		randomFilter = bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"status":      1,
			"cohort_id":   subject.CohortID,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": subject.ProjectSiteID},
			}}
	}
	cohort, _ := models.GetCohort(*project, subject.EnvironmentID.Hex(), subject.CohortID.Hex())
	if (project.ProjectInfo.Type == 3 || (project.ProjectInfo.Type == 2 && cohort.Type == 1)) && subject.LastGroup != "" { // 在随机逻辑
		randomFilter = bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"status":      1,
			"cohort_id":   subject.CohortID,
			"last_group":  subject.LastGroup,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": subject.ProjectSiteID},
			}}
	}
	var randomList models.RandomList
	// 只有随机才查询random_list
	if attribute.AttributeInfo.Random {
		if value, exists := randomListMap[subject.CohortID.Hex()+subject.LastGroup]; exists {
			randomList = value
		} else {
			err := tools.Database.Collection("random_list").FindOne(ctx, randomFilter).Decode(&randomList)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return nil, errors.WithStack(err)
			}
			randomListMap[subject.CohortID.Hex()+subject.LastGroup] = randomList
		}
	}

	// 查询表单
	var randomForm models.Form
	if value, exists := randomFormMap[subject.EnvironmentID.Hex()+subject.CohortID.Hex()]; exists {
		randomForm = value
	} else {
		formFilter := bson.M{"env_id": subject.EnvironmentID}
		if subject.CohortID != primitive.NilObjectID {
			formFilter = bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
		}

		if err := tools.Database.Collection("form").FindOne(nil, formFilter).Decode(&randomForm); err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		randomFormMap[subject.EnvironmentID.Hex()+subject.CohortID.Hex()] = randomForm
	}

	// 查询中心
	var projectSite models.ProjectSite
	if value, exists := projectSiteMap[subject.ProjectSiteID.Hex()]; exists {
		projectSite = value
	} else {
		err := tools.Database.Collection("project_site").FindOne(ctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		projectSiteMap[subject.ProjectSiteID.Hex()] = projectSite
	}

	// 随机信息组装
	subjectData, err := subjectDataAssembly(subject, project, env, &randomList, &randomForm, &attribute, &projectSite)
	if err != nil {
		return nil, err
	}
	return &subjectData, nil
}

// 组装受试者数据
func subjectDataAssembly(
	subject *models.Subject,
	project *models.Project,
	env *models.Environment,
	randomList *models.RandomList,
	randomForm *models.Form,
	attribute *models.Attribute,
	projectSite *models.ProjectSite,
) (models.EdcPush, error) {
	// 查询cohort
	var cohort models.Cohort
	if project.Type != 1 {
		for _, ch := range env.Cohorts {
			if ch.ID == subject.CohortID {
				cohort = ch
				break
			}
		}
	}

	// 提取受试者号
	subjectNo := ""
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectNo = fmt.Sprintf("%v", info.Value)
			break
		}
	}
	// 分层因素数据
	var data []models.Data

	if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
		for _, factor := range randomList.Design.Factors { // 分层因素
			// 随机分层
			for _, info := range subject.Info {
				// 随机分层
				if info.Name == factor.Name {
					if info.Value != nil {
						// 提取选项值
						label := ""
						for _, opt := range factor.Options {
							if opt.Value == info.Value {
								label = opt.Label
							}
						}
						// 追加数据
						data = append(data, models.Data{
							Field: factor.Name,
							Value: label,
						})
					}
					break
				}
			}
			// 实际分层
			for _, actualInfo := range subject.ActualInfo {
				// 随机分层
				if actualInfo.Name == factor.Name {
					if actualInfo.Value != nil {
						// 提取选项值
						label := ""
						for _, opt := range factor.Options {
							if opt.Value == actualInfo.Value {
								label = opt.Label
							}
						}
						// 追加数据
						data = append(data, models.Data{
							Field: factor.Name + "_actual",
							Value: label,
						})
					}
					break
				}
			}
		}
	}

	// 表单数据
	if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
		for _, rf := range randomForm.Fields {
			if (rf.Status == nil || *rf.Status == 1) && (rf.ApplicationType == nil || *rf.ApplicationType == 1) {
				if rf.Type == "radio" || rf.Type == "select" { // 单选框或者下拉框
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								// 提取选项值
								label := ""
								for _, opt := range rf.Options {
									if opt.Value == info.Value {
										label = opt.Label
									}
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: label,
								})
							}
							break
						}
					}
				}
				if rf.Type == "checkbox" { // 复选框
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								var checkboxBf bytes.Buffer
								str := info.Value.([]interface{})
								for _, option := range rf.Options {
									for j := 0; j < len(str); j++ {
										if option.Value == str[j].(string) {
											checkboxBf.WriteString(option.Label)
											checkboxBf.WriteString(",")
										}
									}
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: checkboxBf.String(),
								})
							}
							break
						}
					}
				}
				if rf.Type == "switch" { // 开关
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								v := ""
								if info.Value == true {
									v = "yes"
								} else {
									v = "no"
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: v,
								})
							}
							break
						}
					}
				}

				if rf.Type == "input" || rf.Type == "inputNumber" || rf.Type == "textArea" || rf.Type == "datePicker" || rf.Type == "timePicker" { // 其它类型字段
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: info.Value,
								})
							}
							break
						}
					}
				}
			}
		}
	}

	// 筛选
	if subject.IsScreen != nil {
		isScreenStr := ""
		if *subject.IsScreen == true {
			isScreenStr = "yes"
		} else {
			isScreenStr = "no"
		}

		data = append(data, models.Data{
			Field: "isScreen",
			Value: isScreenStr,
		})
		data = append(data, models.Data{
			Field: "screenTime",
			Value: subject.ScreenTime,
		})
		data = append(data, models.Data{
			Field: "icfTime",
			Value: subject.ICFTime,
		})
	}

	data = append(data, models.Data{
		Field: "cohortName",
		Value: cohort.Name,
	})

	// 除去已登记和已筛选状态其它状态都表示受试者已经随机. 判断data是否要追加(随机号/随机组别/随机时间)字段
	if subject.Status != 1 && subject.Status != 2 && subject.Status != 7 && subject.Status != 8 {
		data = append(data, models.Data{
			Field: "randomNo",
			Value: subject.RandomNumber,
		})

		value := subject.Group
		parGroupName := "" // 主组别
		subGroupName := "" // 子组别
		// 如果是盲法项目且分配了盲态权限则隐藏组别
		if subject.SubGroupName != "" {
			gP, b := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
				return item.Name == subject.Group
			})
			if b {
				group := *gP
				if attribute.AttributeInfo.Blind && group.Blind {
					value = tools.BlindData + " " + tools.BlindData
					parGroupName = tools.BlindData
					subGroupName = tools.BlindData
				} else if attribute.AttributeInfo.Blind && !group.Blind {
					value = tools.BlindData + " " + subject.SubGroupName
					parGroupName = tools.BlindData
					subGroupName = subject.SubGroupName
				} else if !attribute.AttributeInfo.Blind && group.Blind {
					value = subject.ParGroupName + " " + tools.BlindData
					parGroupName = subject.ParGroupName
					subGroupName = tools.BlindData
				}
			}
		} else {
			if attribute.AttributeInfo.Blind {
				value = tools.BlindData
			}
		}
		data = append(data, models.Data{
			Field: "group",
			Value: value, // 该数据推送的时候在写入
		})
		// 主组别
		data = append(data, models.Data{
			Field: "parGroupName",
			Value: parGroupName,
		})
		// 子组别
		data = append(data, models.Data{
			Field: "subGroupName",
			Value: subGroupName,
		})

		timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
		if err != nil {
			return models.EdcPush{}, err
		}
		if timeZone == "" {
			zone, err := tools.GetTimeZone(project.ID)
			if err != nil {
				return models.EdcPush{}, err
			}
			timeZone = tools.FormatOffsetToZoneStringUtc(zone)
			//timeZone = fmt.Sprintf("UTC%+d", strTimeZone)
		}
		//intTimeZone, err := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
		intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
		if err != nil {
			return models.EdcPush{}, err
		}

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		randomTime := time.Unix(subject.RandomTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
		data = append(data, models.Data{
			Field: "randomTime",
			Value: randomTime + "(" + timeZone + ")",
		})
	}

	timestamp := time.Now().UnixMilli()
	md5Str := GetMD5Hash(timestamp) // 签名串
	var irtSubjectID = subject.ID.Hex()
	if (project.ProjectInfo.Type == 3 || (project.ProjectInfo.Type == 2 && cohort.Type == 1)) && cohort.LastID != primitive.NilObjectID { // 在随机项目
		irtSubjectID = ""
	}

	id := primitive.NewObjectID()

	subjectNoPrefix := ""
	// 追加受试者号前缀
	if attribute.AttributeInfo.Prefix && attribute.AttributeInfo.PrefixExpression != "" {
		subjectNoPrefix = strings.ReplaceAll(attribute.AttributeInfo.PrefixExpression, "{siteNO}", projectSite.Number)
	}
	// v2.11 新增逻辑 推送规则如果是受试者号就不传受试者ID
	if project.PushRules == 1 {
		irtSubjectID = ""
	}

	var content = models.Content{
		Env:              env.Name,
		Project:          project.Number,
		Sign:             md5Str,
		Site:             projectSite.Number,
		IrtSubjectID:     irtSubjectID,
		ReplaceSubjectID: "", // 被替换的受试者处于停用状态，历史数据不推送他
		ReplaceSubjectNo: "", // 被替换的受试者处于停用状态，历史数据不推送他
		SubjectNoPrefix:  subjectNoPrefix,
		SubjectNo:        subjectNo,
		Timestamp:        timestamp,
		Cohort:           models.GetCohortReRandomName(cohort),
		SubjectData:      data,
	}

	// 数据组装
	var edcPush = models.EdcPush{
		ID:            id,
		CustomerID:    subject.CustomerID,
		ProjectID:     subject.ProjectID,
		EnvironmentID: subject.EnvironmentID,
		CohortID:      subject.CohortID,
		ProjectSiteID: subject.ProjectSiteID,
		OID:           subject.ID,
		Content:       content,
		Source:        1,
	}
	if len(projectSite.Tz) > 0 {
		edcPush.Location = projectSite.Tz
	}
	return edcPush, nil
}

func getDispensingReportData(
	ctx *gin.Context,
	subject *models.Subject,
	dispensing *models.Dispensing,
	project *models.Project,
	env *models.Environment,
	attributeMap map[string]models.Attribute,
	visitCycleMap map[string]models.VisitCycle,
	projectSiteMap map[string]models.ProjectSite,
) (*models.EdcPush, error) {
	// 查询属性
	var attribute models.Attribute
	if value, exists := attributeMap[subject.CohortID.Hex()]; exists {
		attribute = value
	} else {
		match := bson.M{
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"customer_id": subject.CustomerID,
		}
		if subject.CohortID != primitive.NilObjectID {
			match = bson.M{
				"project_id":  subject.ProjectID,
				"env_id":      subject.EnvironmentID,
				"customer_id": subject.CustomerID,
				"cohort_id":   subject.CohortID,
			}
		}
		err := tools.Database.Collection("attribute").FindOne(ctx, match).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		attributeMap[subject.CohortID.Hex()] = attribute
	}

	// 查询访视信息
	visitCycleFilter := bson.M{"customer_id": subject.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}
	var visitCycle models.VisitCycle
	if value, exists := visitCycleMap[dispensing.CohortID.Hex()]; exists {
		visitCycle = value
	} else {
		err := tools.Database.Collection("visit_cycle").FindOne(ctx, visitCycleFilter).Decode(&visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		visitCycleMap[dispensing.CohortID.Hex()] = visitCycle
	}

	visitInfo := models.VisitCycleInfo{}
	for _, info := range visitCycle.Infos {
		if info.ID == dispensing.VisitInfo.VisitCycleInfoID {
			visitInfo = info
			break
		}
	}

	// 查询中心
	var projectSite models.ProjectSite
	if value, exists := projectSiteMap[subject.ProjectSiteID.Hex()]; exists {
		projectSite = value
	} else {
		err := tools.Database.Collection("project_site").FindOne(ctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		projectSiteMap[subject.ProjectSiteID.Hex()] = projectSite
	}

	dispensingData, err := dispensingDataAssembly(dispensing, subject, project, env, &visitInfo, &attribute, &projectSite)
	if err != nil {
		return nil, err
	}

	return &dispensingData, nil
}

// 发药数据组装
func dispensingDataAssembly(
	dispensing *models.Dispensing,
	subject *models.Subject,
	project *models.Project,
	env *models.Environment,
	visitInfo *models.VisitCycleInfo,
	attribute *models.Attribute,
	projectSite *models.ProjectSite,
) (models.EdcPush, error) {
	// 查询cohort
	var cohort models.Cohort
	if project.Type != 1 {
		for _, ch := range env.Cohorts {
			if ch.ID == subject.CohortID {
				cohort = ch
				break
			}
		}
	}

	// 提取受试者号
	subjectNo := ""
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectNo = fmt.Sprintf("%v", info.Value)
			break
		}
	}

	// 组装推送的药物数据
	var data []models.Data
	// 药物号
	var drugNameNumberStr strings.Builder

	drug := make([]map[string]interface{}, 0)

	var labels []string
	// 对药物号进行排序
	err := slice.SortByField(dispensing.DispensingMedicines, "Number")
	if err != nil {
		return models.EdcPush{}, err
	}
	// 已编号药物
	for _, dm := range dispensing.DispensingMedicines {
		isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, dm.Name)
		if err != nil {
			return models.EdcPush{}, err
		}

		drugName := tools.BlindData // 药物名称
		if !isBlindedDrug {
			drugName = dm.Name
		}

		labels = append(labels, dm.Label)

		drugNameNumberStr.WriteString(dm.Number)
		drugNameNumberStr.WriteString("/")

		m := make(map[string]interface{})
		m["drugNo"] = dm.Number
		m["drugName"] = drugName
		drug = append(drug, m)

	}

	// 未编号药物
	for _, odm := range dispensing.OtherDispensingMedicines {
		isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, odm.Name)
		if err != nil {
			return models.EdcPush{}, err
		}

		drugName := tools.BlindData // 药物名称
		if !isBlindedDrug {
			drugName = odm.Name
		}

		labels = append(labels, odm.Label)

		//drugNameNumberStr.WriteString(drugName)
		drugNameNumberStr.WriteString("(")
		drugNameNumberStr.WriteString(strconv.Itoa(odm.Count))
		drugNameNumberStr.WriteString("/")
		drugNameNumberStr.WriteString(odm.Batch)
		drugNameNumberStr.WriteString("/")
		drugNameNumberStr.WriteString(odm.ExpireDate)
		drugNameNumberStr.WriteString(")")
		drugNameNumberStr.WriteString(" / ")

		m := make(map[string]interface{})
		m["drugName"] = drugName
		m["drugCount"] = odm.Count
		m["drugBatch"] = odm.Batch
		m["drugExpireDate"] = odm.ExpireDate
		drug = append(drug, m)
	}

	// 对药物号进行排序
	err = slice.SortByField(dispensing.RealDispensingMedicines, "Number")
	if err != nil {
		return models.EdcPush{}, err
	}
	//实际使用的药物
	if dispensing.RealDispensingMedicines != nil && len(dispensing.RealDispensingMedicines) > 0 {
		drugNameNumberStr.WriteString("【")
		for i, rdm := range dispensing.RealDispensingMedicines {
			index := i + 1

			isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, rdm.Name)
			if err != nil {
				return models.EdcPush{}, err
			}

			drugName := tools.BlindData // 药物名称
			if !isBlindedDrug {
				drugName = rdm.Name
			}
			drugNameNumberStr.WriteString(rdm.Number)
			if len(dispensing.RealDispensingMedicines) > index {
				drugNameNumberStr.WriteString("/ ")
			}

			m := make(map[string]interface{})
			m["drugNo"] = rdm.Number
			m["drugName"] = drugName
			drug = append(drug, m)
		}
		drugNameNumberStr.WriteString("】")
	}

	//实际使用的其它药物
	if dispensing.RealOtherDispensingMedicines != nil && len(dispensing.RealOtherDispensingMedicines) > 0 {
		drugNameNumberStr.WriteString("【")
		for _, rodm := range dispensing.RealOtherDispensingMedicines {
			isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, rodm.Name)
			if err != nil {
				return models.EdcPush{}, err
			}

			drugName := tools.BlindData // 药物名称
			if !isBlindedDrug {
				drugName = rodm.Name
			}

			//drugNameNumberStr.WriteString(drugName)
			drugNameNumberStr.WriteString("(")
			drugNameNumberStr.WriteString(strconv.Itoa(rodm.Count))
			drugNameNumberStr.WriteString("/")
			drugNameNumberStr.WriteString(rodm.Batch)
			drugNameNumberStr.WriteString("/")
			drugNameNumberStr.WriteString(rodm.ExpireDate)
			drugNameNumberStr.WriteString(")")
			drugNameNumberStr.WriteString(" / ")

			m := make(map[string]interface{})
			m["drugName"] = drugName
			m["drugCount"] = rodm.Count
			m["drugBatch"] = rodm.Batch
			m["drugExpireDate"] = rodm.ExpireDate
			drug = append(drug, m)
		}
		drugNameNumberStr.WriteString("】")
	}

	// 去除 /
	drugNo := ""
	if drugNameNumberStr.String() != "" {
		drugNo = drugNameNumberStr.String()
		last1 := drugNo[len(drugNo)-1:]
		last2 := drugNo[len(drugNo)-3:]
		if last1 == "/" {
			drugNo = drugNo[0 : len(drugNo)-1]
		} else if last2 == " / " {
			drugNo = drugNo[0 : len(drugNo)-3]
		}
	}

	drugValue := ""
	if drug != nil && len(drug) > 0 {
		drugStr, err := json.Marshal(drug)
		if err != nil {
			return models.EdcPush{}, err
		}
		drugValue = string(drugStr)
	} else {
		drugValue = "[]"
	}

	data = append(data, models.Data{
		Field:                        "drugNo",
		Value:                        drugNo,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	data = append(data, models.Data{
		Field:                        "drug",
		Value:                        drugValue,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	// 水平
	doseLevelValue := ""
	if dispensing.DoseInfo != nil && dispensing.DoseInfo.DoseLevelList != nil && dispensing.DoseInfo.DoseLevelList.Name != "" {
		doseLevelValue = dispensing.DoseInfo.DoseLevelList.Name
	}
	data = append(data, models.Data{
		Field:                        "doseLevel",
		Value:                        doseLevelValue,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	//标签
	var labelValue strings.Builder

	labels = slice.Unique(labels)

	if labels != nil && len(labels) > 0 {
		for _, label := range labels {
			labelValue.WriteString(label)
			labelValue.WriteString(" ")
		}
	}
	data = append(data, models.Data{
		Field:                        "labels",
		Value:                        labelValue.String(),
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	dispensingTime := ""
	timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return models.EdcPush{}, err
	}

	if timeZone == "" {
		zone, err := tools.GetTimeZone(project.ID)
		if err != nil {
			return models.EdcPush{}, err
		}
		timeZone = tools.FormatOffsetToZoneStringUtc(zone)
		//timeZone = fmt.Sprintf("UTC%+d", strTimeZone)
	}
	if dispensing.DispensingTime != 0 {
		//intTimeZone, err := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
		intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
		if err != nil {
			return models.EdcPush{}, err
		}

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		dispensingTime = time.Unix(dispensing.DispensingTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
		dispensingTime = dispensingTime + "(" + timeZone + ")"
	}
	data = append(data, models.Data{
		Field:            "drugTime",
		Value:            dispensingTime,
		Visit:            visitInfo.Number,
		InstanceRepeatNo: dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:    dispensing.VisitInfo.BlockRepeatNo,
	})

	data = append(data, models.Data{
		Field:            "cohortName",
		Value:            cohort.Name,
		Visit:            visitInfo.Number,
		InstanceRepeatNo: dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:    dispensing.VisitInfo.BlockRepeatNo,
	})

	timestamp := time.Now().UnixMilli()
	md5Str := GetMD5Hash(timestamp) // 签名串

	var irtSubjectID = subject.ID.Hex()
	if (project.ProjectInfo.Type == 3 || (project.ProjectInfo.Type == 2 && cohort.Type == 1)) && cohort.LastID != primitive.NilObjectID { // 在随机项目
		irtSubjectID = ""
	}

	id := primitive.NewObjectID()

	subjectNoPrefix := ""
	// 追加受试者号前缀
	if attribute.AttributeInfo.Prefix && attribute.AttributeInfo.PrefixExpression != "" {
		subjectNoPrefix = strings.ReplaceAll(attribute.AttributeInfo.PrefixExpression, "{siteNO}", projectSite.Number)
	}
	var content = models.Content{
		Env:             env.Name,
		Project:         project.Number,
		Sign:            md5Str,
		Site:            projectSite.Number,
		IrtSubjectID:    irtSubjectID,
		EdcPushId:       id.Hex(),
		SubjectNo:       subjectNo,
		SubjectNoPrefix: subjectNoPrefix,
		Timestamp:       timestamp,
		Cohort:          models.GetCohortReRandomName(cohort),
		SubjectData:     data,
	}

	// 数据组装
	var edcPush = models.EdcPush{
		ID:            id,
		CustomerID:    subject.CustomerID,
		ProjectID:     subject.ProjectID,
		EnvironmentID: subject.EnvironmentID,
		CohortID:      subject.CohortID,
		ProjectSiteID: subject.ProjectSiteID,
		OID:           dispensing.ID,
		Content:       content,
		Source:        2,
	}
	if len(projectSite.Tz) > 0 {
		edcPush.Location = projectSite.Tz
	}

	return edcPush, nil
}
