[email]
alarm.storehouse.content = <p>库存可用研究产品低于警戒值，请及时补充.研究产品信息(库房名称/研究产品名称/警戒数量/剩余数量)：{{.drugInfo}}</p> # 未使用
alarm.storehouse.title = Clinflash IRT {{.projectNumber}} {{.envName}} 库房研究产品警戒通知 {{.deportName}}
shipmentMode.status.set = 发放数量
shipmentMode.status.reSupply = 再供应量
shipmentMode.status.max = 最大缓冲量
shipmentMode.status.supplyRatio = 供应比例
cross.check.error = Clinflash IRT 异常通知
dispensing.plan = <p>{{.label}}:{{.subjectNumber}}</p><p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>备注:{{.remark}}</p>
dispensing.unscheduled-plan = <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>计划外发放原因:{{.reason}}</p>
dispensing.plan-logistics = <p>{{.label}}:{{.subjectNumber}}</p>  <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>备注:{{.remark}}</p>
dispensing.unscheduled-plan-logistics = <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>计划外发放原因:{{.reason}}</p>
dispensing.plan-title = Clinflash IRT {{.projectNumber}} {{.envName}}  研究产品发放 {{.siteNumber}} {{.siteName}}
dispensing.unscheduled-plan-title = Clinflash IRT  {{.projectNumber}} {{.envName}} 研究产品{{.unscheduled}} {{.siteNumber}} {{.siteName}}
dispensing.reissue = <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>补发原因:{{.remark}}</p>
dispensing.reissue-logistics = <p>{{.label}}:{{.subjectNumber}}</p>  <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>补发原因:{{.remark}}</p>
dispensing.reissue-title = Clinflash IRT {{.projectNumber}} {{.envName}}  研究产品补发 {{.siteNumber}} {{.siteName}}
dispensing.replace = <p>{{.label}}:{{.subjectNumber}}</p><p>替换研究产品编号:{{.replaceNumber}}</p> <p>被替换研究产品编号:{{.drugNumber}}</p> <p>替换时间:{{.dispensingDate}}</p> <p>替换原因:{{.reason}}</p>
dispensing.replace-title = Clinflash IRT {{.projectNumber}} {{.envName}}   研究产品替换提醒 {{.siteNumber}} {{.siteName}}
dispensing.apply = <p>{{.label}}:{{.subjectNumber}}</p><p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>备注:{{.remark}}</p>  <p>研究产品申请时间:{{.dispensingDate}}</p>
dispensing.apply-title = Clinflash IRT {{.projectNumber}} {{.envName}} 发放申请 {{.siteNumber}} {{.siteName}}
dispensing.reissue-dtp = <p>{{.label}}:{{.subjectNumber}}</p><p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>补发原因:{{.remark}}</p>  <p>研究产品补发申请时间:{{.dispensingDate}}</p>
dispensing.reissue-dtp-title = Clinflash IRT {{.projectNumber}} {{.envName}} 补发申请 {{.siteNumber}} {{.siteName}}
dispensing.unscheduled-apply = <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>计划外发放原因:{{.reason}}</p>  <p>研究产品申请时间:{{.dispensingDate}}</p>
dispensing.unscheduled-apply-title = Clinflash IRT {{.projectNumber}} {{.envName}} 计划外申请 {{.siteNumber}} {{.siteName}}
dispensing.replace-dtp = <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.replaceNumber}}</p> <p>研究产品编号(被替换)：{{.drugNumber}}</p> <p>订单号:{{.orderNumber}}</p> <p>替换时间:{{.dispensingDate}}</p> <p>替换原因:{{.reason}}</p>
dispensing.replace-dtp-title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Replacement {{.siteNumber}} {{.siteName}}
dispensing.register-title = Clinflash IRT {{.projectNumber}} {{.envName}} 登记实际研究产品提醒 {{.siteNumber}} {{.siteName}}
dispensing.register = <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>发放时间:{{.dispensingTime}}</p> <p>备注:{{.remark}}</p> <p>实际登记研究产品:{{.registerNumber}}</p> <p>操作时间:{{.dispensingDate}}</p>
dispensing.retrieval-title = Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品取回提醒 {{.siteNumber}} {{.siteName}}
dispensing.retrieval = <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>发放时间:{{.dispensingTime}}</p> <p>备注:{{.remark}}</p> <p>取回研究产品编号:{{.retrievalNumber}}</p> <p>操作时间:{{.dispensingDate}}</p>
dispensing.group = <p>组别:{{.group}}</p>
dispensing.subGroup = <p>子组别:{{.subGroup}}</p>
dispensing.number = <p>随机号:{{.random_number}}</p>
dispensing.not-attend-title = Clinflash IRT {{.projectNumber}} {{.envName}} 不参加访视 {{.siteNumber}} {{.siteName}}
dispensing.single.subject = <p>{{.label}}:{{.subjectNumber}}</p>
dispensing.single.drugNumber = <p>研究产品编号:{{.drugNumber}}</p>
dispensing.single.dispensingDate = <p>发放时间:{{.dispensingDate}}</p>
dispensing.single.replaceTime = <p>替换时间:{{.replaceTime}}</p>
dispensing.single.orderNumber = <p>订单号:{{.orderNumber}}</p>
dispensing.single.visitCycle = <p>访视周期:{{.visitCycle}}</p>
dispensing.single.remark = <p>备注:{{.remark}}</p>
dispensing.single.registerNumber = <p>实际登记研究产品:{{.registerNumber}}</p>
dispensing.single.retrievalNumber = <p>取回研究产品编号:{{.retrievalNumber}}</p>
dispensing.single.dispensingTime = <p>操作时间:{{.dispensingTime}}</p>
dispensing.single.replaceNumber = <p>替换研究产品编号:{{.replaceNumber}}</p>
dispensing.single.beReplaceNumber = <p>被替换研究产品编号:{{.beReplaceNumber}}</p>
dispensing.single.unscheduled-dispensing-reason = <p>计划外发放原因:{{.reason}}</p>
dispensing.single.unscheduled-dispensing-reason-customer = <p>{{.dispensingTypeZh}}原因:{{.reason}}</p>
dispensing.single.re-dispensing-reason = <p>补发原因:{{.reason}}</p>
dispensing.single.replace-reason = <p>研究产品替换原因:{{.reason}}</p>
batch-group.alert-site-title = Clinflash IRT  {{.projectNumber}} {{.envName}} 受试者中心警戒提醒 {{.siteNumber}} {{.siteName}}
batch-group.alert-depot-title = Clinflash IRT  {{.projectNumber}} {{.envName}} 受试者库房警戒提醒 {{.depotName}}
batch-group.limit-site-title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者中心上限提醒  {{.siteNumber}} {{.siteName}}
batch-group.limit-depot-title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者库房上限提醒 {{.depotName}}
batch-group.depotName = 库房：{{.depotName}}
batch-group.siteName = 中心名称:{{.siteName}}
batch-group.alarm = <p>批次入组人数达到警戒值。</p> <p>批次号/警戒值/实际人数:  {{.alarm}}</p>
batch-group.limit = <p>批次入组人数达到警戒值。</p> <p>批次号/警戒值/实际人数: {{.limit}}</p>
footer = 此邮件为系统自动发送的邮件。对此邮件的回复将不受监控或无响应.<br>
mail.test = 测试
mail.text = 测试xx {{.Name}}
medicine.expire_title = Clinflash IRT  {{.projectNumber}} {{.envName}} 研究产品有效期提醒 {{.instituteNumber}} {{.instituteName}}
medicine.freeze.title = Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品隔离通知 {{.instituteInfo}} {{.freezeNumber}}
medicine.release.title = Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品解隔离通知 {{.instituteInfo}} {{.freezeNumber}}
order.automatic_error = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>创建自动研究产品订单失败，请检查自动配药量和库存研究产品数量是否匹配.</p>
order.automatic_error_dual = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>创建自动研究产品订单失败,请检查自动配药量和库存研究产品数量是否匹配.</p> <p></p>
order.automatic_error_title = Clinflash IRT  {{.projectNumber}}({{.envName}}) 自动创建订单失败提醒 {{.destination}}
order.automatic_alarm_title = Clinflash IRT {{.projectNumber}}({{.envName}}) 自动订单警戒
order.automatic_success_title = Clinflash IRT {{.projectNumber}}({{.envName}}) 订单自动创建提醒 {{.destination}} {{.orderNumber}}
order.cancel = <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>订单号:{{.orderNumber}}</p> <p>取消原因：{{.reason}}</p> <p>订单已取消.</p>
order.cancel-logistics = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视周期:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>取消原因：{{.reason}}</p> <p>订单已取消.</p>
order.cancel_dtp = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>取消原因:{{.reason}}</p> <p>订单已取消.</p>
order.cancel_dtp_sub = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>取消原因:{{.reason}}</p> <p>订单已取消.</p>
order.close = <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>订单号:{{.orderNumber}}</p> <p>关闭原因：{{.reason}}</p> <p>订单已关闭.</p>
order.close-logistics = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视周期:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>关闭原因：{{.reason}}</p> <p>订单已关闭.</p>
order.close_dtp = <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>订单号:{{.orderNumber}}</p> <p>关闭原因:{{.reason}}</p> <p>订单已关闭.</p>
order.close_dtp_sub = <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>订单号:{{.orderNumber}}</p> <p>关闭原因:{{.reason}}</p> <p>订单已关闭.</p>
order.end = <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>订单号:{{.orderNumber}}</p> <p>终止原因：{{.reason}}</p> <p>订单已终止.</p>
order.end-logistics = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视周期:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>物流信息:{{.logistics}}</p> <p>终止原因：{{.reason}}</p> <p>订单已终止.</p>
order.lost_dtp = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>访视周期:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>丢失原因:{{.reason}}</p> <p>{{.userName}} 确认该订单已丢失.</p>
order.lost_dtp_sub = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视周期:{{.visit}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>订单号:{{.orderNumber}}</p> <p>丢失原因:{{.reason}}</p> <p>{{.userName}} 确认该订单已丢失.</p>
order.cancel_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单取消通知 {{.destination}} {{.orderNumber}}
order.close_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单关闭通知 {{.destination}} {{.orderNumber}}
order.end_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单终止通知 {{.destination}} {{.orderNumber}}
order.cancel_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} 订单取消通知 {{.subject}} {{.visit}} {{.orderNumber}}
order.close_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} 订单关闭通知 {{.subject}} {{.visit}} {{.orderNumber}}
order.change_title = Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品更换  {{.destination}} {{.orderNumber}}
order.change_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品更换 {{.subject}} {{.visit}} {{.orderNumber}}
order.batch_expiration_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品有效期和批次更新 {{.orderNumber}}
order.lost = <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>订单号:{{.orderNumber}}</p> <p>丢失原因:{{.reason}}</p> <p>{{.userName}} 确认该订单已丢失.</p>
order.lost_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单丢失通知 {{.destination}} {{.orderNumber}}
order.lost_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} 订单丢失通知 {{.subject}} {{.visit}} {{.orderNumber}}
order.no_automatic = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>中心编号:{{.siteNumber}}</p> <p>中心名称:{{.siteName}}</p> <p>库存研究产品低于警戒值，请及时补充.</p>
order.no_automatic_dual = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>中心编号:{{.siteNumber}}</p> <p>中心名称:{{.siteName}}</p> <p>库存研究产品低于警戒值，请及时补充.</p> <p></p> <p>Project Number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site Number:{{.siteNumber}}</p> <p>Site Name:{{.siteName}}</p> <p>The IP in stock are lower than the warning value， please replenish them in time</p>
order.no_automatic_success_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单确认通知 {{.destination}} {{.orderNumber}}
order.medicine_order_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单创建通知 {{.destination}} {{.orderNumber}}
order.medicine_order_dtp_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单创建通知 {{.subject}} {{.visit}} {{.orderNumber}}
order.forecast_title = Clinflash IRT {{.projectNumber}} {{.envName}} 库存使用时间预测
order.no_automatic_title = Clinflash IRT {{.projectNumber}} {{.envName}} 中心研究产品警戒通知 {{.siteNumber}} {{.siteName}}
order.over_title_depot = Clinflash IRT {{.projectNumber}}({{.envName}}) {{.siteName}} 订单超时提醒 {{.destination}} {{.orderNumber}}
order.over_title_site = Clinflash IRT {{.projectNumber}}({{.envName}}) 订单超时提醒 {{.destination}} {{.orderNumber}}
order.overtime_depot = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>接收机构名称:{{.siteName}}</p> <p>订单编号:{{.orderNumber}}</p> <p>有订单尚未确认收到，状态为{{.statusItem}}，订单生成时间{{.generateDate}}，请确认.</p>
order.overtime_site = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>接收机构编号:{{.siteNumber}}</p> <p>接收机构名称:{{.siteName}}</p> <p>订单编号:{{.orderNumber}}</p> <p>有订单尚未确认收到，状态为{{.statusItem}}，订单生成时间{{.generateDate}}，请确认.</p>
order.receive = <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>订单号:{{.orderNumber}}</p> <p>{{.userName}} 确认该订单已在目的地收到。</p>
order.receive-logistics = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>物流信息:{{.logistics}}</p> <p>{{.userName}} 确认该订单已在目的地收到。</p>
order.receive_dtp = <p>{{.label}}:{{.subject}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>物流信息:{{.logistics}}</p> <p>{{.userName}} 确认该订单已在目的地收到。</p>
order.receive_dtp_sub = <p>{{.label}}:{{.subject}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>{{.userName}} 确认该订单已在目的地收到。</p>
order.receive_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单接收通知 {{.destination}} {{.orderNumber}}
order.receive_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} 订单接收通知 {{.subject}} {{.visit}} {{.orderNumber}}
order.recovery_title = Clinflash IRT {{.projectNumber}} {{.envName}} 回收订单创建通知 {{.destination}} {{.orderNumber}}
order.recovery_confirm_title = Clinflash IRT {{.projectNumber}} {{.envName}} 回收订单确认通知 {{.destination}} {{.orderNumber}}
order.send = <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>订单号:{{.orderNumber}}</p> <p>研究产品订单运送中.</p>
order.send-logistics = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>物流信息:{{.logistics}}</p> <p>研究产品订单运送中.</p>
order.send_dtp = <p>{{.label}}:{{.subject}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>物流信息:{{.logistics}}</p> <p>研究产品订单运送中.</p>
order.send_dtp_sub = <p>{{.label}}:{{.subject}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>物流信息:{{.logistics}}</p> <p>研究产品订单运送中.</p>
order.send_title = Clinflash IRT {{.projectNumber}} {{.envName}} 订单运送通知 {{.destination}} {{.orderNumber}}
order.send_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} 订单运送通知 {{.subject}} {{.visit}} {{.orderNumber}}
order.create_title_logistics = Clinflash IRT {{.projectNumber}} {{.envName}} 订单确认通知 {{.subject}} {{.visit}} {{.orderNumber}}
order.create_logistics = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>研究产品订单已创建.</p>
order.create_logistics_dtp = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>研究产品订单已确认.</p>
order.confrim_logistics = <p>起运地:{{.start}}</p> <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>研究产品订单已确认.</p>
order.end_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} 订单终止通知 {{.subject}} {{.visit}} {{.orderNumber}}
order.end_dtp = <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>物流信息:{{.logistics}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>终止原因:{{.reason}}</p> <p>该订单已终止</p>
order.end_dtp_sub = <p>{{.label}}:{{.subject}}</p> <p>访视名称:{{.visit}}</p> <p>订单号:{{.orderNumber}}</p> <p>物流信息:{{.logistics}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>终止原因:{{.reason}}</p> <p>该订单已终止</p>
order.approval.add-title = Clinflash IRT {{.projectNumber}} {{.envName}} 研究中心订单申请
order.approval.failed-title = Clinflash IRT {{.projectNumber}} {{.envName}} 研究中心订单申请审批失败
order.single.start = <p>起运地:{{.start}}</p>
order.single.subject = <p>{{.label}}:{{.subject}}</p>
order.single.visit = <p>访视周期:{{.visit}}</p>
order.single.orderNumber = <p>订单号:{{.orderNumber}}</p>
order.single.expectedArrivalTime = <p>期望送达时间:{{.expectedArrivalTime}}</p>
order.single.logistics = <p>物流信息:{{.logistics}}</p>
order.single.lostReason = <p>丢失原因:{{.reason}}</p>
order.single.userName = <p>{{.userName}} 确认该订单已丢失.</p>
project.env = <p>项目环境:{{.envName}}</p>
project.name = <p>项目名称:{{.projectName}}</p>
project.number = <p>项目编号:{{.projectNumber}}</p>
project.cohort = <p>名称:{{.cohortName}}</p>
project.order.expectedArrivalTime = <p>期望送达时间:{{.expectedArrivalTime}}</p>
project.site.number = <p>中心编号:{{.siteNumber}}</p>
project.site.name = <p>中心名称:{{.siteName}}</p>
subject.alarm.content = <p>分层录入人数达到警戒值</p> <p>分层/警戒人数/实际人数：{{.info}}</p>
subject.alarm.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者警戒
subject.alert_threshold.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者上限设置提醒
subject.alert_threshold.cohort_title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者上限设置提醒
subject.pvUnblinding.content = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>中心编号:{{.siteNumber}}</p> <p>中心名称:{{.siteName}}</p> <p>受试者{{.subjectNumber}}已pv揭盲， 时间:{{.time}} 原因:{{.reason}}</p>
subject.pvUnblinding.title = Clinflash IRT 受试者pv揭盲
subject.random.content_no_group = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>随机时间:{{.time}}</p>
subject.random.content = <p>{{.label}}:{{.subjectNumber}}</p> <p>组别:{{.group}}</p> <p>随机号:{{.randomNumber}}</p> <p>随机时间:{{.time}}</p>
subject.random.content_sub = <p>{{.label}}:{{.subjectNumber}}</p>  <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>随机号:{{.randomNumber}}</p> <p>随机时间:{{.time}}</p>
subject.random.content_no_random_number_no_group = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机时间:{{.time}}</p>
subject.random.content_no_random_number = <p>{{.label}}:{{.subjectNumber}}</p> <p>组别:{{.group}}</p> <p>随机时间:{{.time}}</p>
subject.random.content_no_random_number_sub = <p>{{.label}}:{{.subjectNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>随机时间:{{.time}}</p>
subject.random.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者随机 {{.siteNumber}} {{.siteName}}
subject.add.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者登记 {{.siteNumber}} {{.siteName}}
subject.signOut.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者停用 {{.siteNumber}} {{.siteName}}
subject.signOut.content = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>停用时间:{{.stopTime}}</p> <p>原因:{{.reason}}</p>
subject.signOut.content_no_group = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>停用时间:{{.stopTime}}</p> <p>原因:{{.reason}}</p>
subject.signOut.content_sub = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>停用时间:{{.stopTime}}</p> <p>原因:{{.reason}}</p>
subject.signOut.content_no_random_number_no_group = <p>{{.label}}:{{.subjectNumber}}</p> <p>停用时间:{{.stopTime}}</p> <p>原因:{{.reason}}</p>
subject.signOut.content_no_random_number = <p>{{.label}}:{{.subjectNumber}}</p> <p>组别:{{.group}}</p> <p>停用时间:{{.stopTime}}</p> <p>原因:{{.reason}}</p>
subject.signOut.content_no_random_number_sub = <p>{{.label}}:{{.subjectNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>停用时间:{{.stopTime}}</p> <p>原因:{{.reason}}</p>
subject.replacement.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者替换 {{.siteNumber}} {{.siteName}}
subject.modify.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者修改 {{.siteNumber}} {{.siteName}}
subject.screen.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者筛选 {{.siteNumber}} {{.siteName}}
subject.unblinding-approval.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者紧急揭盲审批结果 {{.siteNumber}} {{.siteName}}
subject.unblinding-approval.content = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>紧急揭盲时间:{{.time}} </p> <p>紧急揭盲原因:{{.reason}}</p> <p>备注:{{.remark}}</p> <p>审批编号:{{.approvalNumber}}</p> <p>审批结果:{{.approvalResult}}</p> <p>原因:{{.rejectReason}}</p>
subject.ordinary-unblinding-approval.title = Clinflash IRT {{.projectNumber}} {{.envName}} 紧急揭盲成功 {{.siteNumber}} {{.siteName}}
subject.ordinary-unblinding-approval.content = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>中心编号:{{.siteNumber}}</p> <p>中心名称:{{.siteName}}</p> <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>紧急揭盲时间:{{.time}} </p> <p>紧急揭盲原因:{{.reason}}</p>
subject.pv-unblinding-approval.title = Clinflash IRT {{.projectNumber}} {{.envName}} 受试者PV揭盲审批结果 {{.siteNumber}} {{.siteName}}
subject.pv-unblinding-approval.content = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>pv揭盲时间:{{.time}} </p> <p>pv揭盲原因:{{.reason}}</p> <p>备注:{{.remark}}</p> <p>审批编号:{{.approvalNumber}}</p> <p>审批结果:{{.approvalResult}}</p> <p>原因:{{.rejectReason}}</p>
subject.unblinding.controlContent = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>紧急揭盲时间:{{.time}} </p> <p>紧急揭盲原因:{{.reason}}</p>
subject.unblinding.content = <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>紧急揭盲时间:{{.time}} </p> <p>紧急揭盲原因:{{.reason}}</p> <p>是否已经通知申办方:{{.isSponsor}}</p> <p>备注:{{.remark}}</p>
subject.unblinding.title = Clinflash IRT {{.projectNumber}} {{.envName}} 紧急揭盲成功 {{.siteNumber}} {{.siteName}}
user.accept = 您邀请的用户{{.userName}}，邮箱:{{.email}}已接受邀请
user.invite = <p> 您已获邀加入Clinflash参与随机试验研究，<a href={{.url}}>系统链接</a>，您的初始密码为:{{.password}}，</p> <p>{{.url}}</p> <p>若点击链接后未正常打开页面，请手动复制链接粘贴到浏览器地址栏后访问.</p>
user.join = <p>{{.customerName}}的{{.userName}}邀请你加入随机实验研究。 <a href={{.url}}>接受邀请</a></p> <p>{{.url}}</p> <p>此链接两天内有效，请及时完成操作。若点击链接后未正常打开页面，请手动复制链接粘贴到浏览器地址栏后访问.</p>
user.locking = 您的账号{{.email}}连续5次密码输入错误，账号已锁定请联系管理员或使用忘记密码操作.
user.notice.customer.bind.title = 「客户授权通知」
user.notice.project.bind.title = 「项目授权通知」
user.notice.return.login = 返回登录
user.notice.title = 项目授权通知
user.notice.customer-title = 客户授权通知
user.notice_customer = 您的帐户 {{.email}} 已成功授权[{{.customer}}]客户上，请及时登录系统查看。
user.notice_project = 您的帐户 {{.email}} 分配到新的临床试验项目[{{.project}}]，请及时登录系统查看。
user.resend_invite = Clinflash Cloud已为您创建账号，请在7天内访问此链接 {{.link}} 激活您的账号。如非您本人操作，请忽略！
user.resend_invite_title = Clinflash Cloud - 激活账号
user.reset = <p>您的密码已经被管理员重置，新密码为:{{.password}}.<a href={{.url}}>系统链接</a></p> <p>{{.url}}</p> <p>若点击链接后未正常打开页面，请手动复制链接粘贴到浏览器地址栏后访问.</p>
user.retrieve = <p>您申请通过邮箱重置密码，如非本人操作请忽略.<a href={{.url}}>重置密码</a></p> <p>{{.url}}</p> <p>此链接7天内有效，请及时完成操作。若点击链接后未正常打开页面，请手动复制链接粘贴到浏览器地址栏后访问.</p>
subject_urgentUnblindingApproval_agree = 已通过
subject_urgentUnblindingApproval_reject = 已拒绝
mail_security = 此电子邮件包含安全链接，请不要与他人分享此电子邮件、链接或访问代码。
mail_noreply = 本电子邮件由系统自动发送，请勿直接回复。
mail_copyright = Copyright ©2020 易迪希医药科技（嘉兴）有限公司 版权所有
mail_upper_half = <!DOCTYPE html> <html lang="en"> <head> <meta charset="utf-8"/> <title>Clinflash IRT</title> <style> .text-body { font-size: 14px; color: dimgrey; } </style> </head> <body> <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="font-size: 14px; background-color: #fff; table-layout: fixed; border-top: #0A47ED solid 6px;"> <tbody> <tr> <td colspan="12" style="padding: 40px 0 40px 50px;"> <img style="width: 238px; height: 40px;" src="{{.irt_url}}//api/img/mail"/> </td> </tr> <tr> <td colspan="12" style="padding: 5px 50px;"> <div class="text-body">
mail_lower_half =  </div> </td> </tr> </tbody> <tfoot> <tr> <td colspan="12" style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3"> <div>此电子邮件包含安全链接，请不要与他人分享此电子邮件、链接或访问代码。</div> <div>本电子邮件由系统自动发送，请勿直接回复。</div> <div>Copyright ©2020 易迪希医药科技（嘉兴）有限公司 版权所有</div> </td> </tr> </tfoot> </table> </body>
mail_lower_half_zh_en =  </div> </td> </tr> </tbody> <tfoot> <tr> <td colspan="12" style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3"> <div>此电子邮件包含安全链接，请不要与他人分享此电子邮件、链接或访问代码。</div> <div>本电子邮件由系统自动发送，请勿直接回复。</div> <div>Copyright ©2020 易迪希医药科技（嘉兴）有限公司 版权所有</div> </td> </tr> <tr> <td colspan="12" style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3"> <div>This email contains a secure link. Please do not share this email, link, or access code with others.</div> <div>This email is sent automatically by the system, please do not reply directly.</div> <div>Copyright ©2020 Clinflash Healthcare Technology (Jiaxing) Co.,Ltd. All Rights Reserved</div> </td> </tr> </tfoot> </table> </body>

[export]
dispense_list_download_name = 发放报表
export.project = 项目
export.projects.number = 项目编号
export.projects.name = 项目名称
export.projects.cohort = 群组
export.projects.stage = 阶段
export.barcode = 条形码
export.dispensing.auto = 自动
export.dispensing.first = 首次
export.dispensing.medicine = 研究产品编号
export.dispensing.medicineName = 研究产品名称
export.dispensing.realMedicine = 实际使用研究产品
export.dispensing.otherMedicineCount = 未编号研究产品数量
export.dispensing.outVisit = 计划外
export.dispensing.reissue = 补发
export.dispensing.replace = 替换
export.dispensing.retrieve = 取回
export.dispensing.register = 登记
export.dispensing.cancel = 撤销
export.dispensing.invalid = 不参加访视
export.dispensing.recover = 恢复发放
export.dispensing.replaceMedicine = 已替换研究产品编号
export.dispensing.room = 房间号
export.dispensing.siteName = 中心名称
export.dispensing.siteNumber = 中心编号
export.dispensing.subject = 受试者
export.dispensing.time = 操作时间
export.dispensing.type = 操作类型
export.dispensing.visit = 访视名称
export.dispensing.visit_number = 访视号
export.dispensing.visitSign = 计划外发放
export.dispensing.visit_apply = 访视申请
export.dispensing.out_visit_apply = 计划外申请
export.dispensing.reissue_apply = 补发申请
export.dispensing.replace_apply = 替换申请
export.dispensing.is_replace = 是否是替换研究产品编号
export.dispensing.is_real = 是否是实际使用研究产品
export.dispensing.operate_time = 发放操作时间
export.dispensing.realMedicineNumber = 实际使用研究产品编号
export.dispensing.weight = 体重
export.dispensing.height = 身高
export.dispensing.age = 年龄
export.random_config.SubjectReplaceText = 标签(即替换文本)：
export.random_config.accuracy = 位数精确值：
export.random_config.attribute = 项目属性
export.random_config.blind = 是否盲法：
export.random_config.countryLayered = 是否国家分层：
export.random_config.regionLayered = 是否区域分层：
export.random_config.createBy = 生成者：
export.random_config.createDate = 生成时间：
export.random_config.runningTime = 模拟运行开始时间：
export.random_config.generationTime = 模拟报告生成时间：
export.random_config.digit = 位数限制：
export.random_config.dispensing = 是否发放：
export.random_config.export = 配置报告
export.random_config.simulation_pdf = 模拟随机报告
export.random_config.factor = 分层因素：
export.random_config.no = No
export.random_config.group = 组别：
export.random_config.instituteLayered = 是否中心分层：
export.random_config.isFreeze = 运行运送算法时将隔离的单品计算为研究机构中可用存货的一部分：
export.random_config.isRandom = 中心没有分配随机号不能入组：
export.random_config.list = 随机列表：
export.random_config.prefix = 是否使用前缀：
export.random_config.random = 是否随机：
export.random_config.random_design = 随机配置
export.random_config.ratio = 组间比例：
export.random_config.report = Clinflash IRT是一个随机和试验研究产品供应管理系统，可用此系统进行随机、发放及试验研究产品供应管理。此配置报告包括项目属性、随机设计、治疗设计，用于各方人员可以快速的审核、批准项目的设置，以及项目文件的存档。
export.random_config.configure_report = Clinflash IRT是一个随机和试验研究产品供应管理系统，可用此系统进行随机、发放及试验研|究产品供应管理。此配置报告包括项目属性、随机设计、治疗设计，用于各方人员可以快速的审|核、批准项目的设置，以及项目文件的存档。
export.random_config.report_pdf = Clinflash IRT是一个随机和试验研究产品供应管理系统，可用此系统进行随机、发放及试验研究产品供应管理。此模拟随机报告包含项目属性、随机设计、随机模拟设置参数、分层总览、中心总览和详情，用于各方人员可以快速的审核、批准项目的设置，以及项目文件的存档。
export.random_config.directory = 目录
export.random_config.summary = 概述
export.random_config.unitCapacity = 单位容量：
export.random_config.unitStandard = 单位计算标准：
export.random_config.ageType = 按年龄
export.random_config.weightType = 按体重
export.random_config.bsaType = 简易体表面积
export.random_config.customerBsaType = 其他体表面积
export.random_config.table.code = 组别代码
export.random_config.table.count = 随机发放数量
export.random_config.table.countMax = 发放数量
export.random_config.table.formulas = 随机发放范围
export.random_config.table.label = (组合)发放标签
export.random_config.table.formula = 自定义公式
export.random_config.table.formulasType = 公式
export.random_config.table.medicine = 研究产品名称
export.random_config.table.spec = 研究产品规格
export.random_config.total = 总例数：
export.random_config.treatment_design = 治疗设计
export.random_config.yes = Yes
export.random_config.type = 随机类型：
export.room.history.room = 历史查看房间号
export.room.history.time = 查看时间
export.room.history.user = 用户
export.room.project = 项目
export.unblinding.remark = 揭盲备注
export.unblinding.reason = 揭盲原因
export.unblinding.reason_mark = 揭盲原因备注
export.unblinding.operator = 揭盲操作人
export.unblinding.operate_time = 揭盲操作时间
export.random.register = 登记
export.random.random = 随机
export.random.exit = 停用
export.random.unBlind = 已揭盲(紧急)
export.random.pv = 已揭盲(PV)
export.random.screenSuccess = 筛选成功
export.random.screenFail = 筛选失败
export.random.finish = 完成研究
export.random.toBeRandom = 待随机
export.random.number = 随机号
export.user.name = 姓名
export.user.email = 邮箱
export.user.role = 角色
export.user.create_time = 创建时间
export.user.status_effective = 有效
export.user.status_invalid = 无效
export.medicine.serial_number = 序列号
export.medicine.ip_name = 研究产品名称
export.medicine.ip_number = 研究产品编号
export.medicine.expiration_date = 有效期
export.medicine.batch_number = 批次号
export.medicine.package_number = 包装号
export.medicine.package_number_serialNumber = 包装序列号
export.subject.number = 受试者号
medicine.status.available = 可用
medicine.status.delivered = 已确认
medicine.status.destroy = 已销毁
medicine.status.expired = 已过期
medicine.status.inStorage = 入库中
medicine.status.lose = 丢失/作废
medicine.status.quarantine = 已隔离
medicine.status.receive = 已领药
medicine.status.return = 已退货
medicine.status.sending = 已运送
medicine.status.stockPending = 待入库
medicine.status.toBeConfirmed = 待确认
medicine.status.toBeWarehoused = 待入仓
medicine.status.transit = 已运送
medicine.status.used = 已使用
medicine.status.apply = 已申请
medicine.status.frozen = 冻结
medicine.status.locked = 锁定
medicineOrder_download_packageNumber = 包装号
medicineOrder_download_packageMethod = 运送方式
medicineOrder_download_packageMethodSingle = 单品
medicineOrder_download_packageMethodPackage = 包装
medicineOrder_download_batchNumber = 批次号
medicineOrder_download_cancelDate = 取消时间
medicineOrder_download_cancelUser = 取消人
medicineOrder_download_count = 研究产品数量
medicineOrder_download_createDate = 创建时间
medicineOrder_download_createUser = 创建人
medicineOrder_download_expiredDate = 有效期
medicineOrder_download_fileName = 订单报表
medicineOrder_download_medicineNumber = 研究产品编号
medicineOrder_download_medicine = 研究产品
medicineOrder_download_number = 订单号
medicineOrder_download_orderInfo = 订单详情
medicineOrder_download_other = 数量
medicineOrder_download_receiveDate = 接收时间
medicineOrder_download_expectedArrivalTime = 期望送达时间
medicineOrder_download_actualReceiptTime = 实际接收时间
medicineOrder_download_receiveInstitute = 目的地
medicineOrder_download_receiveUser = 接收人
medicineOrder_download_sendInstitute = 起运地
medicineOrder_download_status = 订单状态
medicineOrder_download_cancelReason = 取消原因
medicineOrder_download_confirmUser = 确认人
medicineOrder_download_confirmDate = 确认时间
medicineOrder_download_closeUser = 关闭人
medicineOrder_download_closeDate = 关闭时间
medicineOrder_download_closeReason = 关闭原因
medicineOrder_download_sendUser = 运送人
medicineOrder_download_sendDate = 运送时间
medicineOrder_download_lostUser = 丢失人
medicineOrder_download_lostDate = 丢失时间
medicineOrder_download_lostReason = 丢失原因
medicineOrder_download_endUser = 终止人
medicineOrder_download_endDate = 终止时间
medicineOrder_download_endReason = 终止原因
medicineOrder_download_supplier = 物流供应商
medicineOrder_download_supplierOther = 其他物流
medicineOrder_download_supplierNumber = 物流单号
medicine_batch_number = 批次号
medicine_download_batch = 批次号
medicine_download_spec = 规格
medicine_download_packageNumber = 包装号
medicine_download_depot_name = 库房统计报表
medicine_download_expiredDate = 有效期
medicine_download_location = 位置
medicine_download_name = 研究产品名称
medicine_download_number = 研究产品编号
medicine_download_orderNumber = 订单号
medicine_download_country = 国家(分层属性)
medicine_download_region = 区域(分层属性)
medicine_download_site_country = 国家
medicine_download_site_region = 区域
medicine_download_site = 中心名称
medicine_download_site_name = 中心药房报表
medicine_download_dtp_sku = 研究产品单品报表
medicine_download_status = 状态
medicine_download_storehouse = 库房
medicine_download_reason = 原因
medicine_download_operFree = 隔离
medicine_download_operRelease = 解隔离
medicine_download_operLost = 丢失/作废
medicine_download_operUse = 设为可用
medicine_download_operator = 操作人
medicine_download_time = 操作时间
medicine_download_freeze_reason = 隔离原因
medicine_download_freeze_operator = 隔离操作人
medicine_download_freeze_time = 隔离操作时间
medicine_download_release_reason = 解隔离原因
medicine_download_release_operator = 解隔离操作人
medicine_download_release_time = 解隔离操作时间
medicine_download_lost_reason = 丢失/作废原因
medicine_download_lost_operator = 丢失/作废操作人
medicine_download_lost_time = 丢失/作废操作时间
medicine_download_use_reason = 设为可用原因
medicine_download_use_operator = 设为可用操作人
medicine_download_use_time = 设为可用操作时间
medicine_download_site_number = 中心编号
medicine_expiration_date = 有效期
medicine_list_download_name = 研究产品列表
medicine_name = 研究产品名称
medicine_number = 研究产品编号
medicine_package = 包装号
medicine_package_barcode = 包装号-条形码
medicine_package_barcode_short = 包装号&条形码组合
medicine_code = 可识别短码
medicine_examine_uccess = 审核通过
medicine_examine_fail = 审核失败
medicine_examine_update = 修改
medicine_examine_release = 放行
medicine_barcode_code = 研究产品-条形码
medicine_barcode_code_short = 条形码&短码组合
medicine_serial_number = 序列号
medicine_status = 状态
operator.people = 操作人
operator.content = 操作内容
operator.reason = 原因
operator.time = 操作时间
order_status_cancelled = 已取消
order_status_not_cancelled = 当前订单状态不可终止，请刷新页面
order_status_wms_not_cancelled = 不可以取消订单
order_status_lose = 已丢失
order_status_received = 已接收
order_status_requested = 已确认
order_status_toBeConfirmed = 待确认
order_status_transit = 已运送
order_status_apply = 已申请
order_status_terminated = 已终止
order_status_close = 已关闭
random.number = 随机号
randomList.export.available = 可用
randomList.export.block = 区组
randomList.export.center = 中心
randomList.export.delete = 已作废
randomList.export.inactivate = 无效
randomList.export.group = 组别
randomList.export.number = 随机号
randomList.export.status = 状态
randomList.export.subject = 受试者
randomList.export.used = 已使用
randomNumber.export.status.invalid = 无效
randomNumber.export.status.used = 已使用
randomNumber.export.status.unused = 可用
randomNumber.export.status.unavailable = 不可用
randomization.accuracy.ones = 小于等于
randomization.accuracy.twos = 等于
randomization.type.ones = 区组随机
randomization.type.twos = 最小化随机
simulated.random.list.factor = 分层因素
simulated.random.list.group = 组别
simulated.random.list.name = SimulationResult
simulated.random.list.number = 随机号
simulated.random.list.only = 只允许启动一个随机列表
simulated.random.list.runCount = 运行次数
simulated.random.list.site = 研究机构
simulated.random.list.subject = 受试者号
simulated.random.number.not.enough = 受试者数必须小于随机号个数
site.name = 中心名称
site.number = 中心编号
subject.group = 组别
subject.number = 受试者号
subject.replace = 替换
subject.replace_subject = 替换受试者号
subject.replace_number = 替换受试者随机号
subject.random.fileName = 随机报表
subject.status = 状态
subject.status.registered = 已登记
subject.status.random = 已随机
subject.status.sign.out = 已停用
subject.status.unblinding = 已揭盲(紧急)
subject.status.screen.success = 筛选成功
subject.status.screen.fail = 筛选失败
subject.status.finish = 完成研究
subject.status.to.be.random = 待随机
subject.status.join = 已入组
subject.unblinding.fileName = 揭盲报表
subject.unblinding.sponsor = 是否已经通知申办方
user.createDate = 创建时间
user.depot = 库房
user.email = 邮箱
user.name = 姓名
user.roles = 角色
user.site = 中心
user.status = 状态
user.status.activited = 已激活
user.status.not.active = 未激活
user.status.enable = 已启用
user.status.unauthorized = 未授权
user.status.open = 已开启
user.status.disable = 已禁用
system_suggest_value = 系统建议值
plan_number = 计划随机数
source_ip_upload_history_name = 盲底名称
source_ip_upload_history_rows = 行数
source_ip_upload_history_rows_succeeded = 成功
source_ip_upload_history_rows_failed = 失败

[error] # 返回前端的报错
app.unauthorized = 应用未授权
auth.fail = 认证失败
customer.not.exist = 客户不存在
customers.delete.admin = 当前操作人在该客户下没有admin权限，无权操作
customers.delete.message = 客户下存在用户，不能删除
customers.duplicated.names = 客户名称重复
edc.error = 请求失败，项目数据推送方式为IRT全量推送。
edc.version = EDC接口版本错误，请联系EDC相关人员。
edc.add.relation.site.error = 关联中心失败，请重新关联中心
edc.block.is.not.nil.error = blockRepeatNo参数为空，发放失败，请重新确认配置
edc.check.matching.value.error = 复选框有未匹配到的value
edc.configure.drug.error = 研究产品查询为空，请重新确认EDC研究产品配置
edc.configure.visit.error = 访视周期查询为空，请重新确认EDC访视周期配置
edc.drug.number.error = 查询研究产品编号出错，请重新查询
edc.drug.number.mismatch = 查询研究产品编号结果不匹配，请确认替换研究产品编号信息
edc.drug.reissue.error = 补发失败，请联系非盲人员完成操作
edc.drug.replace.error = 替换失败，无法对上一次已完成的访视进行研究产品替换，请重新选择。
edc.drug.replace.nil.nil = 研究产品编号为空，请重新确认后操作
edc.drug.replace.nil.error = 替换研究产品编号查询失败，请联系IRT工程师处理
edc.drug.type.error = type参数为空，发放失败，请重新确认配置
edc.env.error = 项目环境信息不存在，请联系IRT系统工程师查看
edc.factor.error = Cohort因素为空，请重新确认
edc.instance.is.not.nil.error = instanceRepeatNo参数为空，发放失败，请重新确认配置
edc.matching.value.error = Value匹配失败，请重新确认单选按钮组或者下拉框的值
edc.multiple.subject = 受试者号查询重复，请确认受试者号信息。
edc.no.subject = 修改失败，请联系IRT工程师处理
edc.parameter.error = 项目编号/环境/中心编号（名称）/受试者号为空，请重新确认EDC配置
edc.project.env.number.error = 项目编号/环境为空，对接失败，请重新确认配置
edc.query.factor.error = Cohort查询失败，请重新确认Cohort因素配置
edc.query.project.error = 项目查询失败，请联系IRT系统工程师查看
edc.query.project.number.error = 项目查询失败，请联系IRT系统工程师查看
edc.query.project.dtp.error = DTP项目不支持和EDC对接
edc.query.site.number.error = 中心查询重复，请在IRT系统中确认是否存在多个中心
edc.query.site.number.relation.site.error = 中心查询重复，请在IRT系统中确认是否存在多个中心
edc.register.synchronization.error = 不支持全量同步，接口调用失败，请重新确认EDC对接功能配置
edc.relation.site.error = 关联中心失败，请联系IRT系统工程师查看
edc.site.error = 中心查询失败，请联系IRT系统工程师查看
edc.standard.lost.site.error = 中心信息不全，请联系IRT系统工程师修改
edc.standard.site.error = 中心信息缺失，请联系IRT系统工程师添加
edc.start.site.error = 中心启用或激活失败，请重新操作
edc.subject.after.dispensing.error = 受试者未随机，发放失败，请重新确认
edc.subject.dispensing.error = 受试者未随机，发放失败，请重新确认
edc.subject.existence = 受试者已存在，请勿重新添加
edc.subject.random.number.error = 受试者号为空，请重新确认EDC配置
edc.subject.register.error = 受试者未登记，请重新确认
edc.subject.status.dispensing.error = 受试者已随机，随机前发放失败，请重新确认
edc.subject.site.error = IRT返回受试者所在中心不一致，请在IRT上操作受试者转中心后再操作发药行为
edc.unable.at.random = 非随机项目功能支持，请确认项目随机配置
edc.visit.error = 访视周期查询为空，请重新确认EDC访视编号与访视周期配置关系
edc.visit.no.dispensing.error = 当前访视发放失败，请确认随机前/随机后的属性配置
edc.visit.number.drug.configure.error = 研究产品查询重复，请重新确认访视编号与EDC研究产品配置关系
edc.visit.number.error = 访视编号为空，对接失败，请重新确认配置
environment.duplicated.names = 环境名称重复
environment.alertThresholds.limitError = 仅发放项目，阈值上限条件仅允许配置登记/筛选或入组，请重新确认。登记/筛选，根据实际配置控制显示。
environment.alertThresholds.attributeError = 项目属性为“仅发放”，当前阈值上限条件配置已失效，请重新配置。
file_emtpy = 文件列表为空
col_empty_error: 上传随机表存在空白列，请修改后再进行上传
medicine.errorSupplyMode = 补充方式不一致，提交失败，请重新修改。
medicine.errorAutoSupplyMode = 自动配给量不一致，提交失败，请重新修改。
medicine.errorSupplyModeHint = 补充方式不一致。
medicine.errorAutoSupplyModeHint = 自动配给量不一致。
medicine_duplicated = 研究产品已存在
medicine_duplicated_number = 请勿重复上传相同研究产品编号
medicine_duplicated_package_number = 请勿重复上传相同包装号
medicine_duplicated_serial_package_number = 请勿重复上传相同包装序列号
medicine_duplicated_serial_number = 请勿重复上传相同序列号
medicine_upload_package_number = 包装号未上传，请重新上传。
medicine_upload_package_serial_number = 包装序列号未上传，请重新上传。
medicine_upload_package_count = 包装号数量错误，请重新上传。
medicine_upload_drug_name = 请选择正确的研究产品。
medicine_upload_package_serial_count = 包装序列号数量错误，请重新上传。
medicine_not_exist = 研究产品不存在
medicine_packlist_upload_check = 研究产品编号不存在，请先上传研究产品编号
medicine_packlist_upload_firstPack = 包装号不能为空，请重新确认。
minimize_bias_probability_tips = 偏倚概率不可为空
minimize_layered_tips = 未获取到组别分层偏差，请重新确认。
project_edc_irt_return = 受试者编号前缀规则与EDC设置不一致，请重新确认
variable_duplicated = 变量ID重复，请重新确认。
form_name_duplicated = 表单名称重复，请重新确认。
option_label_duplicated = 添加重复，请重新确认。
combined_dispensation_label_duplicated = 组合标签内研究产品发放方式需一致，请重新确认。
form_invalid_error = 无效失败，字段已在研究产品配置中应用。
update_medicine_status_error = 部分研究产品状态已变更，请重新选择。
page_notice.mail.send_fail = 发送失败
page_notice.mail.send_success = 发送成功
page_notice.system_update.email_error = 邮箱地址错误
page_notice.system_update.others = 其他
page_notice.system_update.quota_exceeded = 超过主帐户每日发送配额
page_notice.system_update.timeout = 邮件服务器超时
project.site.delete = 该中心已被使用不能禁用
project.storehouse.delete = 该库房已被使用不能删除
project.storehouse.had.add = 库房已存在
project.storehouse.unConnected = 该库房未对接物流仓库，请勿推送入库数据
project.user.join = <p>{{.customerName}}邀请你加入随机实验研究</p> <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p>
project.user.title = Clinflash IRT 项目邀请
projects.duplicated.names = 项目名称重复
projects.duplicated.numbers = 编号重复，请重新输入
randomNumber.exist.used = 错误，该区组存在使用过的随机号，无法重新分配其他分层，请重新确认。
randomNumber.exist.used.clean = 错误，该区组存在使用过的随机号，无法清空其他分层，请重新确认。
random_filed_error = 有重复的变量字段，请重新确认。
random_attribute_error = 最小化随机计算已配置”不适用“，请修改后再创建。
random_length_error = 错误!生成的随机号长度已经超过了设置的号码长度
random_list_download_name = 随机号报表
random_number_block_error = 区组需唯一，请重新确认。
random_number_error = 错误，重复的随机号，请重新确认。
random_number_format_error = 上传列表格式错误，请重新确认。
random_number_format_error_trim = 上传数据有空格，请重新上传
random_list_name_duplicated = 名称已存在
random_total_error = 错误，总例数须要被各分组比例之和整除，请重新确认。
random_duplicated_factor = 已存在相同分层因素，请勿重复配置
randomNumber_status_error = 随机号状态已变更，请重新确认。
randomization.upload.blockSize = 随机表中区组长度与系统中配置的可接受的区组大小不一致，请重新确认。
randomization.upload.group = 错误，组别与配置的治疗组别不一致。
randomization.upload.group_error = 上传失败，上传组别与系统配置不一致，请重新确认。
randomization.upload.group_tip_error = 上传盲底组别数量和系统配置不一致，请确认。
roles.delete.message = 角色已使用，不能删除
roles.duplicated.names = 重复添加，请重新修改
roles.duplicated.sys_check = 不允许修改系统管理员角色菜单权限
roles.duplicated.project_admin_check = 不允许修改项目管理员项目查看权限
shipment_order_add_info = 因为是盲法项目，所以请选择2组研究产品名称
shipment_order_buffer_info = 当前中心的库存量大于最大缓冲量
shipment_order_cancel_not_exist = 物流中心该出库单号不存在
shipment_order_cancel_not_status = 物流已发货，无法取消该运单
shipment_order_initial_info = 当前中心的无法重复初始发放
shipment_order_create_error = 没有需要创建的研究产品，请重新选择
shipment_order_mode_info = 起运地为中心时，补充方式只能为发放数量补充方式
shipment_order_over_warning_info = 当前中心的可用库存量高于警戒值
shipment_order_sendAndReceive_info = 起运地与目的地不能重复
shipment_order_dtp_info = DTP订单不允许部分确认、接收
shipment_order_check_packageNumber = 整盒包装数量和配置中不一致
shipment_order_supply_info = 供应计划未包含全部研究产品供应配置，请联系项目管理员配置。
shipment_order_supply_page_error = 无法创建订单，“{{.currentName}}”为包装运输，同包装研究产品“{{.otherName}}”所属群组状态为”已完成/草稿/已终止“，请重新确认。
shipment_order_create_modeMax_info = 目的地可用库存已大于最大缓冲量，研究产品订单不生成
shipment_order_no_supply = 请先配置供应计划。
shipment_order_ratio_err = 目的地中心未开启供应比例配置。
shipment_out_of_stock = 库存不足，无法创建研究产品订单，请联系管理员补充库存。
shipment_change_out_of_stock = 库存不足，无法更换，请联系管理员补充库存。
shipment_order_supplyRatio_create_fail = 订单生成失败，研究产品数量与比例不匹配，请重新确认。
site.disabled = 该中心未被启用
site.had.bind = 该中心曾被启用不能删除
sites.duplicated.number = 编号重复，请重新输入
storehouse.had.bind = 该库房曾被启用不能删除
storehouses.duplicated.number = 库房编号重复
subject_cohort_check = 当前阶段非入组状态不能进行随机操作
subject_cohort_check_register = 登记失败，草稿态不允许登记。
subject_register_number_fail = 登记失败，受试者数量已达最大位长数。
subject_cohort_last_group = 上阶段该受试者未随机入组
subject_factor_check_error = 随机数已超过项目上限或项目非入组状态，请及时联系系统配置人员调整后再操作随机。
simulate_random_site_cont_error = 保存失败，区域/国家数必须小于等于中心数。
subject_factor_no_null = 分层因素不能为空
subject_medicine_batch_count = 发放失败，无可用有效期内发放的研究产品，请确认。
subject_medicine_count = 发放失败，中心库存不足。
subject_medicine_count_store = 发放失败，库房库存不足。
subject_medicine_alarm_count = 发放失败，中心库存不足。
subject_medicine_dose_error = 发放失败，无法匹配访视对应的发放剂量。
subject_medicine_dtp_error = 组合标签内研究产品发放方式需一致，请重新确认。
subject_medicine_label_select = 请选择研究产品标签或研究产品
subject_medicine_label_select_outsize = 发放失败，未配置独立计划外访视发放规则，请确认。
subject_medicine_count_real = 库存内无该研究产品，请重新输入
subject_medicine_count_real_same = 登记研究产品不允许相同批次，请重新输入
subject_medicine_other = 未编号研究产品
subject_no_random = 没有匹配的随机号，请重新确认。
subject_no_drug_configure = 随机失败，研究产品未配置，请及时联系系统配置人员调整后再操作随机。
subject_no_enough_drug = 随机失败，研究产品库存不足，请确认。
subject_site_no_depot = 无法随机/发放，研究中心未绑定库房，请联系管理员配置。
subject_no_register = 受试者未登记
subject_replace_no_register_screenFail = 替换失败，替换受试者未登记/筛选失败，请重新输入。
subject_replace_no_site_fail = 替换失败，替换受试者号不在当前中心内，请重新确认。
subject_replace_no_site_fail_cohort = 替换受试者号不在当前群组内，请重新确认。
subject_replace_no_site_fail_stage = 替换受试者号不在当前阶段内，请重新确认。
subject_replace_no_cohort_site_fail = 替换失败，替换受试者号不在当前群组中心内，请重新确认。
subject_replace_no_stage_site_fail = 替换失败，替换受试者号不在当前阶段中心内，请重新确认。
subject_replace_random = 替换失败，替换受试者已随机，请重新输入。
subject_replace_sign_out = 替换失败，替换受试者已停用，请重新输入。
subject_replace_register = 替换失败，替换受试者已登记，请重新输入。
subject_no_replace = 你输入的受试者号已完成随机不能替换
subject_no_visit = 请设置完访视周期后在进行登记
subject_number_repeat = 受试者已存在，请重新确认
subject_transfer_fail = 转运失败，新中心受试者号{{.subjectNo}}校验重复，请重新确认。
subject_switch_cohort_fail1 = 受试者状态：当前仅支持状态处于随机前的受试者进行群组切换
subject_switch_cohort_fail2 = edc对接不能勾选随机前修改推送：当前项目与EDC进行了随机前修改的数据对接，不支持进行群组切换
subject_switch_cohort_fail3 = 发药：受试者已产生发药，不支持进行群组切换
subject_switch_cohort_fail4 = 表单分层不一致：切换群组与当前群组表单分层配置不同，请确认
subject_random_error = 随机前的发放未完成，不能进行随机
subject_random_number_existence = 该随机号在系统中已存在不可使用
subject_status_no_cancel = 当前受试者状态不能撤销
subject_status_no_delete = 当前受试者状态不能删除
subject_status_no_dispensing = 当前受试者状态不能发放
subject_status_no_join = 当前受试者状态不能操作不参加访视，请重新确认。
subject_status_no_random = 当前受试者状态不能随机
subject_status_no_reissue = 当前受试者状态不能补发
subject_status_no_replace = 当前受试者状态不能替换
subject_status_no_replace_dispensing = 当前受试者状态不能进行研究产品替换
subject_status_no_retrieval = 当前受试者状态不能取回研究产品
subject_status_no_sign_out = 当前受试者状态不能停用
subject_status_no_unblinding = 当前受试者状态不能揭盲
subject_status_no_update = 当前受试者状态不能修改
subject_visit_dispensing = 当前访视已发放
subject_visit_dispensing_no_join = 当前访视已设置不参加发放
subject_visit_dispensing_no_order = 请按访视顺序发放
subject_visit_dispensing_no_order_confrim = 上一个访视订单待确认
subject_visit_dispensing_no_order_dtp = 当前访视配置无法使用中心（直接寄送受试者）/库房（直接寄送受试者），请检查配置
subject_visit_dispensing_store = 当前中心未绑定库房
subject_visit_dispensing_set_no_join = 当前访视不允许设置不参加，请刷新页面
subject_visit_dispensing_no_reissue = 补发失败，无法对当前计划外访视进行补发，请重新选择。
subject_visit_dispensing_no_reissue_dose = 补发失败，访视判断/剂量水平对应选项已删除，请重新确认。
subject_visit_dispensing_no_site = 该中心暂未分配库房
subject_visit_dispensing_order_status = 当前订单状态不允许登记实际使用研究产品
subject_visit_dispensing_order_status_last = 上一个访视的订单状态未配送，当前访视无法发放
subject_visit_cannot_cancel = 当前项目属性不允许进行取回撤销操作
subject_visit_cannot_replace = 当前研究产品状态不允许进行替换操作
medicine_approval_err = 当前有未完成解隔离审批的任务，请先联系审批人员完成审批后再申请解隔离。
medicine_release_err = 至少选择一项或输入数量
upload_medicines_cell = 请删除Excel最后的空行数据
upload_medicines_drugName = 研究产品名称不匹配
upload_medicines_info = 请勿上传空模板数据
upload_translate_info = 请勿上传空模板数据
random_type_error = 随机表随机类型与系统配置不一致
random_group_error = 随机表组别与系统配置不一致
random_factor_error = 随机表分层因素/选项与系统配置不一致
medicine_delete_error = 研究产品已被订单使用，不能删除
factor_calc_not_match = 计算结果无法匹配到对应的分层，请重新确认。
factor_not_match = 无法匹配到对应分层，请重新确认
subject_visit_err = 模板不适配，当前日期已超过最小窗口期日期，请重新确认。
subject.register.enrollment.full = 登记失败，当前状态为入组已满。
subject.dispensing.enrollment.full = 发放失败，入组已满，请联系管理员确认。
subject.register.screen.fail.enrollment.full = 筛选失败，当前状态为入组已满。
user.customer.authorization.success = 授权成功
user.customer.bind.error = 客户组无该用户，请联系管理员添加用户
user.customer.bind.error1 = 客户组无该用户
user.customer.bind.success = 绑定成功
user.exist.env = 环境下已存在此用户
user.no.exist = 用户不存在
user.no.exist.customer = 用户不属于当前客户
user.password.error = 密码错误
user.phone.exist = 该手机号已绑定用户
user.resend.email.info = 用户状态已激活，无需发送激活邮件
users.authentication.failed = 认证失败，请重新登录
users.duplicated.customer = 用户已存在
users.duplicated.emails = 用户邮箱重复
users.duplicated.cloud-customer = Cloud中用户已禁用，无法添加。
users.duplicated.cloud-disable = Cloud已禁用
users.duplicated.cloud-delete = 用户已删除，请联系Cloud Admin确认。
users.duplicated.cloud-exist = 邮箱已存在
users.identify.code.incorrect = 验证码不正确
users.missing.phone = 手机号不完整，请引导用户登录cloud后在个人信息中修改
users.phone.not.exist = 账号不存在或无权限，请联系项目管理员添加
user_no_permission = 账号无操作权限，请联系项目管理员添加
visit_cycle_duplicated_number = 访视编号重复
visit_cycle_dtp_required = 请选择DTP方式
visit_cycle_duplicated_random1 = 已存在随机的访视，请勿重复配置
visit_cycle_duplicated_random3 = 已存在随机、发放的访视，请勿修改配置
visit_cycle_duplicated_version = 访视版本号重复
visit_cycle_formula_error = 公式无法识别，请重新确认。
visit_cycle_formula_visit_error = 已开启随机访视体重比较条件，请配置随机访视允许发放。
visit_cycle_visit_formula_error = 已开启随机访视体重为本次计算体重，请配置随机访视允许发放。
visit_cycle_formula_customer_error = 公式无法识别，请重新输入
visit_cycle_duplicated_random2 = 再随机类型的项目，最多只能配置2条随机的访视
work.task.error = 任务已完成，不可重复操作
work.task.deleted = 该任务已不存在，请刷新重试
work.task.scan.error = 该任务不需要进行扫码确认操作
work.task.packageScan.error = 该任务不需要进行包装扫码操作
work.task.package_number.error = 扫码失败，扫码产品非当前项目/环境中系统生成的研究产品
work.task.exist = 单次操作未完成，不可重复操作
work.task.medicine.error = 系统检测扫描列表已更新，请刷新重试
medicine_other_repeat = 添加重复，请重新添加
medicine_drug_configure_check = 删除失败，研究产品已使用或已产生库存。
medicine_drug_configure_other_check2 = 修改失败，“
medicine_drug_configure_other_check = ”已有库存数
medicine_drug_configure_other_check1 = ，无法重新自动准确计算库存，请重新确认数据后再操作。
medicine_other_repeat_formula = 公式不唯一，添加重复，请重新添加
medicine_open_setting_repeat1 = 已配置
medicine_open_setting_openTrue = 开放属性
medicine_open_setting_openFlase = 不开放属性
medicine_open_setting_repeat2 = ，需保持一致，请重新配置。
medicine_open_setting_repeat3 = 发放方式与已有配置不一致，请重新确认。
medicine_open_setting_approval = 保存失败，不允许仅添加单一盲态研究产品控制。
site_not_delete = 已登记、随机/发放的中心，不可设为无效
order_status_error = 订单状态异常，请返回列表重新操作
subject_status_error = 受试者状态异常，请返回列表重新操作
approval_task_error = 订单审批任务状态异常，请返回列表重新操作
urgentUnblinding_approval_task_error = 紧急揭盲审批任务状态异常，请返回列表重新操作
wms.cancel_order_fail = 佰诚库房订单取消失败
unblinding_code_error = 揭盲码错误
unblinding_password_error = 密码错误
common_configuration_error = 配置异常
subject_urgentUnblindingApproval_applicationed = 该受试者揭盲(紧急)申请已提交，请等待审批人审批确认。
subject_urgentUnblindingApproval_pv_applicationed = 该受试者揭盲(pv)申请已提交，请等待审批人审批确认。
supply_plan_duplicated_name = 名称重复，请重新输入
barcode_error = 保存失败，请先关闭包装号功能
barcode_rule = 研究产品条形码生成规则需保持一致，请确认
barcode_package_rule = 包装条形码生成规则需保持一致，请确认
form_field_used = 字段已使用，无法删除
planned_case_error = 当前随机数已超过项目入组上限，请及时联系系统配置人员调整后再操作随机。
planned_case_random_error = 随机失败，项目已达入组上限。
planned_case_register_error = 登记失败，项目已达登记上限。
planned_case_screen_error = 筛选失败，项目已达筛选成功上限。
subject_replace_auto_error = 替换失败，替换受试者随机号与当前激活随机表冲突，请修改规则后操作。
random_list_upload_error = 分层因素不一致，请同步后再操作。
subject_random_factor_error = 随机失败，请更新最新分层结果后再操作。
simulate_random_factor_error = 系统检测到不同随机表因素不同，无法同时模拟随机，请修改一致后操作。
simulate_random_factor_ratio_error = 模拟随机分层配置与实际分层配置不同，请编辑后再运行。
simulate_random_factor_ratio_list_error = 系统检测到随机配置与随机表不一致，请修改一致后操作。
simulate_random_factor_ratio_total_error = 分层例数和与随机人数不符，请调整。
simulate_random_factor_ratio_lack_error = 分层例数缺失，请补充。
subject_random_sequence_number_start_max_error = 顺序号起始值超过位数上限，请检查随机号配置。
edc_register_error = 登记失败，草稿态不允许登记，请联系IRT项目管理员，修改群组状态至“入组”后操作。
factor_calc_mapping_converge = 映射关系重合，请重新确认。
randomization_config_factor_not_calc_form = 自定义公式变量ID对应的表单字段缺失，请检查配置。
randomization_config_factor_not_calc = 变量ID配置计算冲突，请重新确认。
randomization_config_factor_not_calc_type = 变量ID配置控件冲突，请重新确认。
report.template.name_exist = 模版名称已存在
report.template.name_invalid = 模板名称有误
project_at_random_error = 再随机项目仅支持添加两个阶段
medicine_package_err = 修改失败，包装研究产品名称与设置不匹配，请重新确认

[enum] # 后端枚举 未使用
cohort.status.complete = 完成
cohort.status.draft = 草稿
cohort.status.enrollment = 入组
cohort.status.stop = 停止

[general]
common.operator = 操作
common.required = 请输入
common.error.default = 请求遇到了错误
common.error.request = 错误的请求
common.error.not-logged-in = 未登录
common.error.unauthorized = 未授权
common.error.not-found = 未知请求
common.checkbox = 复选框
common.date = 日期选择框
common.dateTime = 时间选择框
common.delete.fail = 删除失败
common.delete.success = 删除成功
common.duplicated.factors = 因素重复
common.duplicated.names = 名称重复
common.no = 否
common.input = 输入框
common.inputNumber = 数字输入框
common.load.fail = 加载失败
common.load.success = 加载成功
common.nil = 无
common.operation.edc.dsp.fail = 当前访视已发放，信息二次返回.
common.operation.edc.fail = 受试者已随机
common.operation.fail = 操作失败
common.operation.success = 操作成功
common.radio = 单选框
common.remark = 备注
common.save.fail = 保存失败
common.save.success = 保存成功
common.select = 下拉框
common.switch = 开关
common.textArea = 多行文本框
common.yes = 是
common.update.fail = 更新失败
common.update.success = 更新成功
common.wrong.parameters = 参数错误
common.country = 国家

[history]
history.dispensing.updateBatch = 研究产品【更新】，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}，状态：{{.status}}，数量：{{.count}}
history.dispensing.cancel = 【发放撤销】{{.label}}：{{.subject}}，撤销原因：{{.reason}}。
history.dispensing.dispensing = 【发放】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}。
history.dispensing.dispensing-other = 【发放】{{.label}}：{{.subject}}， 未编号研究产品名称/数量/批次/有效期：{{.medicine}}。
history.dispensing.dispensing-with-other = 【发放】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}。
history.dispensing.dispensing-with-other-reason = 【发放】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，原因：{{.reason}}
history.dispensing.dispensingVisit = 【计划外发放】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，计划外发放原因：{{.reason}}
history.dispensing.dispensingVisit-other = 【计划外发放】{{.label}}：{{.subject}}，未编号研究产品名称/数量/批次/有效期：{{.medicine}}，计划外发放原因：{{.reason}}
history.dispensing.dispensing-new = 【发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}} 研究产品编号：{{.medicine}}， 备注：{{.remark}}
history.dispensing.dispensing-other-new = 【发放】{{.label}}：{{.subject}}，{{.form}} {{.formula}}未编号研究产品名称/数量/批次/有效期：{{.medicine}}， 备注：{{.remark}}
history.dispensing.dispensing-with-other-new = 【发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}} 研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}} ，备注：{{.remark}}
history.dispensing.dispensing-with-other-reason-new = 【计划外发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}} 研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}， 计划外发放原因：{{.reason}}
history.dispensing.dispensingVisit-new = 【计划外发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}} 研究产品编号：{{.medicine}}，计划外发放原因：{{.reason}}
history.dispensing.dispensingVisit-other-new = 【计划外发放】{{.label}}：{{.subject}}，{{.form}} {{.formula}} 未编号研究产品名称/数量/批次/有效期：{{.medicine}}， 计划外发放原因：{{.reason}}
history.dispensing.dtp-dispensing = 【申请】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，备注：{{.remark}}
history.dispensing.dtp-dispensing-other = 【申请】{{.label}}：{{.subject}}，未编号研究产品名称、数量：{{.medicine}}，备注：{{.remark}}
history.dispensing.dtp-dispensing-with-other = 【申请】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称、数量：{{.other_medicine}}，备注：{{.remark}}
history.dispensing.dtp-dispensing-with-other-reason = 【计划外申请】 {{.label}}：{{.subject}}， 研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，原因：{{.reason}}。
history.dispensing.dtp-dispensingVisit = 【计划外申请】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，计划外发放原因：{{.reason}}
history.dispensing.dtp-dispensingVisit-other = 【计划外申请】{{.label}}：{{.subject}}，未编号研究产品名称/数量/批次/有效期：{{.medicine}}，计划外发放原因：{{.reason}}
history.dispensing.dtp-reissue = 【补发申请】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，补发原因：{{.remark}}
history.dispensing.dtp-reissue-other = 【补发申请】{{.label}}：{{.subject}}，未编号研究产品名称 数量：{{.medicine}}，补发原因：{{.remark}}
history.dispensing.dtp-reissue-with-other = 【补发申请】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，补发原因：{{.remark}}
history.dispensing.invalid = 【不参加访视】{{.label}}：{{.subject}}，备注：{{.remark}}。
history.dispensing.register = 【登记实际使用研究产品】{{.label}}：{{.subject}}， 系统发放研究产品：{{.medicine}}， 实际使用研究产品：{{.real_medicine}}。
history.dispensing.reissue = 【补发】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 补发原因：{{.remark}}。
history.dispensing.reissue-other = 【补发】{{.label}}：{{.subject}}，未编号研究产品名称 数量：{{.medicine}}， 补发原因：{{.remark}}。
history.dispensing.reissue-with-other = 【补发】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称(数量)：{{.other_medicine}}， 补发原因：{{.remark}}。
history.dispensing.replace-logistics = 【替换】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。
history.dispensing.dispensing-logistics = 【发放】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}，{{.form}} 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。
history.dispensing.dispensing-logistics-noRandomNumber = 【发放】{{.label}}：{{.subject}}，访视：{{.visit}}，{{.form}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。
history.dispensing.dispensingVisit-logistics = 【计划外发放】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，{{.form}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 原因：{{.reason}}。
history.dispensing.dispensingVisit-logistics-noRandomNumber = 【计划外发放】{{.label}}：{{.subject}}，访视：{{.visit}}，{{.form}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 原因：{{.reason}}。
history.dispensing.reissue-with-logistics = 【补发】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 补发原因：{{.remark}}。
history.dispensing.reissue-with-logistics-noRandomNumber = 【补发】{{.label}}：{{.subject}}，访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 补发原因：{{.remark}}。
history.dispensing.replace = 【研究产品替换】 {{.label}}：{{.subject}}，替换原因：{{.reason}} ，替换研究产品编号：{{.medicine}}，被替换研究产品{{.beReplaceMedicine}}。
history.dispensing.retrieval = 【取回研究产品】{{.label}}：{{.subject}}，取回研究产品编号：{{.medicine}}。
history.dispensing.retrieval-order = 【取回研究产品】{{.label}}：{{.subject}}，取回研究产品编号：{{.medicine}}。订单取消/关闭/终止
history.dispensing.send-type-0 = 中心（中心库存）
history.dispensing.send-type-1 = 中心（直接寄送受试者）
history.dispensing.send-type-2 = 库房（直接寄送受试者）
history.dispensing.scanConfrim = 【扫码确认】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，短码：{{.shortCode}}
history.dispensing.dispensing-new-formula = 【发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}}  研究产品编号：{{.medicine}}，备注：{{.remark}}
history.dispensing.dispensing-other-new-formula = 【发放】{{.label}}：{{.subject}}，{{.form}} {{.formula}}未编号研究产品名称/数量/批次/有效期：{{.medicine}}，备注：{{.remark}}
history.dispensing.dispensing-with-other-new-formula = 【发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}}研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，备注：{{.remark}}
history.dispensing.dispensing-with-other-reason-new-formula = 【计划外发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}}研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，计划外发放原因：{{.reason}}
history.dispensing.dispensingVisit-new-formula = 【计划外发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}}研究产品编号：{{.medicine}}，{{.formula}}，计划外发放原因：{{.reason}}
history.dispensing.dispensingVisit-other-new-formula = 【计划外发放】{{.label}}：{{.subject}}，{{.form}} {{.formula}}未编号研究产品名称/数量/批次/有效期：{{.medicine}}，{{.formula}}，计划外发放原因：{{.reason}}
history.dispensing.dispensing-logistics-formula = 【发放】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，{{.form}} {{.formula}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}，备注：{{.remark}}。
history.dispensing.dispensing-logistics-noRandomNumber-formula = 【发放】{{.label}}：{{.subject}}，访视：{{.visit}}，{{.form}} {{.formula}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}，备注：{{.remark}}。
history.dispensing.dispensingVisit-logistics-formula = 【计划外发放】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，{{.form}} {{.formula}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}，原因：{{.reason}}。
history.dispensing.dispensingVisit-logistics-noRandomNumber-formula = 【计划外发放】{{.label}}：{{.subject}}，访视：{{.visit}}，{{.form}} {{.formula}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}，原因：{{.reason}}。
history.dispensing.resume = 【恢复发放】{{.label}}：{{.subject}}
history.dispensing.dispensingCustomer-dispensing = 【发放】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-dispensingVisit = 【计划外发放】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-dispensingVisitCustomer = 【{{.dispensingType}}】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-reissue = 【补发】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-replace = 【研究产品替换】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-retrieval = 【取回研究产品】{{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-register = 【登记实际使用研究产品】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-not-attend = 【不参加】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-resume = 【恢复发放】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.single.comma = ，
history.dispensing.single.period = 。
history.dispensing.single.colon = ：
history.dispensing.single.dispensingVisit = 计划外发放
history.dispensing.single.dispensingApply = 申请
history.dispensing.single.randomNumber = 随机号：{{.randomNumber}}
history.dispensing.single.visit = 访视：{{.visit}}
history.dispensing.single.form = {{.form}}
history.dispensing.single.date = 出生日期：{{.date}}
history.dispensing.single.weight = 体重：{{.weight}}
history.dispensing.single.height = 身高：{{.height}}
history.dispensing.single.dose = {{.dose}}
history.dispensing.single.level = 发放水平：{{.level}}
history.dispensing.single.medicine = 研究产品编号：{{.medicine}}
history.dispensing.single.otherMedicine = 未编号研究产品名称/数量/批次/有效期：{{.medicine}}
history.dispensing.single.order = 订单编号：{{.order}}
history.dispensing.single.sendType = 发放方式
history.dispensing.single.vendor = 物流：{{.vendor}}/{{.number}}
history.dispensing.single.outSize = 是否超窗：是
history.dispensing.single.remark = 备注：{{.remark}}
history.dispensing.single.reason = 原因：{{.reasonDispensingVisit}}
history.dispensing.single.reasonDispensingVisit = 计划外发放原因：{{.reasonDispensingVisit}}
history.dispensing.single.reasonDispensingVisitCustomer = {{.dispensingType}}原因：{{.reasonDispensingVisit}}
history.dispensing.single.reasonReissue = 补发原因：{{.reasonDispensingVisit}}
history.dispensing.single.reasonReplace = 替换原因：{{.reasonDispensingVisit}}
history.dispensing.single.replaceNumber = 替换研究产品编号：{{.replaceNumber}}
history.dispensing.single.beReplaceMedicine = 被替换研究产品：{{.beReplaceMedicine}}
history.dispensing.single.retrievalMedicine = 研究产品：{{.retrievalMedicine}}
history.dispensing.single.systemMedicine = 系统发放研究产品：{{.systemMedicine}}
history.dispensing.single.realMedicine = 实际使用研究产品：{{.realMedicine}}
history.dispensing.single.noKey = {{ .data }}
history.dispensing.single.notAttendRemark = "备注：开启后续阶段访视，当前阶段：{{.random}}，后续阶段:{{.atRandom}}"
history.dispensing.single.resumeRemark = "备注：关闭后续阶段访视，当前阶段：{{.currentStage}}，后续阶段：{{.nextStage}}"
history.medicine.update-batch-expireDate = 【编辑】 关联订单：{{.orderNumber}}，状态：{{.status}}，研究产品编号：{{.ipNumber}}，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}。
history.medicine.other-update-batch-expireDate = 【编辑】 关联订单：{{.orderNumber}}，状态：{{.status}}，研究产品：{{.ip}}，数量：{{.count}}，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}。
history.medicine.updateBatch = 研究产品【更新】，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}，状态：{{.status}}。
history.medicine.updateOtherBatch = 研究产品【更新】，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}，状态：{{.status}}，数量：{{.count}}。
history.medicine.expired = 【已过期研究产品】 已过期原因：系统自动已过期，当前已过期研究产品编号：{{.packNumber}}。
history.medicine.freeze = 【隔离研究产品】 隔离原因：{{.reason}}，当前隔离研究产品编号：{{.packNumber}}。
history.medicine.freeze-new = 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}，研究产品编号：{{.packNumber}}，原因：{{.reason}}。
history.medicine.freeze-package-new = 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}，研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}，原因：{{.reason}}。
history.medicine.otherFreeze = 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}， 原因：{{.freezeReason}}， 当前隔离研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 隔离：{{.freezeCount}}。
history.medicine.otherMedicineLost = 研究产品【已丢失/作废】， 原因：{{.freezeReason}}， 当前隔离研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 丢失/作废：{{.freezeCount}}。
history.medicine.otherMedicineLost-new = 研究产品【丢失/作废】，研究产品：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.freezeCount}}，原因：{{.freezeReason}}。
history.medicine.lost = 【丢失/作废研究产品】，丢失/作废原因：{{.reason}}，当前丢失/作废研究产品编号：{{.packNumber}}。
history.medicine.lost-new = 研究产品【丢失/作废】，研究产品编号：{{.packNumber}}，原因：{{.reason}}。
history.medicine.lost-package = 研究产品【丢失/作废】，研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}，原因：{{.reason}}。
history.medicine.release = 研究产品【解隔离】， 当前解隔离研究产品编号：{{.packNumber}}，原因：{{.reason}}。
history.medicine.release-package = 研究产品【解隔离】， 当前解隔离研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}，原因：{{.reason}}。
history.medicine.quarantine-no = 研究产品【解隔离审批】，研究产品编号：{{.packNumber}}，解隔离确认：拒绝，拒绝原因：{{.reason}}。
history.medicine.approval = 研究产品【解隔离申请】，原因：{{.reason}}，研究产品编号：{{.packNumber}}。
history.medicine.locked = 研究产品【锁定】
history.medicine.otherLocked = 研究产品【锁定】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.otherUse = 研究产品【已使用】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.otherCanUse = 研究产品【可用】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.otherLost = 研究产品【作废】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.otherToBeConfirm = 研究产品【待确认】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.otherExpired = 研究产品【已过期】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.toBeConfirm = 研究产品【待确认】
history.medicine.toBeConfirmNew = 关联订单：{{.order}}，状态：待确认。
history.medicine.toFrozenNew = 关联订单：{{.orderNumber}}，状态：已冻结。
history.medicine.toBeConfirmNewUpdate = 关联订单：{{.orderNumber}}，状态：待确认。
history.medicine.confirmed = 研究产品【已确认】
history.medicine.confirmedNew = 关联订单：{{.orderNumber}}，状态：已确认。
history.medicine.otherConfirmed = 研究产品【已确认】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.releaseLost = 【解隔离丢失/作废研究产品】 丢失/作废原因：{{.reason}}，当前丢失/作废研究产品编号：{{.packNumber}}。
history.medicine.releaseLost-package = 【解隔离丢失/作废研究产品】 丢失/作废原因：{{.reason}}，当前丢失/作废研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}。
history.medicine.otherRelease = 【解隔离研究产品】 解隔离原因：{{.reason}}，当前解隔离研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 解隔离：{{.count}}。
history.medicine.otherReleaseLost = 【丢失/作废研究产品】 ，丢失/作废原因：{{.reason}}，当前丢失/作废研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 丢失/作废：{{.count}}。
history.medicine.other-quarantine-no = 研究产品【解隔离审批】，解隔离确认：拒绝， 拒绝原因：{{.reason}}，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.other-approval = 研究产品【解隔离申请】 原因：{{.reason}}，当前申请解隔离研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.replace = 【发放替换研究产品】 设为作废原因：{{.reason}}，当前作废研究产品编号：{{.packNumber}}。
history.medicine.replace-with-dtp = 【替换研究产品】，研究产品【丢失/作废】，原因：研究产品被替换。
history.medicine.cancel = 研究产品【已撤销】，关联受试者：{{.subject}}，访视：{{.visit}}，操作：研究产品撤销。
history.medicine.use = 【设为可用研究产品】 设为可用原因：{{.reason}}，当前可用研究产品编号：{{.packNumber}}。
history.medicine.use-package = 【设为可用研究产品】 设为可用原因：{{.reason}}，当前可用研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}。
history.medicine.replace-dtp = 研究产品已替换，替换前：{{.beReplace}}，替换后：{{.replace}}。
history.medicine.register = 研究产品【已使用】，原因：登记实际使用研究产品，系统发放研究产品：{{.medicine}}，实际使用研究产品：{{.real_medicine}}。
history.medicine.apply = 研究产品已申请
history.medicine.shipped = 关联订单：{{.orderNumber}}，状态：已运送。
history.medicine.otherShipped = 研究产品【已运送】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.receive = 研究产品【已接收】
history.medicine.receiveNew = 关联订单：{{.orderNumber}}，状态：已接收。
history.medicine.otherReceive = 研究产品【已接收】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
history.medicine.used = 研究产品已使用
history.medicine.canUse = 研究产品【可用】
history.medicine.uploadCanUse = 【研究产品上传】状态：待入仓
history.medicine.scanCanUse = 【扫码入仓】状态：待审核
history.medicine.examinePassThrough = 【审核通过】状态：待入仓
history.medicine.examineRefuse = 【审核拒绝】状态：审核失败
history.medicine.updateName = 【修改】状态：待审核
history.medicine.updateNameScanCan = 【修改】状态：待扫码
history.medicine.release-usable = 【放行】状态：可用
history.medicine.register-use = 研究产品【可用】，原因：研究产品登记为“可用”。
history.medicine.register-frozen = 研究产品【冻结】，原因：研究产品登记为“冻结”。
history.medicine.register-lost = 研究产品【丢失/作废】，原因：研究产品登记为“丢失/作废”。
history.medicine.in-order = 研究产品待确认，受试者号：{{.subject}}，研究产品编号：{{.medicine}}。
history.medicine.in-order-new = 研究产品待确认
history.medicine.confrim = 研究产品已确认
history.medicine.dispensing-used = 研究产品已使用，受试者号：{{.subject}}，研究产品编号：{{.medicine}}。
history.medicine.use-dtp = 关联订单：{{.orderNumber}}，状态：可用。
history.medicine.use-order-cancel = 研究产品 状态恢复，原因：订单取消。
history.medicine.use-order-close = 研究产品 状态恢复，原因：订单关闭。
history.medicine.use-order-termination = 研究产品 状态恢复，原因：订单终止。
history.medicine.use-available-order-cancel = 研究产品【可用】，原因：订单取消，状态恢复。
history.medicine.use-available-order-close = 研究产品【可用】，原因：订单关闭，状态恢复。
history.medicine.use-available-order-termination = 研究产品【可用】，原因：订单终止，状态恢复。
history.medicine.use-frozen-order-cancel = 研究产品【冻结】，原因：订单取消，状态恢复。
history.medicine.use-frozen-order-close = 研究产品【冻结】，原因：订单关闭，状态恢复。
history.medicine.use-frozen-order-termination = 研究产品【冻结】，原因：订单终止，状态恢复。
history.medicine.use-available-order-confrim = 关联订单:{{.orderNumber}}，状态：可用，原因：订单确认时未确认该研究产品，状态恢复。
history.medicine.use-available-order-frozen = 关联订单:{{.orderNumber}}，状态：冻结，原因：订单确认时未确认该研究产品，状态恢复。
history.medicine.use-lose-order-cancel = 研究产品【丢失/作废】，原因：订单取消，状态恢复。
history.medicine.use-lose-order-close = 研究产品【丢失/作废】，原因：订单关闭，状态恢复。
history.medicine.use-lose-order-termination = 研究产品【丢失/作废】，原因：订单终止，状态恢复。
history.medicine.use-expired-order-cancel = 研究产品【已过期】，原因：订单取消，状态恢复。
history.medicine.use-expired-order-close = 研究产品【已过期】，原因：订单关闭，状态恢复。
history.medicine.use-expired-order-termination = 研究产品【已过期】，原因：订单终止，状态恢复。
history.medicine.sku-freeze = 研究产品【已冻结】
history.medicine.sku-freeze-reason = 【设为冻结研究产品】 设为冻结原因：{{.reason}}，当前冻结研究产品编号：{{.packNumber}}。
history.medicine.sku-freeze-subject = 研究产品【已冻结】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
history.medicine.sku-used-subject = 研究产品【已使用】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
history.medicine.sku-use-subject = 研究产品【可用】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
history.medicine.sku-in-order-subject = 研究产品【待确认】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
history.medicine.sku-lost-subject = 研究产品【丢失/作废】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
history.medicine.rest-receive = 研究产品【已领药】，关联受试者：{{.subject}}，访视：{{.visit}}，操作：{{.operation}}。
history.medicine.rest-return = 研究产品【已退货】，关联受试者：{{.subject}}，访视：{{.visit}}，操作：{{.operation}}。
history.medicine.rest-destroy = 研究产品【已销毁】，关联受试者：{{.subject}}，访视：{{.visit}}，操作：{{.operation}}。
history.medicine.rest-return-retrieve = 研究产品【已退货】，操作：{{.operation}}。
history.medicine.rest-destroy-retrieve = 研究产品【已销毁】，操作：{{.operation}}。
history.medicine.operation.dispensing = 发放
history.medicine.operation.retrieval = 取回
history.medicine.operation.replace = 研究产品替换
history.medicine.operation.unscheduled = 计划外发放
history.medicine.operation.reissue = 补发
history.medicine.operation.register = 登记实际使用研究产品
history.medicine.sku-lose = 关联订单：{{.orderNumber}}，状态：已丢失。
history.medicine.cancel-dtp = 研究产品已终止，终止原因：{{.reason}}。
history.medicine.drugFreeze.drugFreeze = 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.drugFreeze-package = 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.otherDrugFreeze = 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.allDrugFreeze = 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}。
history.medicine.drugFreeze.allDrugFreeze-package = 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}。
history.medicine.drugFreeze.lost = 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.lost-package = 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.approval = 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，研究产品编号：{{.medicines}}。
history.medicine.drugFreeze.approval-package = 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，研究产品编号/包装号：{{.medicines}}。
history.medicine.drugFreeze.release = 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.release-package = 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.otherLost = 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，当前丢失/作废研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.other-approval = 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}。
history.medicine.drugFreeze.otherRelease = 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.freezeReason}}。
history.medicine.drugFreeze.other-quarantine-no = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
history.medicine.drugFreeze.other-quarantine-yes = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.untieReason}}，解隔离确认：通过。
history.medicine.drugFreeze.allLost = 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前丢失/作废研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}。
history.medicine.drugFreeze.all-approval = 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}。
history.medicine.drugFreeze.allRelease = 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}。
history.medicine.drugFreeze.all-quarantine-no = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}， 原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
history.medicine.drugFreeze.all-quarantine-yes = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}， 原因：{{.untieReason}}，解隔离确认：通过。
history.medicine.drugFreeze.quarantine-no = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
history.medicine.drugFreeze.quarantine-yes = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.untieReason}}，解隔离确认：通过。
history.medicine.drugFreeze.allLost-package = 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前丢失/作废研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}。
history.medicine.drugFreeze.all-approval-package = 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}。
history.medicine.drugFreeze.allRelease-package = 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}。
history.medicine.drugFreeze.all-quarantine-no-package = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}， 原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
history.medicine.drugFreeze.all-quarantine-yes-package = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}， 原因：{{.untieReason}}，解隔离确认：通过。
history.medicine.drugFreeze.quarantine-no-package = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
history.medicine.drugFreeze.quarantine-yes-package = 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.untieReason}}，解隔离确认：通过。
history.medicine.change.newToConfirm = 关联订单:{{.orderNumber}}，状态：待确认，原研究产品：{{.oldMedicine}}，更换后研究产品：{{.newMedicine}}，更换原因：{{.reason}}。
history.medicine.change.newConfirmed = 关联订单:{{.orderNumber}}，状态：已确认，原研究产品：{{.oldMedicine}}，更换后研究产品：{{.newMedicine}}，更换原因：{{.reason}}。
history.medicine.change.old = 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}，研究产品编号：{{.oldMedicine}}，关联订单：{{.orderNumber}}，更换原因：{{.reason}}。
history.medicine.change.otherNewToConfirm = 关联订单:{{.orderNumber}}，状态：待确认，原研究产品名称/批次号/有效期/更换：{{.oldMedicine}}，更换后研究产品：{{.newMedicine}}，更换原因：{{.reason}}。
history.medicine.change.otherNewConfirmed = 关联订单:{{.orderNumber}}，状态：已确认，原研究产品名称/批次号/有效期/更换：{{.oldMedicine}}，更换后研究产品：{{.newMedicine}}，更换原因：{{.reason}}。
history.medicine.change.otherOld = 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}，研究产品名称/批次号/有效期/更换：{{.oldMedicine}}，关联订单：{{.orderNumber}}，更换原因：{{.reason}}。
history.order.cancel = 取消订单，订单已取消，取消原因：{{.reason}}。
history.order.close = 关闭订单，订单已关闭，关闭原因：{{.reason}}。
history.order.confirm-task = 发送订单确认任务，订单已请求。
history.order.confirm-task-finish = 完成了订单确认任务，订单运送中。
history.order.confrim = 确认订单，订单已请求。
history.order.create = 创建订单，订单待确认。
history.order.close-with-dtp = 订单编号：{{.orderNumber}}，订单【已关闭】，关闭原因：全部研究产品被替换。
history.order.close-with-register = 订单编号：{{.orderNumber}}，订单【已关闭】，关闭原因：研究产品已登记为“可用/冻结”。
history.order.close-with-register-lost = 订单编号：{{.orderNumber}}，订单【已关闭】，关闭原因：研究产品已登记为“丢失/作废”。
history.order.lost = 订单丢失，订单已丢失，丢失原因：{{.reason}}。
history.order.receive = 接收订单，订单已收到。
history.order.receive-task-confrim = 发送研究产品接收任务，订单已请求。
history.order.receive-task-finish = 完成了研究产品接收任务，订单已收到。
history.order.receive-task-send = 发送研究产品接收任务，订单运送中。
history.order.send = 运送订单，订单运送中。
history.order.apply = 创建订单，订单已申请， 备注：{{.remark}}。
history.order.cancel-new = 订单编号：{{.orderNumber}}，订单【已取消】，取消原因：{{.reason}}。
history.order.close-new = 订单编号：{{.orderNumber}}，订单【已关闭】，关闭原因：{{.reason}}。
history.order.confrim-new = 订单编号：{{.orderNumber}}，订单【已确认】。
history.order.confrim-expectedArrivalTime-new = 订单编号：{{.orderNumber}}，订单【已确认】，期望送达时间:{{.expectedArrivalTime}}。
history.order.create-new = 订单编号：{{.orderNumber}}，订单【待确认】。
history.order.create-expectedArrivalTime-new = 订单编号：{{.orderNumber}}，订单【待确认】，期望送达时间:{{.expectedArrivalTime}}。
history.order.lost-new = 订单编号：{{.orderNumber}}，订单【已丢失】，丢失原因：{{.reason}}。
history.order.receive-new = 订单编号：{{.orderNumber}}，订单【已接收】。
history.order.receive-actualTime-new = 订单编号：{{.orderNumber}}，订单【已接收】，实际接收时间:{{.actualReceiptTime}}。
history.order.send-new = 订单编号：{{.orderNumber}}，订单【已运送】。
history.order.send-expectedArrivalTime-new = 订单编号：{{.orderNumber}}，订单【已运送】，期望送达时间:{{.expectedArrivalTime}}。
history.order.logistics—other = 【物流信息更新】订单编号：{{.orderNumber}}，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}。
history.order.logistics = 【物流信息更新】订单编号：{{.orderNumber}}，物流供应商：{{.logistics}}，物流单号：{{.number}}。
history.order.logistics—other-actualTime = 【物流信息更新】订单编号：{{.orderNumber}}，实际接收时间:{{.actualReceiptTime}}。
history.order.logistics-actualTime = 【物流信息更新】订单编号：{{.orderNumber}}，实际接收时间:{{.actualReceiptTime}}。
history.order.logistics—other-actualTime-all = 【物流信息更新】订单编号：{{.orderNumber}}，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}，实际接收时间:{{.actualReceiptTime}}。
history.order.logistics-actualTime-all = 【物流信息更新】订单编号：{{.orderNumber}}，物流供应商：{{.logistics}}，物流单号：{{.number}}，实际接收时间:{{.actualReceiptTime}}。
history.order.send-with-logistics = 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，物流单号：{{.number}}。
history.order.send-with-logistics-expectedTime = 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，物流单号：{{.number}}，期望送达时间:{{.expectedArrivalTime}}。
history.order.send-with-other-logistics = 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}。
history.order.send-with-other-logistics-expectedTime = 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}，期望送达时间:{{.expectedArrivalTime}}。
history.order.cancel-dtp = 订单编号：{{.orderNumber}}，订单【已终止】，终止原因：{{.reason}}。
history.order.approval = 研究中心订单申请【审批已通过】，订单【待确认】，订单编号：{{.orderNumber}}。
history.order.change = 【更换】，订单编号：{{.orderNumber}}，原研究产品详情：{{.oldMedicines}}，更换后研究产品详情：{{.newMedicines}}，更换原因：{{.reason}}。
history.order.batch = 【编辑】，订单编号：{{.orderNumber}}，研究产品-有效期-批次号：{{.ipExpireDateBatch}}。
history.order.expireDate = 【编辑】， {{.data}}
history.order.expireDateBatch.orderNumber = 订单编号：{{.orderNumber}}
history.order.expireDateBatch.ipExpireDateBatch = 研究产品-有效期-批次号：{{.ipExpireDateBatch}}
history.project.cohort.add = 【添加 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，提醒阈值：{{.reminderThresholds}}%
history.project.cohort.delete = 【删除 Cohort/再随机】 名称：{{.cohortName}}
history.project.cohort.edcAdd = 【添加 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，因素：{{.factor}}，提醒阈值：{{.reminderThresholds}}%
history.project.cohort.edcEdit = 【修改 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，因素：{{.factor}}，提醒阈值：{{.reminderThresholds}}%
history.project.cohort.edcRAdd = 【添加 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，因素：{{.factor}}，上一阶段：{{.lastCohort}}，提醒阈值：{{.reminderThresholds}}%
history.project.cohort.edcREdit = 【修改 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，因素：{{.factor}}，上一阶段：{{.lastCohort}}，提醒阈值：{{.reminderThresholds}}%
history.project.cohort.edit = 【修改 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，提醒阈值：{{.reminderThresholds}}%
history.project.cohort.rAdd = 【添加 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，上一阶段：{{.lastCohort}}，提醒阈值：{{.reminderThresholds}}%
history.project.cohort.rEdit = 【修改 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，上一阶段：{{.lastCohort}}，提醒阈值：{{.reminderThresholds}}%
history.project.drugConfigure.add = 【新增研究产品配置】 组别：{{.group}}，研究产品配置：{{.drugConfigures}}
history.project.drugConfigure.delete = 【删除研究产品配置】 组别：{{.group}}，研究产品配置：{{.drugConfigures}}
history.project.drugConfigure.edit = 【编辑研究产品配置】 组别：{{.group}}，研究产品配置：{{.drugConfigures}}
history.project.env.add = 【添加环境】 新增{{.env}}环境
history.project.info.add = 【添加项目】 项目编号：{{.projectNum}}，项目名称：{{.projectName}}，申办单位：{{.biddingUnit}}，项目描述：{{.projectDesc}}
history.project.info.edit = 【修改项目】 项目编号：{{.projectNum}}，项目名称：{{.projectName}}，申办单位：{{.biddingUnit}}，项目描述：{{.projectDesc}}
history.project.medicine.batch = 【批次管理】 本次更新库存批次号：{{.batch}}，有效期：{{.expireDate}}，状态：{{.status}}，更新批次号：{{.batchUpdate}}，更新有效期：{{.expireDateUpdate}}
history.project.medicine.upload = 【上传研究产品】 研究产品名称：{{.drugName}}，上传研究产品数量：{{.count}}
history.randomization.attribute = 属性配置： 是否随机：{{.random}}， 是否展示随机号：{{.isRandomNumber}}， 是否发放：{{.dispensing}}， 是否盲法：{{.blind}}， 是否使用前缀：{{.prefix}}， 受试者号前缀：{{.prefixExpression}}， 标签(即替换文本)：{{.subjectReplaceText}}， 位数精确值(1:小于等于/2:等于)：{{.accuracy}}， 位数限制：{{.digit}}， 运行运送算法时将隔离的单品计算为研究机构重可用存货的一部分：{{.isFreeze}}， EDC对接研究产品配置标签：{{.edcDrugConfigLabel}}， 是否号段随机：{{.segment}}{{.segmentLength}}
history.randomization.block.distributionFactor = 【随机分段】 【{{.name}}】 区组：{{.block}} 分配分层：{{.valueSite}}
history.randomization.block.distributionSite = 【随机分段】 【{{.name}}】 区组：{{.block}} 分配中心：{{.valueSite}}
history.randomization.block.generate = 【区组随机】【随机号生成】名称：{{.name}}，初始编号：{{.initialValue}}，组别配置 组别(权重比)：{{.groupRatio}}，区组配置 区组长度(区组数)：{{.blockNumber}}，分层因素：{{.factors}}，号码长度：{{.numberLength}}，随机种子：{{.seed}}，号码前缀：{{.numberPrefix}}，生成数量：{{.numberText}}
history.randomization.block.upload = 【区组随机】【随机号上传】名称：{{.name}}，分层因素：{{.factors}}，可接受区组大小：{{.blockSize}}，上传数量：{{.numberText}}
history.randomization.config.block.distributionFactor = 【随机分段】 【{{.name}}】 区组：{{.block}} 分配分层：{{.valueSite}}
history.randomization.config.block.distributionSite = 【随机分段】 【{{.name}}】 区组：{{.block}}  分配中心：{{.valueSite}}
history.randomization.config.block.generate = 【区组随机】【随机号生成】名称：{{.name}}，初始编号：{{.initialValue}}，组别配置 组别(权重比)：{{.groupRatio}}，区组配置 区组长度(区组数)：{{.blockNumber}}，分层因素：{{.factors}}，号码长度：{{.numberLength}}，随机种子：{{.seed}}，号码前缀：{{.numberPrefix}}，生成数量：{{.numberText}}
history.randomization.config.block.upload = 【区组随机】【随机号上传】名称：{{.name}}，分层因素：{{.factors}}，可接受区组大小：{{.blockSize}}，上传数量：{{.numberText}}
history.randomization.config.factor.add = 【分层新增】 字段编号：{{.number}}， 名称：{{.label}} ， 控件类型：{{ .type}}， 选项： {{.options}}
history.randomization.config.factor.addEDC = 【分层新增】 字段编号：{{.number}}， 名称：{{.label}} ，变量名称：{{ .name}}， 控件类型：{{ .type}}， 选项： {{.options}}
history.randomization.config.factor.clean = 【清空其他分层】 【{{ .name}}】 区组[{{ .block}}] 清空其他分层
history.randomization.config.factor.countryEnable = 【分层调整】 启用国家分层
history.randomization.config.factor.delete = 【分层删除】 字段编号：{{.oldNumber}}， 名称：{{.oldLabel}}， 控件类型：{{ .oldType}}， 选项 {{.oldOptions}}
history.randomization.config.factor.deleteEDC = 【分层删除】 字段编号：{{.oldNumber}}， 名称：{{.oldLabel}} ，变量名称：{{ .oldName}}， 控件类型：{{ .oldType}}， 选项 {{.oldOptions}}
history.randomization.config.factor.disableCountryLayer = 【分层调整】 禁用国家分层
history.randomization.config.factor.disableLayer = 【分层调整】 禁用地区分层
history.randomization.config.factor.disableSiteLayer = 【分层调整】 禁用中心分层
history.randomization.config.factor.edit = 【分层修改】 （字段编号：{{.oldNumber}}， 名称：{{.oldLabel}} ， 控件类型：{{ .oldType}}， 选项 {{.oldOptions}} ）     修改为     字段编号：{{.number}}， 名称：{{.label}} ， 控件类型：{{ .type}}， 选项 {{.options}}
history.randomization.config.factor.editEDC = 【分层修改】 （字段编号：{{.oldNumber}}， 名称：{{.oldLabel}} ，变量名称：{{ .oldName}}， 控件类型：{{ .oldType}}， 选项 {{.oldOptions}} ）     修改为     字段编号：{{.number}}， 名称：{{.label}} ，变量名称：{{ .name}}， 控件类型：{{ .type}}， 选项 {{.options}}
history.randomization.config.factor.number = 【设置分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
history.randomization.config.factor.siteEnable = 【分层调整】 启用中心分层
history.randomization.config.form.add = 【表单新增】 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}
history.randomization.config.form.addEDC = 【表单新增】 字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}
history.randomization.config.form.addOption = 【表单新增】 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}  选项：{{.options}}
history.randomization.config.form.addOptionEDC = 【表单新增】 字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}  选项：{{.options}}
history.randomization.config.form.delete = 【表单删除】 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}
history.randomization.config.form.deleteEDC = 【表单删除】 字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}
history.randomization.config.form.deleteOption = 【表单删除】变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}}
history.randomization.config.form.deleteOptionEDC = 【表单删除】字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}}
history.randomization.config.form.edit = 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} ) 修改为 (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} )
history.randomization.config.form.editEDC = 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} ) 修改为  (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} )
history.randomization.config.form.editOption = 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} )    修改为 (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}})
history.randomization.config.form.editOptionEDC = 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}})    修改为 (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}})
history.randomization.config.form.editOptionend = 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} )    修改为 (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
history.randomization.config.form.editOptionendEDC = 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} )    修改为 (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.oldOptions}})
history.randomization.config.form.editOptionstart = 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}})    修改为 (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}})
history.randomization.config.form.editOptionstartEDC = 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}})    修改为 (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}})
history.randomization.config.form.editOptionstartend = 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}})    修改为 ( 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
history.randomization.config.form.editOptionstartendEDC = 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}})    修改为 (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
history.randomization.config.form.editend = 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} ) 修改为  (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
history.randomization.config.form.editendEDC = 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} )  (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
history.randomization.config.form.editstart = 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}}) 修改为  (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} )
history.randomization.config.form.editstartEDC = 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}}) 修改为  (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} )
history.randomization.config.form.editstartend = 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}}) 修改为  ( 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
history.randomization.config.form.editstartendEDC = 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}}) 修改为  (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
history.randomization.config.group.add = 【治疗组别新增】 组别代码：{{.number}}， 组别名称：{{.name}}
history.randomization.config.group.delete = 【治疗组别删除】  组别代码：{{.number}}， 组别名称：{{.name}}
history.randomization.config.group.edit = 【治疗组别修改】  组别代码：{{.oldNumber}}， 组别名称：{{.oldName}}      修改为    组别代码：{{.number}}， 组别名称：{{.name}}
history.randomization.config.list.disableStatus = 【状态调整】【{{.name}}】 禁用随机状态
history.randomization.config.list.enableStatus = 【状态调整】 【{{.name}}】 启用随机状态
history.randomization.config.list.invalid = 【作废】 已将随机表【{{.name}}】作废
history.randomization.config.minimize = 【最小化随机】 名称：{{.name}}，初始编号：{{.initialValue}}，组别配置 组别(权重比)：{{.groupRatio}}， 分层因素 分层(权重比)：{{.factors}}，偏倚概率：{{.probability}}，总例数：{{.total}}，号码长度：{{.numberLength}}，随机种子：{{.seed}}，号码前缀：{{.numberPrefix}}
history.randomization.config.typeBlock = 【随机类型调整】 启用区组随机
history.randomization.config.typeMin = 【随机类型调整】 启用最小化随机
history.randomization.factor.clean = 【清空其他分层】 【{{ .name}}】 区组[{{ .block}}] 清空其他分层
history.randomization.factor.number = 【设置分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
history.randomization.factor.addNumber = 【添加分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
history.randomization.factor.editNumber = 【编辑分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
history.randomization.factor.delNumber = 【删除分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
history.randomization.list.disableStatus = 【状态调整】 禁用随机状态
history.randomization.list.enableStatus = 【状态调整】 启用随机状态
history.randomization.list.invalid = 【作废】 已将随机表作废
history.randomization.list.site = 【编辑】 绑定中心:{{.siteName}}
history.randomization.minimize = 【最小化随机】 名称：{{.name}}，初始编号：{{.initialValue}}，组别配置 组别(权重比)：{{.groupRatio}}， 分层因素 分层(权重比)：{{.factors}}，偏倚概率：{{.probability}}，总例数：{{.total}}，号码长度：{{.numberLength}}，随机种子：{{.seed}}，号码前缀：{{.numberPrefix}}
history.subject.add = 【登记】 {{.content}}
history.subject.at-random-add = 【登记】 阶段：{{.stage}}，{{.content}}
history.subject.delete = 【删除】 {{.label}}：{{.name}}
history.subject.beReplaced = 【被替换】 当前受试者已经被受试者：{{.name}} 替换
history.subject.replaced-new = 【受试者替换】 原受试者：{{.name}}，原随机号：{{.beReplaceRandomNumber}}，替换受试者：{{.replaceName}}，替换受试者随机号：{{.replaceRandomNumber}}
history.subject.pvUnblinding = 【PV揭盲】 受试者：{{.name}} 已PV揭盲，揭盲原因：{{.reason}}
history.subject.remark-pvUnblinding = 【PV揭盲】 受试者：{{.name}} 已PV揭盲，揭盲原因:{{.reason}}，备注：{{.remark}}
history.subject.random = 【随机】 受试者：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}
history.subject.randomSub = 【随机】 受试者：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}，子组别：{{.subGroup}}
history.subject.randomNoNumber = 【随机】 受试者：{{.name}} 已随机，组别：{{.group}}
history.subject.randomNoNumberSub = 【随机】 受试者：{{.name}} 已随机，组别：{{.group}}，子组别：{{.subGroup}}
history.subject.replaced = 【替换】 当前受试者来源于受试者替换，被替换的受试者:{{.name}} 随机号：{{.randomNumber}}
history.subject.signOut = 【停用】 受试者：{{.name}} 已停用，停用原因：{{.reason}}
history.subject.unblinding = 【紧急揭盲】 受试者：{{.name}} 已紧急揭盲，揭盲原因：{{.reasonStr}}，备注：{{.reason}}，是否已经通知申办方：{{.isSponsor}}，备注：{{.remark}}
history.subject.unblinding-success = 揭盲（紧急）成功
history.subject.at-random-unblinding-success = 阶段：{{.stage}}，揭盲（紧急）成功
history.subject.unblinding-application = 【揭盲(紧急)申请】审批编号：{{.approvalNumber}}，状态:待审批
history.subject.at-random-unblinding-application = 【揭盲(紧急)申请】 阶段：{{.stage}}，审批编号：{{.approvalNumber}}，状态:待审批
history.subject.unblinding-application-pv = 【揭盲(pv)申请】审批编号：{{.approvalNumber}}，状态:待审批
history.subject.at-random-unblinding-application-pv = 【揭盲(pv)申请】 阶段：{{.stage}}，审批编号：{{.approvalNumber}}，状态:待审批
history.subject.unblinding-approval-agree = 【揭盲(紧急)审批】审批编号：{{.approvalNumber}}，状态:已通过
history.subject.unblinding-approval-agree-pv = 【揭盲(pv)审批】审批编号：{{.approvalNumber}}，状态:已通过
history.subject.unblinding-approval-reject = 【揭盲(紧急)审批】审批编号：{{.approvalNumber}}，状态:已拒绝，原因：{{.reason}}
history.subject.unblinding-approval-reject-pv = 【揭盲(pv)审批】审批编号：{{.approvalNumber}}，状态:已拒绝，原因：{{.reason}}
history.subject.update = 【修改】 {{.content}}
history.subject.at-random-update = 【修改】 阶段：{{.stage}}，{{.content}}
history.subject.at-random-random = 【随机】 阶段：{{.stage}}，{{.content}}
history.subject.updateSubjectNo = 【修改】受试者：{{.oldSubjectNo}}，改为：{{.shortname}}
history.subject.transfer = 【转运】 {{.label}}：{{.subjectNo}}，原中心：{{.oldSite}}，新中心：{{.newSite}}。
history.subject.at-random-transfer = 【转运】 阶段：{{.stage}}，{{.label}}：{{.subjectNo}}，原中心：{{.oldSite}}，新中心：{{.newSite}}。
history.subject.switch-cohort = 【切换群组】 {{.label}}：{{.subjectNo}}，原群组：{{.oldCohort}}，新群组：{{.newCohort}}。
history.subject.joinTime = 【编辑】 {{.label}}：{{.subjectNo}}，入组时间：{{.joinTime}}。
history.subject.at-random-joinTime = 【编辑】 阶段：{{.stage}}，{{.label}}：{{.subjectNo}}，入组时间：{{.joinTime}}。
history.subject.start-follow-up-visits = 【开启后续阶段访视】 阶段：{{.currentStage}}，{{.label}}：{{.subjectNo}}，当前阶段：{{.currentStage}}，后续阶段：{{.nextStage}}。
history.subject.close-follow-up-visits = 【关闭后续阶段访视】 阶段：{{.currentStage}}，{{.label}}：{{.subjectNo}}，当前阶段：{{.currentStage}}，后续阶段：{{.nextStage}}。
history.subject.label.common-key-value = {{.name}}：{{.value}}
history.subject.label.common-key-value1 = {{.name}}：{{.value}}
history.subject.label.replaced-new = 【受试者替换】 原{{.label}}：{{.name}}，原随机号：{{.beReplaceRandomNumber}}，替换{{.label}}：{{.replaceName}}，替换受试者随机号：{{.replaceRandomNumber}}
history.subject.label.at-random-replaced-new-a = 【受试者替换】 原{{.label}}：{{.name}}，替换{{.label}}：{{.replaceName}}，阶段：{{.stage}}，原随机号：{{.beReplaceRandomNumber}}，替换受试者随机号：{{.replaceRandomNumber}}
history.subject.label.at-random-replaced-new-b = 【受试者替换】 原{{.label}}：{{.name}}，替换{{.label}}：{{.replaceName}}，阶段：{{.stage}}，原随机号：{{.beReplaceRandomNumber}}，替换受试者随机号：{{.replaceRandomNumber}}；阶段：{{.stage2}}，原随机号：{{.beReplaceRandomNumber2}}，替换受试者随机号：{{.replaceRandomNumber2}}
history.subject.label.pvUnblinding = 【PV揭盲】 {{.label}}：{{.name}} 已PV揭盲，揭盲原因：{{.reason}}
history.subject.label.at-random-pvUnblinding = 【PV揭盲】 阶段：{{.stage}}，{{.label}}：{{.name}} 已PV揭盲，揭盲原因：{{.reason}}
history.subject.label.remark-pvUnblinding = 【PV揭盲】 {{.label}}：{{.name}} 已PV揭盲，揭盲原因:{{.reason}}，备注：{{.remark}}
history.subject.label.at-random-remark-pvUnblinding = 【PV揭盲】 阶段：{{.stage}}，{{.label}}：{{.name}} 已PV揭盲，揭盲原因:{{.reason}}，备注：{{.remark}}
history.subject.label.random = 【随机】 {{.label}}：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}
history.subject.label.at-random-random = 【随机】 阶段：{{.stage}}，{{.label}}：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}
history.subject.label.randomSub = 【随机】 {{.label}}：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}，子组别：{{.subGroup}}
history.subject.label.at-random-randomSub = 【随机】 阶段：{{.stage}}，{{.label}}：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}，子组别：{{.subGroup}}
history.subject.label.randomNoNumber = 【随机】 {{.label}}：{{.name}} 已随机，组别：{{.group}}
history.subject.label.at-random-randomNoNumber = 【随机】 阶段：{{.stage}}，{{.label}}：{{.name}} 已随机，组别：{{.group}}
history.subject.label.randomNoNumberSub = 【随机】 {{.label}}：{{.name}} 已随机，组别：{{.group}}，子组别：{{.subGroup}}
history.subject.label.at-random-randomNoNumberSub = 【随机】 阶段：{{.stage}}，{{.label}}：{{.name}} 已随机，组别：{{.group}}，子组别：{{.subGroup}}
history.subject.label.signOut = 【停用】 {{.label}}：{{.name}} 已停用，停用原因：{{.reason}}
history.subject.label.at-random-signOut = 【停用】 阶段：{{.stage}}，{{.label}}：{{.name}} 已停用，停用原因：{{.reason}}
history.subject.label.signOutReal = 【停用】 {{.label}}：{{.name}} 已停用，实际停用日期：{{.signOutRealTime}}，停用原因：{{.reason}}
history.subject.label.at-random-signOutReal = 【停用】 阶段：{{.stage}}，{{.label}}：{{.name}} 已停用，实际停用日期：{{.signOutRealTime}}，停用原因：{{.reason}}
history.subject.label.unblinding = 【紧急揭盲】 {{.label}}：{{.name}} 已紧急揭盲，揭盲原因：{{.reasonStr}}，备注：{{.reason}}，是否已经通知申办方：{{.isSponsor}}，备注：{{.remark}}
history.subject.label.at-random-unblinding = 【紧急揭盲】 阶段：{{.stage}}，{{.label}}：{{.name}} 已紧急揭盲，揭盲原因：{{.reasonStr}}，备注：{{.reason}}，是否已经通知申办方：{{.isSponsor}}，备注：{{.remark}}
history.subject.label.updateSubjectNo = 【修改】{{.label}}：{{.oldSubjectNo}}，改为：{{.shortname}}
history.subject.label.screen = 【筛选】{{.label}}：{{.name}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，ICF签署日期：{{.icfTime}}
history.subject.label.at-random-screen = 【筛选】 阶段：{{.stage}}，{{.label}}：{{.name}} 已筛选，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，ICF签署日期：{{.icfTime}}
history.subject.label.screenScreenFail = 【筛选】{{.label}}：{{.name}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}
history.subject.label.update = 【修改】{{.label}}：{{.name}}，{{.content}}
history.subject.label.at-random-update = 【修改】 阶段：{{.stage}}，{{.label}}：{{.name}}，{{.content}}
history.subject.label.updateCustomize = 【修改】{{.label}}：{{.name}}，{{.content}}，{{.updateFields}}
history.subject.label.update2Customize = 【编辑】 {{.updateFields}}
history.subject.label.updateCustomizeConnectingSymbol = ，
history.subject.label.updateCustomizeLastSymbolKey = 。
history.subject.label.updateCustomizeJoinTime = 入组时间：{{.joinTime}}
history.subject.label.updateCustomizeStage = 阶段：{{.stage}}
history.subject.label.updateCustomizeSignOutRealTime = 实际停用日期：{{.signOutRealTime}}
history.subject.label.updateCustomizeIsScreen = 是否筛选成功：{{.isScreen}}
history.subject.label.updateCustomizeScreenTime = 筛选日期：{{.screenTime}}
history.subject.label.updateCustomizeIcfTime = ICF签署日期：{{.icfTime}}
history.subject.label.updateCustomizeReason = 停用原因：{{.reason}}
history.subject.label.updateCustomizeRemark = 完成研究-备注：{{.remark}}
history.subject.label.updateSignOutTime = 【修改】{{.label}}：{{.name}}，{{.content}}，实际停用日期：{{.signOutRealTime}}
history.subject.label.updateScreen = 【修改】{{.label}}：{{.name}}，{{.content}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，ICF签署日期：{{.icfTime}}
history.subject.label.updateScreenSignOutTime = 【修改】{{.label}}：{{.name}}，{{.content}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，ICF签署日期：{{.icfTime}}，实际停用日期：{{.signOutRealTime}}
history.subject.label.updateScreenFail = 【修改】{{.label}}：{{.name}}，{{.content}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}
history.subject.label.updateScreenFailSignOutTime = 【修改】{{.label}}：{{.name}}，{{.content}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，实际停用日期：{{.signOutRealTime}}
history.subject.label.finish = 【完成研究】{{.label}}：{{.name}}，随机号：{{.randomNumber}}，已完成研究，备注：{{.remark}}
history.subject.label.at-random-finish = 【完成研究】 阶段：{{.stage}}，{{.label}}：{{.name}}，随机号：{{.randomNumber}}，已完成研究，备注：{{.remark}}
history.supply-plan.add = 【新增供应计划】 计划名称：{{.name}}，计划说明：{{.description}}
history.supply-plan.update = 【修改供应计划】 计划名称：{{.name}}，计划说明：{{.description}}
history.supply-plan-medicine.add = 【新增供应计划研究产品配置】 研究产品：{{.medicineName}}，初始发放量：{{.initSupply}} ，研究产品警戒值：{{.warning}}，最大缓冲量：{{.buffer}}，再供应量：{{.secondSupply}}，不运送天数：{{.unDistributionDate}}，不发放天数：{{.unProvideDate}}，有效期提醒：{{.validityReminder}}，自动配药：{{.autoSupply}}，自动配药量：{{.autoSupplySize}}，  补充方式：{{.supplyMode}}。
history.supply-plan-medicine.update = 【修改供应计划研究产品配置】 研究产品：{{.medicineName}}，初始发放量：{{.initSupply}} ，研究产品警戒值：{{.warning}}，最大缓冲量：{{.buffer}}，再供应量：{{.secondSupply}}，不运送天数：{{.unDistributionDate}}，不发放天数：{{.unProvideDate}}，有效期提醒：{{.validityReminder}}，自动配药：{{.autoSupply}}，自动配药量：{{.autoSupplySize}}，  补充方式：{{.supplyMode}}。
medicine.autoSupplySize1 = 最大缓冲量
medicine.autoSupplySize2 = 再供应量
medicine.supplyMode1 = 全研究产品补充
medicine.supplyMode2 = 单研究产品补充
medicine.supplyMode3 = 全研究产品补充 + 1个随机研究产品编号
medicine.supplyMode4 = 单研究产品补充 + 1个随机研究产品编号
subject_urgentUnblindingApproval_reason_other = 其他
subject_urgentUnblindingApproval_reason_sae = SAE
subject_urgentUnblindingApproval_reason_pregnancy = 妊娠
subject_urgentUnblindingApproval_reason_policy = 政策要求

[operation_log]
operation_log.label.medicine = 研究产品名称
operation_log.label.site = 中心编号
operation_log.label.dept = 库房名称
operation_log.label.supply = 供应计划名称
operation_log.label.group = 组别代码
operation_log.label.factor = 分层因素
operation_log.label.list = 随机列表名称
operation_log.label.invalidList = 作废随机表
operation_log.label.visitCycle = 访视名称
operation_log.label.drugConfigure = 研究产品配置
operation_log.label.packageConfigure = 包装配置
operation_log.label.medicinesList = 研究产品列表
operation_log.label.updateBatch = 批次管理
operation_log.label.otherMedicines = 未编码研究产品
operation_log.label.simulate_random_name = 名称
operation_log.label.name = 字段名称
operation_log.label.attribute = 属性配置
operation_log.label.uploadMedicine = 上传研究产品
operation_log.label.barcode_add = 生成条形码
operation_log.label.uploadPacklist = 上传包装清单
operation_log.label.deleteMedicine = 批量删除研究产品编号
operation_log.label.projectUser = 人员
operation_log.label.project = 项目设置
operation_log.module.barcode = 项目构建-编码配置
operation_log.module.supply = 项目构建-供应计划
operation_log.module.supply_detail = 项目构建-供应计划-供应计划详情
operation_log.module.random_design = 项目构建-随机配置-随机设计
operation_log.module.project_site = 项目构建-中心管理
operation_log.module.project_storehouse = 项目构建-库房管理
operation_log.module.project_storehouse_medicine_alert = 项目构建-库房管理-研究产品警戒
operation_log.module.visitCycle = 项目构建-研究产品管理-访视管理
operation_log.module.visitSetting = 项目构建-研究产品管理-访视管理-设置
operation_log.module.attribute = 项目构建-属性配置
operation_log.module.drug_configure = 项目构建-研究产品管理-研究产品配置
operation_log.module.drug_configure_setting = 项目构建-研究产品管理-研究产品配置-设置
operation_log.module.package_configure = 项目构建-研究产品列表-设置
operation_log.module.examine = 项目构建-研究产品列表-审核
operation_log.module.update = 项目构建-研究产品列表-修改
operation_log.module.release = 项目构建-研究产品列表-放行
operation_log.module.barcode_add = 项目构建-研究产品管理-条形码列表-生成条形码
operation_log.module.medicinesList_uploadMedicine = 项目构建-研究产品管理-研究产品列表-上传
operation_log.module.medicinesList_uploadPacklist = 项目构建-研究产品管理-研究产品列表-上传包装清单
operation_log.module.medicinesList_delete = 项目构建-研究产品管理-研究产品列表-批量删除
operation_log.module.updateBatch = 项目构建-研究产品管理-批次管理
operation_log.module.otherMedicines = 项目构建-研究产品管理-未编码研究产品
operation_log.module.form = 项目构建-随机配置-表单配置
operation_log.module.region = 项目构建-随机配置-区域配置
operation_log.module.simulate_random = 项目构建-模拟随机
operation_log.module.push = 项目构建-推送统计-发送
operation_log.module.user = 设置-用户
operation_log.module.project = 项目设置
operation_log.module.projectUser = 其它设置-人员管理
operation_log.module.projectUser_role = 其它设置-人员管理-角色
operation_log.module.projectUser_site = 其它设置-人员管理-中心
operation_log.module.projectUser_depot = 其它设置-人员管理-仓库
operation_log.module.project_information = 项目设置-基本信息
operation_log.module.project_env = 项目设置-项目环境
operation_log.module.project_function = 项目设置-业务功能
operation_log.module.project_docking = 项目设置-外部对接
operation_log.module.project_custom = 项目设置-自定义流程
operation_log.module.project_permission = 项目设置-项目权限
operation_log.module.notifications = 其他设置-通知设置
operation_log.module.configure_export = 其他设置-配置导出
operation_log.module.project_basic_information = 项目设置-基本信息
operation_log.module.subjects = 受试者管理
operation_log.module.project_notice = 项目通知
operation_log.module.project_multi_language = 多语言
operation_log.module.project_multi_language_translate = 多语言-
operation_log.module.project_multi_language_batch_upload = 多语言-
operation_log.add = 新增
operation_log.edit = 编辑
operation_log.delete = 删除
operation_log.copy = 复制
operation_log.run = 运行
operation_log.unbind = 解绑
operation_log.close = 关闭
operation_log.setting = 设置
operation_log.cancel = 取消
operation_log.reauthorization = 再次授权
operation_log.invite_again = 再次邀请
operation_log.export = 导出
operation_log.project_copy = 项目复制
operation_log.cohort_copy = 复制
operation_log.activate = 激活
operation_log.inactivate = 失活
operation_log.barcode.env_name = 环境
operation_log.barcode.random = 编码规则
operation_log.barcode.manual = 手动编码上传
operation_log.barcode.auto = 系统自动编码
operation_log.supply.env_name = 环境
operation_log.supply.name = 计划名称
operation_log.supply.status = 计划状态
operation_log.supply.status_effective = 有效
operation_log.supply.status_invalid = 无效
operation_log.supply.site = 计划适用中心
operation_log.supply.all_site = 全部中心
operation_log.supply.desc = 计划描述
operation_log.supply.control = 供应计划控制
operation_log.supply.alarm = 中心库存警戒
operation_log.supply.supply = 自动供应
operation_log.supply.blindMedicine = 盲态研究产品
operation_log.supply.openMedicine = 开放研究产品
operation_log.supply.forecastStop = 盲态研究产品最低预测自动供应停用
operation_log.supply_detail.env_name = 环境
operation_log.supply_detail.name = 研究产品
operation_log.supply_detail.init_supply = 初始发放量
operation_log.supply_detail.warning = 中心库存警戒值
operation_log.supply_detail.dispensing_alert = 受试者发放警戒值
operation_log.supply_detail.na = NA
operation_log.supply_detail.buffer = 最大缓冲量
operation_log.supply_detail.second_supply = 再供应量
operation_log.supply_detail.forecast = 最低预测
operation_log.supply_detail.un_distribution_date = 不运送天数
operation_log.supply_detail.un_provide_date = 不发放天数
operation_log.supply_detail.validity_reminder = 有效期提醒
operation_log.supply_detail.auto_supply_size = 自动配药量
operation_log.supply_detail.supply_mode_key = 补充方式
operation_log.supply_detail.forecastPeriod = 预测窗口期
operation_log.supply_detail.supply_mode.all_supply = 全研究产品补充
operation_log.supply_detail.supply_mode.single = 单研究产品补充
operation_log.supply_detail.supply_mode.all_one = 全研究产品补充+1个随机研究产品编号
operation_log.supply_detail.supply_mode.single_one = 单研究产品补充+1个随机研究产品编号
operation_log.addBarcode.env_name = 环境
operation_log.addBarcode.medicineName = 研究产品名称
operation_log.addBarcode.medicineSpec = 研究产品规格
operation_log.addBarcode.storehouse = 库房
operation_log.addBarcode.expireDate = 有效期
operation_log.addBarcode.batchNumber = 批次号
operation_log.addBarcode.count = 研究产品码个数
operation_log.addBarcode.prefix = 短码前缀
operation_log.addBarcode.barcodeRule = 研究产品条形码规则
operation_log.addBarcode.isPackageBarcode = 包装条形码
operation_log.addBarcode.packageRule = 包装条形码规则
operation_log.addBarcode.rule_order = 顺序
operation_log.addBarcode.rule_reverse = 乱序
operation_log.addBarcode.openPackageBarcode = 打开 
operation_log.addBarcode.closePackageBarcode = 关闭
operation_log.uploadMedicines.env_name = 环境
operation_log.uploadMedicines.onlyID = 表单ID
operation_log.uploadMedicines.medicineName = 研究产品名称
operation_log.uploadMedicines.medicineSpec = 研究产品规格
operation_log.uploadMedicines.storehouse = 库房
operation_log.uploadMedicines.expireDate = 有效期
operation_log.uploadMedicines.batchNumber = 批次号
operation_log.uploadMedicines.count = 数量
operation_log.uploadMedicines.singleCount = 单品数量
operation_log.uploadMedicines.packageCount = 包装数量
operation_log.uploadMedicines.deleteMedicines = 批量删除的研究产品编号
operation_log.uploadMedicines.fileName = 上传文件名
operation_log.updateBatch.batch = 库存批次号
operation_log.updateBatch.expirationDate = 有效期
operation_log.updateBatch.status = 选择的研究产品状态
operation_log.updateBatch.updateBatch = 更新批次号
operation_log.updateBatch.name = 研究产品名称
operation_log.updateBatch.updateCount = 更新数量
operation_log.updateBatch.updateExpirationDate = 更新有效期
operation_log.updateBatch.toBeWarehoused = 待入仓
operation_log.updateBatch.available = 可用
operation_log.updateBatch.delivered = 已确认
operation_log.updateBatch.transit = 已运送
operation_log.updateBatch.quarantine = 已隔离
operation_log.updateBatch.used = 已使用
operation_log.updateBatch.lose = 丢失/作废
operation_log.updateBatch.expired = 已过期
operation_log.updateBatch.InOrder = 待确认
operation_log.updateBatch.stockPending = 待入库
operation_log.updateBatch.apply = 已申请
operation_log.updateBatch.frozen = 已冻结
operation_log.updateBatch.approval = 待审批
operation_log.updateBatch.lock = 锁定
operation_log.updateBatch.depot = 库房
operation_log.updateBatch.group = 组别
operation_log.updateBatch.warn = 受试者警戒人数
operation_log.updateBatch.capacity = 受试者上限人数
operation_log.updateBatch.batchNo = 批次号
operation_log.simulateRandom.env_name = 环境
operation_log.simulateRandom.onlyID = 表单ID
operation_log.simulateRandom.name = 名称
operation_log.simulateRandom.randomList = 随机列表
operation_log.simulateRandom.siteQuantity = 中心数
operation_log.simulateRandom.countryQuantity = 国家数
operation_log.simulateRandom.regionQuantity = 区域数
operation_log.simulateRandom.RunQuantity = 运行次数
operation_log.simulateRandom.SubjectQuantity = 受试者数
operation_log.simulateRandom.FactorRatio = 分层例数
operation_log.simulateRandom.run = 模拟随机：运行
operation_log.project_storehouse.env_name = 环境
operation_log.project_storehouse.onlyID = 表单ID
operation_log.project_storehouse.name = 库房
operation_log.project_storehouse.country = 所属国家地区
operation_log.project_storehouse.contacts = 联系人
operation_log.project_storehouse.phone = 手机号
operation_log.project_storehouse.email = 邮箱
operation_log.project_storehouse.address = 地址
operation_log.project_storehouse.connected = 是否对接
operation_log.project_storehouse.supplier = 物流供应商
operation_log.project_storehouse.notIncluded = 订单中不包含隔离研究产品
operation_log.project_storehouse.research_product_name_validity_reminder = 研究产品名称(有效期提醒)
operation_log.project_storehouse.medicine_name = 研究产品名称
operation_log.project_storehouse.medicine_alert = 研究产品警戒值
operation_log.project_storehouse.medicine_info = 研究产品名称/警戒值/有效期提醒
operation_log.supplierType.shengsheng = 生生物流
operation_log.supplierType.catalent = catalent
operation_log.supplierType.baicheng = 佰诚物流
operation_log.supplierType.eDRUG = eDRUG平台
operation_log.projectSiteStatus.valid = 有效
operation_log.projectSiteStatus.invalid = 无效
operation_log.projectSiteActive.open = 开启
operation_log.projectSiteActive.close = 关闭
operation_log.project_site.env_name = 环境
operation_log.project_site.number = 中心编号
operation_log.project_site.name = 中心标准名称
operation_log.project_site.shortName = 中心简称
operation_log.project_site.country = 国家/地区
operation_log.project_site.region = 区域
operation_log.project_site.status = 状态
operation_log.project_site.active = 自动订单供应
operation_log.project_site.supplyPlan = 供应计划
operation_log.project_site.storehouse = 库房名称
operation_log.project_site.contacts = 联系人
operation_log.project_site.phone = 联系方式
operation_log.project_site.email = 邮箱
operation_log.project_site.address = 地址
operation_log.project_site.order_trail = 首次研究产品自动订单：开启，订单编号
operation_log.attribute.env_name = 环境
operation_log.attribute.random = 随机化
operation_log.attribute.random_true = 随机
operation_log.attribute.random_false = 非随机
operation_log.attribute.isRandomNumber = 随机号展示
operation_log.attribute.isRandomNumber_true = 展示
operation_log.attribute.isRandomNumber_false = 不展示
operation_log.attribute.isRandomSequenceNumber = 随机顺序号展示
operation_log.attribute.isRandomSequenceNumber_true = 展示
operation_log.attribute.isRandomSequenceNumber_false = 不展示
operation_log.attribute.randomSequenceNumberPrefix = 随机顺序号前缀
operation_log.attribute.randomSequenceNumberDigit = 随机顺序号位数
operation_log.attribute.randomSequenceNumberStart = 随机顺序号起始数
operation_log.attribute.dispensing = 发放设计
operation_log.attribute.dispensing_true = 发放
operation_log.attribute.dispensing_false = 不发放
operation_log.attribute.dtpRule = DTP规则
operation_log.attribute.dtpRule_ip = 研究产品
operation_log.attribute.dtpRule_visitFlow = 访视流程
operation_log.attribute.dtpRule_notApplicable = 不适用
operation_log.attribute.randomControl = 随机控制
operation_log.attribute.randomControlRule = 随机控制规则
operation_log.attribute.randomControl1 = 所有的分组，有供应后允许随机
operation_log.attribute.randomControl2 = 已分配的分组，有供应后允许随机
operation_log.attribute.randomControl3 = 强制随机到有供应的分组
operation_log.attribute.randomControl3_info = 至少多少个分组需要有供应
operation_log.attribute.allowRegisterGroup = 实际使用研究产品组别
operation_log.attribute.blind = 盲法
operation_log.attribute.blind_true = 盲态
operation_log.attribute.blind_false = 开放
operation_log.attribute.minimize_calc = 最小化随机计算
operation_log.attribute.minimize_calc_factor = 随机分层
operation_log.attribute.minimize_calc_actual = 实际分层
operation_log.attribute.notApplicable = 不适用
operation_log.attribute.prefix = 受试者前缀
operation_log.attribute.subject_number_rule = 受试者号录入规则
operation_log.attribute.subject_number_rule1 = 自定义
operation_log.attribute.subject_number_rule2 = 自动递增且在项目中唯一
operation_log.attribute.subject_number_rule3 = 自动递增且在中心中唯一
operation_log.attribute.screen = 受试者筛选流程
operation_log.attribute.screen_true = 开启
operation_log.attribute.screen_false = 关闭
operation_log.attribute.prefix_number = 受试者号前缀
operation_log.attribute.prefix_true = 有
operation_log.attribute.prefix_false = 无
operation_log.attribute.sitePrefix = 是否将中心作为前缀
operation_log.attribute.prefixConnector = 前缀连接符
operation_log.attribute.otherPrefix = 受试者号的其他前缀
operation_log.attribute.otherPrefixText = 前缀文本
operation_log.attribute.subjectReplaceText = 受试者号替换文本
operation_log.attribute.subjectReplaceTextEn = 受试者号替换文本(英文)
operation_log.attribute.accuracy = 受试者号位数精确值
operation_log.attribute.accuracy_le = 小于等于
operation_log.attribute.accuracy_eq = 等于
operation_log.attribute.digit = 受试者号位数
operation_log.attribute.isFreeze = 隔离单品计算规则
operation_log.attribute.edcDrugConfigLabel = EDC对接研究产品配置标签
operation_log.attribute.segment = 号段随机
operation_log.attribute.segmentType = 计算规则
operation_log.attribute.serialNumber = 序列号
operation_log.attribute.medicineNumber = 研究产品编号
operation_log.attribute.segmentLength = 号段随机长度
operation_log.attribute.unblindingReason = 揭盲原因
operation_log.attribute.unblindingAllowTrue = 允许备注
operation_log.attribute.unblindingAllowFalse = 不允许备注
operation_log.attribute.allowReplace = 受试者替换
operation_log.attribute.allowReplaceOpen = 开启
operation_log.attribute.allowReplaceUnOpen = 关闭
operation_log.attribute.replaceRule = 替换受试者随机号
operation_log.attribute.ReplaceRuleNumber = 替换受试者随机号规则
operation_log.attribute.blindingRestrictions = 停用非盲受试者
operation_log.attribute.pvBlindingRestrictions = 包含PV揭盲的受试者
operation_log.attribute.IPInheritance = 研究产品继承
operation_log.attribute.RemainingVisit = 剩余访视周期
operation_log.push.registered = 登记
operation_log.push.update = 修改
operation_log.push.random = 随机
operation_log.push.dispense = 发放
operation_log.push.out_visit_dispensing = 访视外发放
operation_log.push.replace = 研究产品替换
operation_log.push.reissue = 补发
operation_log.push.cancel = 研究产品撤销
operation_log.push.retrieval = 研究产品取回
operation_log.push.realDispensing = 实际用药
operation_log.push.subjectReplace = 受试者替换
operation_log.push.unknown = 未知
operation_log.projectUser.emailLanguage = 邮件语言
operation_log.projectUser.email = 邮箱
operation_log.projectUser.roles = 角色
operation_log.projectUser.addRoles = 分配角色
operation_log.projectUser.cancelRoles = 取消角色
operation_log.projectUser.unblindingCode = 生成揭盲码
operation_log.projectUser.sites = 中心
operation_log.projectUser.addSites = 分配中心
operation_log.projectUser.cancelSites = 取消中心
operation_log.projectUser.depots = 仓库
operation_log.projectUser.addDepots = 分配仓库
operation_log.projectUser.cancelDepots = 取消仓库
operation_log.projectUser.App = APP帐号
operation_log.projectUser.status = 状态
operation_log.projectUser.status_effective = 有效
operation_log.projectUser.status_invalid = 无效
operation_log.project_notice.envName = 环境
operation_log.project_notice.notice_rule = 通知规则
operation_log.project_notice.notice_targets = 通知对象
operation_log.project.sponsor = 申办方
operation_log.project.name = 项目名称
operation_log.project.startTime = 项目周期(开始日期)
operation_log.project.endTime = 项目周期(结束日期)
operation_log.project.plannedCases = 计划病例数
operation_log.project.phone = 联系方式
operation_log.project.descriptions = 备注
operation_log.project.timeZone = 时区
operation_log.project.status = 状态
operation_log.project.progress = 进行中
operation_log.project.finish = 已完成
operation_log.project.close = 已关闭
operation_log.project.pause = 已暂停
operation_log.project.terminate = 已终止
operation_log.project.orderCheck = 订单核查
operation_log.project.timing = 定时(包含手动核查)
operation_log.project.realTime = 实时
operation_log.project.notApplicable = 不适用
operation_log.project.orderConfirmation = 中心回收订单确认
operation_log.project.deIsolationApproval = 解隔离审批
operation_log.project.administrators = 管理员
operation_log.project.connectEdc = 同步EDC
operation_log.project.pushMode = 数据推送方式
operation_log.project.real = EDC实时请求
operation_log.project.active = IRT全量推送
operation_log.project.pushRules = 推送规则
operation_log.project.subjectNumber = 受试者号
operation_log.project.subjectUID = 受试者UID
operation_log.project.pushScenario = 推送场景
operation_log.project.registerPush = 登记
operation_log.project.updateRandomFrontPush = 受试者修改(随机前)
operation_log.project.updateRandomAfterPush = 受试者修改(随机后)
operation_log.project.randomPush = 随机
operation_log.project.randomBlockPush = 分层校验不一致，进行随机阻断
operation_log.project.formRandomBlockPush = 表单校验不一致，进行随机阻断
operation_log.project.cohortRandomBlockPush = 群组名称校验不一致，进行随机阻断
operation_log.project.stageRandomBlockPush = 阶段名称校验不一致，进行随机阻断
operation_log.project.randomBlock: 随机阻断因素
operation_log.project.blockCohort: 群组名称
operation_log.project.blockStage: 阶段名称
operation_log.project.blockStratification: 分层
operation_log.project.blockForm: 表单
operation_log.project.dispensingPush = 发药
operation_log.project.screenPush = 筛选
operation_log.project.synchronizationMode = 同步方式
operation_log.project.edcUrl = URL
operation_log.project.edcSupplier = EDC供应商
operation_log.project.edcMappingRules = EDC映射
operation_log.project.folderOid = 文件夹OID
operation_log.project.formOid = 表单OID
operation_log.project.fieldOid = 字段OID
operation_log.project.ipNumberOid = 研究产品编号OID
operation_log.project.dispenseTimeOid = 发放时间OID
operation_log.project.randomizationCode = 随机号
operation_log.project.randomizationTime = 随机时间
operation_log.project.group = 组别
operation_log.project.factor = 分层因素值
operation_log.project.cohor = 群组/再随机
operation_log.project.stepBy = 受试者筛选时同步
operation_log.project.timeFull = 受试者随机时同步
operation_log.project.connectLearning = 对接eLearning
operation_log.project.needLearning = 必须完成课程
operation_log.project.needLearningEnv = 环境
operation_log.project.unblindingControl = 揭盲控制
operation_log.project.unblindingSms = 短信
operation_log.project.unblindingProcess = 流程操作
operation_log.project.unblindingCode = 揭盲码
operation_log.project.pvUnblinding = 揭盲(pv)
operation_log.project.pvUnblindingSms = 短信(pv)
operation_log.project.pvUnblindingProcess = 流程操作(pv)
operation_log.project.orderApprovalControl = 研究中心订单审批审批控制
operation_log.project.envName = 环境名称
operation_log.project.envCapacity = 入组上限
operation_log.project.alertThresholds = 状态/上限值/提醒阈值
operation_log.project.envReminderThresholds = 提醒阈值
operation_log.project.newEnvName = 新环境
operation_log.project.lockStatus = 状态
operation_log.project.unlock = 解锁
operation_log.project.locked = 锁定
operation_log.project.roleName = 角色名称
operation_log.project.scope = 分类
operation_log.project.roleStatus = 状态
operation_log.project.roleDescription = 说明
operation_log.project.cancelRolePermissionSelect = 取消勾选
operation_log.project.addRolePermissionSelect = 勾选
operation_log.project.cohort = 群组名称
operation_log.project.stage = 阶段名称
operation_log.project.complete = 完成
operation_log.project.draft = 草稿
operation_log.project.enrollment = 入组
operation_log.project.stop = 停止
operation_log.project.enrollmentFull = 入组已满
operation_log.project.capacity = 入组上限
operation_log.project.reminderThresholds = 提醒阈值
operation_log.project.lastStage = 上一阶段
operation_log.project.cohortStatus = 状态
operation_log.region.name = 名称
operation_log.region.env_name = 环境
operation_log.random_design.env_name = 环境
operation_log.random_design.factorLabel = 分层因素
operation_log.random_design.sync = 同步
operation_log.random_design.inactivate = 同步
operation_log.random_design.list = 随机列表名称
operation_log.random_design.type = 随机类型
operation_log.random_design.block = 区组随机
operation_log.random_design.min = 最小化随机
operation_log.random_design.group_name = 组别名称
operation_log.random_design.group_code = 组别代码
operation_log.random_design.status = 状态
operation_log.random_design.status_effective = 有效
operation_log.random_design.status_invalid = 无效
operation_log.random_design.factor.layer = 地区分层
operation_log.random_design.factor.number = 字段编号
operation_log.random_design.factor.calcType = 计算公式
operation_log.random_design.factor.formula = 自定义公式
operation_log.random_design.factor.keepDecimal = 保留小数位
operation_log.random_design.factor.roundingMethod = 保留小数位-取整方式
operation_log.random_design.factor.roundingMethod_up = 向上取整
operation_log.random_design.factor.roundingMethod_down = 向下取整
operation_log.random_design.factor.calcType_age = 年龄
operation_log.random_design.factor.calcType_bmi = BMI
operation_log.random_design.factor.inputLabel = 录入字段名称
operation_log.random_design.factor.inputWeightLabel = 录入体重字段名称
operation_log.random_design.factor.inputHeightLabel = 录入身高字段名称
operation_log.random_design.factor.name = 系统字段
operation_log.random_design.factor.label = 名称
operation_log.random_design.factor.type = 控件类型
operation_log.random_design.factor.options = 选项
operation_log.random_design.factor.options_label_value = 选项标签
operation_log.random_design.factor.folder_oid = 文件夹OID
operation_log.random_design.factor.form_oid = 表单OID
operation_log.random_design.factor.field_oid = 字段OID
operation_log.random_design.factor.disable = 禁用地区分层
operation_log.random_design.factor.country = 开启国家分层
operation_log.random_design.factor.site = 开启中心分层
operation_log.random_design.factor.region = 开启区域分层
operation_log.random_design.factor.status = 状态
operation_log.random_design.factor.precision = 保留小数位
operation_log.random_design.factor.status_effective = 有效
operation_log.random_design.factor.status_invalid = 无效
operation_log.random_design.mapping.random.folder_oid = 随机号-文件夹OID
operation_log.random_design.mapping.random.form_oid = 随机号-表单OID
operation_log.random_design.mapping.random.field_oid = 随机号-字段OID
operation_log.random_design.mapping.group.folder_oid = 组别-文件夹OID
operation_log.random_design.mapping.group.form_oid = 组别-表单OID
operation_log.random_design.mapping.group.field_oid = 组别-字段OID
operation_log.random_design.mapping.time.folder_oid = 随机时间-文件夹OID
operation_log.random_design.mapping.time.form_oid = 随机时间-表单OID
operation_log.random_design.mapping.time.field_oid = 随机时间-字段OID
operation_log.random_list.env_name = 环境
operation_log.random_list.onlyID = 表单ID
operation_log.random_list.name = 名称
operation_log.random_list.site = 中心
operation_log.random_list.initial_number = 初始编号
operation_log.random_list.end_number = 终止编号
operation_log.random_list.block_rule = 区组规则
operation_log.random_list.random_number_rule = 随机号规则
operation_log.random_list.block_rule_order = 顺序
operation_log.random_list.block_rule_reverse = 乱序
operation_log.random_list.random_number_rule_order = 顺序
operation_log.random_list.random_number_rule_reverse = 乱序
operation_log.random_list.weight_ratio = 组别配置(权重比)
operation_log.random_list.block_configuration = 区组配置
operation_log.random_list.factor_ratio = 分层因素(权重比)
operation_log.random_list.number_length = 号码长度
operation_log.random_list.seed = 随机种子
operation_log.random_list.prefix = 号码前缀
operation_log.random_list.size = 可接受的区组大小（多个区组用"，"隔开）
operation_log.random_list.status = 状态
operation_log.random_list.disable = 禁用
operation_log.random_list.enable = 启用
operation_log.random_list.invalid = 作废
operation_log.random_list.isRandom = 中心没有分配随机号不能入组
operation_log.random_list.isCountryRandom = 国家没有分配随机号不能入组
operation_log.random_list.isRegionRandom = 区域没有分配随机号不能入组
operation_log.random_list.set_site = 区组分配给中心
operation_log.random_list.set_region = 区组分配给区域
operation_log.random_list.set_country = 区组分配给国家
operation_log.random_list.set_factor = 区组分配给分层
operation_log.random_list.clean_factor = 清空分层
operation_log.random_list.set_count = 设置人数
operation_log.random_list.file_name = 上传
operation_log.random_list.factor = 分层因素
operation_log.random_list.estimateNumber = 预计人数
operation_log.random_list.warnNumber = 警戒人数
operation_log.random_list.block = 区组
operation_log.random_list.randomNumber = 随机号
operation_log.random_list.randomNumberStatus = 状态
operation_log.visitCycle.env_name = 环境
operation_log.visitCycle.type = 访视日期类型
operation_log.visitCycle.baseCohort = baseline基准
operation_log.visitCycle.sort = 排序
operation_log.visitCycle.number = 访视编号
operation_log.visitCycle.name = 访视名称
operation_log.visitCycle.random = 是否随机
operation_log.visitCycle.dispensing = 是否发放
operation_log.visitCycle.replace = 允许受试者替换
operation_log.visitCycle.doseAdjustment = 剂量调整
operation_log.visitCycle.startDays = 起始天数
operation_log.visitCycle.endDays = 结束天数
operation_log.visitCycle.folder_oid = 文件夹OID
operation_log.visitCycle.form_oid = 表单OID
operation_log.visitCycle.dispensing_ip_oid = 发放研究产品OID
operation_log.visitCycle.dispensing_time_oid = 发放时间OID
operation_log.visitCycle.interval = 间隔时长
operation_log.visitCycle.period = 窗口期
operation_log.visitCycle.group = 组别
operation_log.visitCycle.baseline = baseline
operation_log.visitCycle.lastdate = Lastdate
operation_log.visitCycle.sop = 访视流程
operation_log.visitCycle.version = 访视版本号
operation_log.visitCycle.push = 预览并发布
operation_log.visitCycle.label = 按标签发放
operation_log.visitCycle.open_setting = 开放配置
operation_log.visitCycle.formula = 公式计算
operation_log.visitCycle.dtp = 是否DTP
operation_log.visitCycle.DTPMode = DTP方式
operation_log.visitCycle.send-type-0 = 中心（中心库存）
operation_log.visitCycle.send-type-1 = 中心（直接寄送受试者）
operation_log.visitCycle.send-type-2 = 库房（直接寄送受试者）
operation_log.visitSetting.env_name = 环境
operation_log.visitSetting.unscheduled_visit = 计划外访视
operation_log.visitSetting.name_zh = 中文名称
operation_log.visitSetting.name_en = 英文名称
operation_log.drug_configure.env_name = 环境
operation_log.drug_configure.onlyID = 表单ID
operation_log.drug_configure.group = 组别
operation_log.drug_configure.preGroup = 主组别
operation_log.drug_configure.subGroup = 子组别
operation_log.drug_configure.open = 发放方式
operation_log.drug_configure.formula = 按公式计算
operation_log.drug_configure.spec = 规格
operation_log.drug_configure.calculationType = 公式
operation_log.drug_configure.age = 年龄
operation_log.drug_configure.weight = 体重
operation_log.drug_configure.bsa = 简易体表面积BSA
operation_log.drug_configure.otherBsa = 其他体表面积BSA
operation_log.drug_configure.drugValue = 研究产品名称/发放数量/规格
operation_log.drug_configure.dispensingNumber = 发放数量
operation_log.drug_configure.visitCycles = 访视名称
operation_log.drug_configure.label = (组合)发放标签
operation_log.drug_configure.drugLabel = 发放标签
operation_log.drug_configure.room = 房间号
operation_log.drug_configure.isDispense = 不发放天数
operation_log.drug_configure.notDispenseConfig = 研究产品名称-不发放天数
operation_log.drug_configure.isOpenPackage = 按包装运输
operation_log.drug_configure.isOpenApplication = 研究中心订单申请
operation_log.drug_configure.supplyRatio = 供应比例
operation_log.drug_configure.orderApplictionConfig = 研究产品名称-比例
operation_log.drug_configure.packageConfig = 研究产品名称-比例，混合包装
operation_log.drug_configure.packageConfigNew = 研究产品名称-比例，混合包装
operation_log.drug_configure.drugNameSpec = 研究产品名称/规格
operation_log.drug_configure.visitDrugNameDispeningNumber = 访视名称/研究产品名称/发放数量
operation_log.drug_configure.weightDispeningNumber = 体重范围/发放数量
operation_log.drug_configure.ageDispeningNumber = 年龄范围/发放数量
operation_log.drug_configure.specifications = 单位容量
operation_log.drug_configure.standard = 单位计算标准
operation_log.drug_configure.comparisonSwitch = 体重比较计算
operation_log.drug_configure.comparisonType = 比较条件
operation_log.drug_configure.comparisonRatio = 变化
operation_log.drug_configure.currentComparisonType = 本次计算体重
operation_log.drug_configure.otherCheck = 未编号研究产品
operation_log.drug_configure.openCheck = 开放研究产品
operation_log.drug_configure.keepDecimal = 保留小数位
operation_log.drug_configure.precision = 保留位数
operation_log.drug_configure.automatic_recode = 自动赋值
operation_log.drug_configure.automatic_recode_spec = 计算单位
operation_log.drug_configure.check = 勾选
operation_log.drug_configure.uncheck = 未勾选
operation_log.drug_configure.calculationOpen = 开启
operation_log.drug_configure.calculationUnOpen = 未开启
operation_log.drug_configure.weightCalculation = 上次访视计算体重
operation_log.drug_configure.weightActual = 上次访视实际体重
operation_log.drug_configure.weightRandom = 随机访视体重
operation_log.drug_configure.customerCalculation = 自定义公式
operation_log.drug_configure_setting.env_name = 环境
operation_log.drug_configure_setting.dtp_ipType = 研究产品/发放方式
operation_log.drug_configure_setting.dtp_ipType_site = 中心（中心库存）
operation_log.drug_configure_setting.dtp_ipType_siteSubject = 中心（直接寄送受试者）
operation_log.drug_configure_setting.dtp_ipType_depotSubject = 库房（直接寄送受试者）
operation_log.drug_configure_setting.doseAdjustment = 剂量管理
operation_log.drug_configure_setting.selectType = 剂量选择
operation_log.drug_configure_setting.doseForm = 剂量表单
operation_log.drug_configure_setting.isFirstInitial = 首次访视启用初始剂量
operation_log.drug_configure_setting.isDoseReduction = 允许受试者剂量下调
operation_log.drug_configure_setting.frequency = 次数
operation_log.drug_configure_setting.doseLevel = 剂量水平
operation_log.drug_configure_setting.doseLevelID = ID
operation_log.drug_configure_setting.doseLevelName = 名称
operation_log.drug_configure_setting.doseLevelGroup = 组别
operation_log.drug_configure_setting.doseLevelDoseDistribution = 发放剂量
operation_log.drug_configure_setting.doseLevelInitialDose = 初始剂量
operation_log.drug_configure_setting.visitJudgment = 访视判断
operation_log.drug_configure_setting.visitJudgmentID = ID
operation_log.drug_configure_setting.visitJudgmentName = 名称/值
operation_log.drug_configure_setting.visitJudgmentGroup = 组别
operation_log.drug_configure_setting.visitJudgmentDoseDistribution = 发放剂量
operation_log.drug_configure_setting.visitJudgmentVisitInheritance = 后续访视继承
operation_log.drug_configure_setting.visitJudgmentVisitInheritanceCount = 后续访视继承次数
operation_log.drug_configure_setting.InheritanceGroup = 后续访视继承组别
operation_log.drug_configure_setting.InheritanceRule = 后续访视继承规则
operation_log.drug_configure_setting.groupRuleMain = 主访视
operation_log.drug_configure_setting.groupRuleStop = 停止发放
operation_log.drug_examine.nameDateNumberCount = 研究产品名称/有效期/批次号/数量
operation_log.drug_examine.examineConfig = 审核确认
operation_log.drug_examine.examineNotes = 备注
operation_log.drug_update.nameDateNumberCount = 研究产品名称/有效期/批次号/数量
operation_log.form.env_name = 环境
operation_log.form.name = 字段名称
operation_log.form.editable = 可修改
operation_log.form.required = 必填
operation_log.form.type = 控件类型
operation_log.form.format = 格式类型
operation_log.form.options = 选项
operation_log.form.input = 输入框
operation_log.form.inputNumber = 数字输入框
operation_log.form.textArea = 多行文本框
operation_log.form.select = 下拉框
operation_log.form.checkbox = 复选框
operation_log.form.radio = 单选框
operation_log.form.switch = 开关
operation_log.form.datePicker = 日期选择框
operation_log.form.timePicker = 时间选择框
operation_log.form.length = 长度
operation_log.form.max = 最大值
operation_log.form.min = 最小值
operation_log.form.variableFormat = 变量格式
operation_log.form.status = 状态
operation_log.form.status_effective = 有效
operation_log.form.status_invalid = 无效
operation_log.form.applicationType = 应用类型
operation_log.form.applicationTypeRegister = 受试者登记
operation_log.form.applicationTypeFormula = 公式计算
operation_log.form.applicationTypeDoseAdjustment = 剂量调整
operation_log.form.applicationTypeFactorCalc = 分层计算
operation_log.form.variable = 变量ID
operation_log.form.currentTime = 当前时间
operation_log.project_multi_language.projectName = 项目
operation_log.project_multi_language.language = 语言
operation_log.project_multi_language.status = 启用状态
operation_log.project_multi_language.sharedSystemLibrary = 共享系统库
operation_log.project_multi_language_translate.projectName = 项目
operation_log.project_multi_language_translate.languageLibrary = 语言库
operation_log.project_multi_language_translate.pagePath = 页面路径
operation_log.project_multi_language_translate.envName = 环境
operation_log.project_multi_language_translate.cohortName = 群组
operation_log.project_multi_language_translate.stageName = 阶段
operation_log.project_multi_language_translate.type = 类型
operation_log.project_multi_language_translate.key = Key
operation_log.project_multi_language_translate.name = 名称
operation_log.project_multi_language_translate.label = label
operation_log.project_multi_language_batch_upload.projectName = 项目
operation_log.project_multi_language_batch_upload.filename = 批量导入文件名

[edc]
is_screen = 是否筛选成功
screen_time = 筛选日期
icf_time = ICF签署日期
edc_push_subject_number = 受试者号
edc_push_randomization_number = 随机号
edc_push_group = 组别
edc_push_randomization_time = 随机时间
edc_push_visit_number = 访视号
edc_push_dispense = 发放
edc_push_dispense_time = 发放时间
edc_push_drug = 药物
edc_push_drug_level = 剂量水平
edc_push_drug_label = 标签
edc_push_cohort = 群组/阶段
edc_push_edc_return = EDC返回

[edc_push_log]
edc_push_error_code_200 = 成功
edc_push_error_code_201 = 时间戳、签名和基础的参数错误，请联系IRT工程师确认。
edc_push_error_code_202 = 项目编号错误，请联系IRT配置管理员确认。
edc_push_error_code_203 = 环境编号错误，请联系IRT配置管理员确认。
edc_push_error_code_204 = 中心编号错误，请联系IRT配置管理员确认。
edc_push_error_code_205 = 受试者重复，请联系EDC配置管理员人工处理。
edc_push_error_code_206 = 受试者号前缀规则不一致，请联系EDC和IRT配置管理员确认。
edc_push_error_code_207 = 修改失败，目标受试者号已存在，请重新确认。
edc_push_error_code_208 = 受试者创建中，请稍后操作。
edc_push_error_code_209 = 创建失败，EDC中心版本未推送，请联系EDC配置管理员确认。
edc_push_error_code_210 = 创建失败，姓名缩写缺失，请重新确认。
edc_push_error_code_211 = 创建失败，请联系EDC工程师确认。
edc_push_error_code_212 = EDC数据保存时解析出错，请联系EDC工程师确认。
edc_push_error_code_213 = EDC数据请求解析错误，请联系EDC工程师确认。
edc_push_error_code_299 = 其他未定义错误，请联系EDC工程师确认。

[project_dynamics]
project_dynamics_scene_personnel = 人员
project_dynamics_scene_order = 订单
project_dynamics_scene_unblinding = 揭盲
project_dynamics_scene_forecast = 库存
project_dynamics_scene_visit = 访视
project_dynamics_type_enter_site = 进入中心
project_dynamics_type_bind_storehouse = 绑定库房
project_dynamics_type_role_assignment = 角色分配
project_dynamics_type_overtime = 超时
project_dynamics_type_emergency_unblinding = 紧急揭盲
project_dynamics_type_emergency_unblinding_pv = pv揭盲
project_dynamics_type_alert_storehouse = 仓库警戒
project_dynamics_type_forecast = 库存预测时间
project_dynamics_type_visit = 访视超窗
project_dynamics_content_enter_site = 【{{.siteName}}】<a>{{.email}}</a>已进入中心
project_dynamics_content_bind_storehouse = 【{{.storehouseName}}】<a>{{.email}}</a>已绑定库房
project_dynamics_content_role_assignment = 【角色分配】<a>{{.email}}</a>已分配{{.roles}}角色
project_dynamics_content_overtime = 【订单超时】<a>{{.orderNumber}}</a>已超时
project_dynamics_content_emergency_unblinding = 【{{.siteName}}】<a>{{.subjectName}}</a>已揭盲
project_dynamics_content_emergency_unblinding_emergency = 【{{.siteName}}】<a>{{.subjectName}}</a>已揭盲(紧急)
project_dynamics_content_emergency_unblinding_pv = 【{{.siteName}}】<a>{{.subjectName}}</a>已揭盲(PV)
project_dynamics_content_alert_storehouse = 【<a>{{.storehouseName}}</a>】仓库研究产品已达警戒值
project_dynamics_content_forecast = 【<a>{{.siteName}}</a>】等库存使用时间预测提醒
project_dynamics_content_visit = 【{{.siteName}}】【{{.subject}}】{{.visit}}已超窗。

[app]
app_scan_package_notification_title = 包装扫码通知
app_scan_package_notification_content = 包装扫码
app_scan_notification_title = 扫码入仓通知
app_scan_notification_content = 扫码入仓
app_shipment_confirmed_notification_title = 订单待确认通知
app_shipment_confirmed_notification_content = 订单待确认
app_shipment_received_notification_title = 订单待接收通知
app_shipment_received_notification_content = 订单待接收
app_recovery_shipment_confirmed_notification_title = 回收订单待确认通知
app_recovery_shipment_confirmed_notification_content = 回收订单待确认
app_recovery_shipment_received_notification_title = 回收订单待接收通知
app_recovery_shipment_received_notification_content = 回收订单待接收
app_recovery_shipment_delivered_notification_title = 回收订单待运送通知
app_recovery_shipment_delivered_notification_content = 回收订单待运送
app_dispensation_confirmed_notification_title = 发放确认通知
app_dispensation_confirmed_notification_content = 发放确认
app_re_dispensation_confirmed_notification_title = 补发确认通知
app_re_dispensation_confirmed_notification_content = 补发确认
app_shipment_delivered_notification_title = 订单待运送通知
app_shipment_delivered_notification_content = 订单待运送
app_shipment_delivered_application_title = 研究中心订单申请通知
app_shipment_delivered_application_content = 研究中心订单申请
app_unblinding_urgent_notification_title = 揭盲(紧急)审批通知
app_unblinding_urgent_notification_content = 揭盲(紧急)审批
app_unschedualed_dispensation_confirmation_notification_title = 计划外发放确认通知
app_unschedualed_dispensation_confirmation_notification_content = 计划外发放确认
app_unblinding_pv_notification_title = 揭盲(PV)审批通知
app_unblinding_pv_notification_content = 揭盲(PV)审批
app_ip_dispensation_notification_title = 研究产品发放通知
app_ip_dispensation_notification_content = 研究产品发放
app_site_alert_notification_title = 中心警戒通知
app_site_alert_notification_content = 研究产品已达到警戒值
app_depot_alert_notification_title = 库房警戒通知
app_depot_alert_notification_content = 研究产品已达到警戒值
app_shipment_timeout_notification_title = 订单超时通知
app_shipment_timeout_notification_content_1 = 订单
app_shipment_timeout_notification_content_2 = 已超时
app_shipment_timeout_notification_content_day = 天
app_shipment_timeout_notification_content_days = 天
app_visit_reminder_title = 访视提醒
app_visit_reminder_content_a = {{.days}}天后您有访视任务，请记得提前安排好！
app_visit_reminder_content_b = 明天您有访视任务，请记得安排好！
app_visit_reminder_content_c = 今天您有访视任务，请记得去检查！
app_visit_reminder_content_d = 今天是不是忘记访视安排？进来看一下呢！

[other]
order_logistics_1 = 顺丰
order_logistics_2 = EMS
order_logistics_3 = 京东
order_logistics_4 = 圆通
order_logistics_5 = 韵达
order_logistics_6 = 中通
order_logistics_7 = 申通
order_logistics_8 = 极兔
order_logistics_9 = 其他