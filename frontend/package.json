{"name": "clinflash-irt", "version": "0.1.0", "license": "UNLICENSED", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@arco-design/web-react": "^2.62.1", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^5.0.0", "@dnd-kit/sortable": "^7.0.2", "@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1", "@originjs/vite-plugin-commonjs": "^1.0.3", "@types/node": "^16.7.13", "@types/qs": "^6.9.7", "@types/react": "^17.0.20", "@types/react-dom": "^17.0.9", "@vitejs/plugin-react": "^4.0.4", "@yaireo/tagify": "4.17.9", "ahooks": "^3.7.2", "antd": "^4.24.0", "copy-to-clipboard": "^3.3.1", "craco-less": "^2.0.0", "dayjs": "^1.11.0", "echarts": "5.4.1", "echarts-for-react": "3.0.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "immutability-helper": "^3.1.1", "jotai": "^2.9.0", "jotai-scope": "^0.7.0", "moment": "^2.29.2", "moment-timezone": "^0.5.45", "react": "^18.2.0", "react-barcode": "^1.4.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-html-parser": "^2.0.2", "react-idle-timer": "^4.6.4", "react-infinite-scroll-component": "^6.1.0", "react-intl": "^6.4.3", "react-modal": "^3.16.1", "react-router-dom": "^6.4.3", "react-scripts": "5.0.0", "react-to-print": "^2.14.6", "ts-md5": "^1.3.1", "typescript": "^4.4.2", "utils": "^0.3.1", "virtualizedtableforantd4": "^1.1.6", "vite": "^4.4.9", "vite-plugin-compression": "^0.5.1", "vite-tsconfig-paths": "^4.2.0"}, "scripts": {"start": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "eslintIgnore": ["src/assets"], "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"@types/react": "^17.0.20"}, "proxy": "http://localhost:8080", "devDependencies": {"@types/file-saver": "^2", "@types/lodash": "^4.14.186"}}