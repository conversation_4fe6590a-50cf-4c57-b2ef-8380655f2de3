package task

import (
	"clinflash-irt/catalent"
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"clinflash-irt/wms"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"sort"
	"strconv"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

/*
	读取配置当前时间点的项目PROD环境

	循环项目环境

		读取 关联供应计划 中心  关联对应药物 过滤未创建订单的中心

		读取属性配置

		获取不发放日期配置

		查询中心库存 排除不发放日期的可用库存

		if	供应计划研究产品开启自动预测
			查询所有受试者发药
			查询所有cohort visitCycle

		查询 研究产品配置盲态属性

		查询库房所有药物	排除不配送日期

		查询库房所有未编号药物	 排除不配送日期

		for 中心
			读取该中心订单邮箱
			读取该中心警戒邮箱

			if 供应计划控制开启
					if 开启自动订单
							触发订单逻辑
					if 开启自动警戒
						触发药物警戒逻辑
						写入app任务
						极光推送 预处理
			else
				for 供应计划研究产品 过滤盲态和开放产品
					if 中心自动订单开启
						触发订单逻辑
					else
						if 实时推送 && 当天该中心未推送过邮件
							触发药物警戒逻辑
						if 定时
							触发药物警戒逻辑
							写入app任务
							极光推送 预处理


		生成的所有订单ID  catelen


订单逻辑
	if 开启
		过滤盲态和开放产品

	达到警戒值药物 = 0
	for 供应计划研究产品
		补充量

		if 警戒值达到 && 最大预测达到
			补充量 = 补充最大量
		if 警戒值达到 && ！最大预测达到
			补充量 = 补充再供应、缓冲
		if ！警戒值达到 && 最大预测达到
			补充量 = 补充最大预测
		if ！警戒值达到 && ！最大预测达到
			达到警戒值药物 ++
			if 全研究补充 || 全研究产品 + 1
				补充最大量
			else 单研究产品
				不补充
		if 补充量 > 0
			拿库房药物ID
			if len(拿库房药物ID) < 补充量
				库存不足
	if 达到警戒值药物 == len(供应计划研究产品)
		不需要补充 return
	if 订单药物数量 == 0 &&  ！库房库存足够
		自动订单警戒

	if 研究产品+1

		拿库房药物ID
		if len(拿库房药物ID) == 0
			库存不足

	if 全研究产品
		if 库房库存足够
			生成订单
		else
			创建失败
	if 单研究产品
		if 订单药物数量 > 0
			生成订单
		if !库房库存足够
			创建失败



警戒逻辑
	if 实时 + 该中心存在今天的警戒通知
		return
	for 供应计划研究产品 过滤盲态和开放产品
		if 警戒值达到 || 最大预测达到
			邮件通知

拿库房药物ID
	if 药物是包装产品
		获取整包数量
	else
		按数量获取库房药物
*/

// SupplyPlanMedicineInfo ..
type SupplyPlanMedicineInfo struct {
	ID             primitive.ObjectID        `json:"id" bson:"_id"`
	SupplyPlanInfo models.SupplyPlanInfo     `json:"info" bson:"info"`
	Medicine       []models.MedicinePlanInfo `json:"medicine" bson:"medicine"`
}

type MedicineAlarm struct {
	ID                  primitive.ObjectID `json:"id" bson:"_id"`
	Name                string             `json:"name" bson:"name"`
	Number              string             `json:"number" bson:"number"`
	StorehouseID        primitive.ObjectID `json:"storehouseId" bson:"storehouse_id"`
	SiteID              primitive.ObjectID `json:"siteId" bson:"site_id"`
	ExpirationDate      string             `json:"expirationDate" bson:"expiration_date"`
	Batch               string             `json:"batch" bson:"batch"`
	PackageSerialNumber string             `json:"packageSerialNumber" bson:"package_serial_number"` //包装号序列号
	SerialNumber        string             `json:"SerialNumber" bson:"serial_number"`                //包装号序列号

}

type ProjectAlarm struct {
	ID           primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID   primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectInfo  models.ProjectInfo `json:"info" bson:"info"`
	Environments models.Environment `json:"envs" bson:"envs"`
}

type AppPush struct {
	RegistrationidZh []string
	RegistrationidEn []string
	TitleZh          string
	TitleEn          string
	AlertbodyZh      string
	AlertbodyEn      string
	SJson            []byte
}

// TaskAlarmMedicineNew 定时任务入口
func TaskAlarmMedicineNew(orderCheck int) error {
	err := AlarmMedicineNew(orderCheck)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// AlarmMedicineNew 入口
func AlarmMedicineNew(orderCheck int, envOIDs ...primitive.ObjectID) error {

	// 读取配置当前时间点的项目PROD环境
	now := time.Now()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "now", now)

	var projects []ProjectAlarm
	var err error
	if len(envOIDs) > 0 {
		projects, err = getTimeZoneProject(ctx, orderCheck, envOIDs[0])
		if err != nil {
			return errors.WithStack(err)
		}
	} else {
		projects, err = getTimeZoneProject(ctx, orderCheck)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	for _, project := range projects {

		envOID := project.Environments.ID
		mails := []models.Mail{}
		// 读取 关联供应计划 中心  关联对应药物 过滤未创建订单的中心
		projectSites, supplyPlanMap, err := getSitePlan(nil, project.Environments.ID)
		if err != nil {
			return err
		}

		// 读取环境仓库信息
		storeIDs := []primitive.ObjectID{}
		slice.ForEach(projectSites, func(index int, item models.ProjectSite) {
			if len(item.StoreHouseID) > 0 {
				storeIDs = append(storeIDs, item.StoreHouseID[0])
			}
		})
		projectStores, err := database.GetProjectStorehouseInfo(storeIDs)
		if err != nil {
			return err
		}

		attributes, err := database.GetAttributes(nil, envOID)
		if err != nil {
			return err
		}

		blindMedicineName, err := tools.IsBlindDrugMap(envOID)
		if err != nil {
			return err
		}

		isOtherMedicine, err := tools.IsOtherDrugMap(envOID)
		if err != nil {
			return err
		}

		// 7064   自动预测 相关
		subjectMap := make(map[string]models.Subject)
		var visitCycles []models.VisitCycle
		var drugConfigures []models.DrugConfigure

		{
			cursor, err := tools.Database.Collection("visit_cycle").Find(nil, bson.M{"env_id": envOID})
			if err != nil {
				return errors.WithStack(err)
			}
			err = cursor.All(nil, &visitCycles)
			if err != nil {
				return errors.WithStack(err)
			}

			for _, value := range visitCycles {
				if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
					subjectMap, err = GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
				}
			}

			cursor, err = tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": envOID})
			if err != nil {
				return errors.WithStack(err)
			}
			err = cursor.All(nil, &drugConfigures)
			if err != nil {
				return errors.WithStack(err)
			}
		}

		packageIsOpen, packageAllCount, packageConfigs, mixPackageConfig, _, err := tools.IsOpenPackage(envOID)
		if err != nil {
			return errors.WithStack(err)
		}

		noticeConfig, alarmState, alarmOrderState, OrderErrorState, OrderSuccessState, alarmMailBody, orderMailBody, err := AutoOrderMailCustomContentStates(project)
		if err != nil {
			return errors.WithStack(err)
		}

		// 实时配置
		var alarmMedicineRecords []models.AlarmMedicineRecords
		if project.ProjectInfo.OrderCheck == 2 { //
			cursor, err := tools.Database.Collection("alarm_medicine_records").Find(nil, bson.M{
				"created_time": bson.M{"$gte": time.Now().Add(-time.Hour * 24).Unix()},
				"env_id":       project.Environments.ID,
			})
			if err != nil {
				return errors.WithStack(err)
			}
			err = cursor.All(nil, &alarmMedicineRecords)
			if err != nil {
				return errors.WithStack(err)

			}
		}

		// 项目初始订单号
		orderNumber, err := getOrderNumber(project.ID)

		orderIds := []primitive.ObjectID{}
		appPush := []AppPush{}

		// 获取未编号轨迹ID
		var medicineOtherKey []models.MedicineOtherKey
		cursor, err := tools.Database.Collection("medicine_other_key").Find(nil, bson.M{"env_id": envOID})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineOtherKey)
		if err != nil {
			return errors.WithStack(err)
		}

		ctx = context.WithValue(ctx, "project", project)
		ctx = context.WithValue(ctx, "visitCycles", visitCycles)
		ctx = context.WithValue(ctx, "subjectMap", subjectMap)
		ctx = context.WithValue(ctx, "drugConfigures", drugConfigures)
		ctx = context.WithValue(ctx, "isOtherMedicine", isOtherMedicine)
		ctx = context.WithValue(ctx, "attributes", attributes)
		ctx = context.WithValue(ctx, "noticeConfig", noticeConfig)
		ctx = context.WithValue(ctx, "packageIsOpen", packageIsOpen)
		ctx = context.WithValue(ctx, "packageAllCount", packageAllCount)
		ctx = context.WithValue(ctx, "packageConfigs", packageConfigs)
		ctx = context.WithValue(ctx, "mixPackageConfig", mixPackageConfig)
		ctx = context.WithValue(ctx, "alarmMailBody", alarmMailBody)
		ctx = context.WithValue(ctx, "orderMailBody", orderMailBody)
		ctx = context.WithValue(ctx, "alarmOrderState", alarmOrderState)
		ctx = context.WithValue(ctx, "OrderErrorState", OrderErrorState)
		ctx = context.WithValue(ctx, "OrderSuccessState", OrderSuccessState)
		ctx = context.WithValue(ctx, "alarmMedicineRecords", alarmMedicineRecords)
		ctx = context.WithValue(ctx, "projectStores", projectStores)
		ctx = context.WithValue(ctx, "medicineOtherKey", medicineOtherKey)
		ctx = context.WithValue(ctx, "blindMedicineName", blindMedicineName)

		callback := func(sctx mongo.SessionContext) (interface{}, error) {

			var err error
			defer func() {
				if err := recover(); err != nil {
					trace := tools.PrintStackTrace(err)
					tools.SaveErrorLog("TaskAlarmMedicine-"+project.ProjectInfo.Number, errors.New(trace))
				}

			}()

			selectStore := false
			selectSubject := false
			for _, item := range projectSites {
				supplyPlan := supplyPlanMap[item.SupplyPlanID]
				if item.Active == 2 || supplyPlan.SupplyPlanInfo.PlanControl && len(supplyPlan.SupplyPlanInfo.AutoSupply) > 0 {
					selectStore = true
				}
				_, ok := slice.Find(supplyPlan.Medicine, func(index int, item models.MedicinePlanInfo) bool {
					_, supplyOk := slice.Find(item.AutoSupplySize, func(index int, supply int8) bool {
						return supply == 3
					})
					return supplyOk
				})
				if ok {
					selectSubject = true
				}
			}

			subjectDispensing := []models.ForecastSubjectDispensing{}
			if selectSubject {
				subjectDispensing, err = GetEnvAllSubjectDispensing(sctx, envOID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			siteMedicines, err := getAllSiteMedicine(sctx, envOID, projectSites, "medicine", attributes)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			siteOtherMedicines, err := getAllSiteMedicine(sctx, envOID, projectSites, "medicine_others", attributes)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			storeMedicine := []MedicineAlarm{}
			storeOtherMedicine := []MedicineAlarm{}
			// 如果中心没开启自动订单 || 供应控制没勾选自动订单 不用查
			if selectStore {
				storeMedicine, err = getAllStoreMedicine(sctx, envOID, projectSites, "medicine")
				if err != nil {
					return nil, errors.WithStack(err)
				}

				storeOtherMedicine, err = getAllStoreMedicine(sctx, envOID, projectSites, "medicine_others")
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			for _, projectSite := range projectSites {
				supplyPlan := supplyPlanMap[projectSite.SupplyPlanID]

				// 供应计划控制开启
				if supplyPlan.SupplyPlanInfo.PlanControl {
					// 过滤
					warnMedicine := filterMedicine(supplyPlan.Medicine, supplyPlan.SupplyPlanInfo.SiteWarning, blindMedicineName)

					autoMedicine := filterMedicine(supplyPlan.Medicine, supplyPlan.SupplyPlanInfo.AutoSupply, blindMedicineName)

					if warnMedicine != nil && len(warnMedicine) > 0 {
						if alarmState { // 药物警戒通知场景关闭
							err = alarmLogic(ctx, sctx, project, projectSite, supplyPlan.Medicine, siteMedicines, siteOtherMedicines, subjectDispensing, &appPush, &mails)
							if err != nil {
								return nil, err
							}
						}
					}
					if autoMedicine != nil && len(autoMedicine) > 0 {
						// 自动订单
						orderId, err := generationOrder(ctx, sctx, orderNumber, projectSite, autoMedicine, siteMedicines, siteOtherMedicines, &storeMedicine, &storeOtherMedicine, subjectDispensing, &mails, supplyPlan.SupplyPlanInfo.AutoSupply)
						if err != nil {
							return nil, err
						}
						if !orderId.IsZero() {
							orderIds = append(orderIds, orderId)
							orderNumbers, _ := strconv.Atoi(orderNumber)
							orderNumber = strconv.Itoa(orderNumbers + 1)
						}
					}

				} else {
					if projectSite.Active == 1 {
						if alarmState { // 药物警戒通知场景关闭
							// 药物警戒
							err = alarmLogic(ctx, sctx, project, projectSite, supplyPlan.Medicine, siteMedicines, siteOtherMedicines, subjectDispensing, &appPush, &mails)
							if err != nil {
								return nil, err
							}
						}

					}

					if projectSite.Active == 2 {
						// 自动订单
						orderId, err := generationOrder(ctx, sctx, orderNumber, projectSite, supplyPlan.Medicine, siteMedicines, siteOtherMedicines, &storeMedicine, &storeOtherMedicine, subjectDispensing, &mails, []int{})
						if err != nil {
							return nil, err
						}

						if !orderId.IsZero() {
							orderIds = append(orderIds, orderId)
							orderNumbers, _ := strconv.Atoi(orderNumber)
							orderNumber = strconv.Itoa(orderNumbers + 1)
						}

					}

				}
			}

			//

			var docs []interface{}
			var mailEnvs []interface{}

			for _, mail := range mails {
				docs = append(docs, mail)
			}

			if len(docs) > 0 {
				_, err := tools.Database.Collection("mail").InsertMany(sctx, docs)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if len(mails) > 0 {
					for _, mail := range mails {

						mailEnvs = append(mailEnvs, models.MailEnv{
							ID:         primitive.NewObjectID(),
							MailID:     mail.ID,
							CustomerID: project.CustomerID,
							ProjectID:  project.ID,
							EnvID:      project.Environments.ID,
						})
					}
					_, err := tools.Database.Collection("mail_env").InsertMany(sctx, mailEnvs)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}

			return nil, nil
		}

		err = func() error {
			cctx := context.Background()
			session, err := tools.MongoClient.StartSession()
			if err != nil {
				tools.SaveErrorLog("TaskAlarmMedicine-"+project.ProjectInfo.Number, err)
				return errors.WithStack(err)
			}
			defer session.EndSession(cctx)
			_, err = session.WithTransaction(cctx, callback)
			if err != nil {
				tools.SaveErrorLog("TaskAlarmMedicine-"+project.ProjectInfo.Number, err)
				return errors.WithStack(err)
			}
			return nil
		}()

		if err == nil {
			for _, id := range orderIds {
				err := catalent.SendToCatalent(id.Hex())
				if err != nil {
					tools.SaveErrorLog(fmt.Sprintf("SendToCatalent-%s", id), err)
				}
				var order models.MedicineOrder
				if err := tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"_id": id}).Decode(&order); err != nil {
					tools.SaveErrorLog(fmt.Sprintf("SendToWMS-%s", id), err)
				} else {
					connectedErr := wms.ConnectedWms(nil, nil, order)

					if connectedErr != nil {
						tools.SaveErrorLog(fmt.Sprintf("SendToWMS-%s", id), connectedErr)
					}
				}
			}

			// 极光推送
			for _, push := range appPush {
				// 中文推送
				registrationIdZh := push.RegistrationidZh
				titleZh := push.TitleZh
				alertBodyZh := push.AlertbodyZh
				registrationIdEn := push.RegistrationidEn
				titleEn := push.TitleEn
				alertBodyEn := push.AlertbodyEn
				sJson := push.SJson
				if len(registrationIdZh) > 0 {
					err = tools.DataAssembly(registrationIdZh, titleZh, alertBodyZh, string(sJson))
					if err != nil {
						tools.SaveErrorLog("TaskAlarmMedicinePushApp-"+project.ProjectInfo.Number, err)

					}
				}

				// 英文推送
				if len(registrationIdEn) > 0 {
					err = tools.DataAssembly(registrationIdEn, titleEn, alertBodyEn, string(sJson))
					if err != nil {
						tools.SaveErrorLog("TaskAlarmMedicinePushApp-"+project.ProjectInfo.Number, err)

					}
				}
			}
		}
	}

	return nil
}

func getTimeZoneProject(ctx context.Context, orderCheck int, envOID ...primitive.ObjectID) ([]ProjectAlarm, error) {
	now := ctx.Value("now").(time.Time)

	nowStr := now.Format("15:04")
	match := bson.M{"envs.name": "PROD", "status": bson.M{"$ne": 2}, "info.order_check": orderCheck, "info.research_attribute": bson.M{"$ne": 1}}
	if envOID != nil { // 手动
		match = bson.M{"envs.id": envOID[0]}
	}

	pipe := mongo.Pipeline{
		{{Key: "$unwind", Value: "$envs"}},
		{{"$match", match}},
		{{"$project", bson.M{
			"info.timeZone":         bson.M{"$ifNull": bson.A{"$info.timeZone", 8}},
			"info.timeZoneStr":      bson.M{"$ifNull": bson.A{"$info.timeZoneStr", "8"}},
			"info.order_check_time": bson.M{"$ifNull": bson.A{"$info.order_check_time", "08:00"}}, //
			"info.order_check_day":  1,                                                            //
			"info.type":             1,
			"info.order_check":      1,
			"info.number":           1,
			"info.name":             1,
			"envs.id":               1,
			"envs.name":             1,
		}}},
	}
	if envOID == nil && orderCheck == 1 { // 模糊匹配， 后面根据代码 转时区
		pipe = append(pipe, bson.D{{"$match", bson.M{"info.order_check_time": bson.M{"$regex": nowStr[2:]}}}})
	}
	//nowStr[2:]
	var projects []ProjectAlarm
	cursor, err := tools.Database.Collection("project").Aggregate(nil, pipe)
	if err != nil {
		return projects, errors.WithStack(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		return projects, errors.WithStack(err)
	}
	if orderCheck == 1 && envOID == nil { // 定时任务触发 过滤不是当前时间点的   不是这个星期
		projects = slice.Filter(projects, func(index int, item ProjectAlarm) bool {
			_, ok := slice.Find(item.ProjectInfo.OrderCheckDay, func(index int, item int) bool {
				return time.Weekday(math.Abs(float64(item)-1)) == now.Weekday()
			})
			if len(item.ProjectInfo.OrderCheckDay) == 0 {
				ok = true
			}

			changeTime := tools.FormatFloatTimeHour(item.ProjectInfo.TimeZoneStr, item.ProjectInfo.Tz, "")
			//changeTime := now.UTC().Add(time.Hour * time.Duration(item.ProjectInfo.TimeZone)).Format("15:04")
			return changeTime == item.ProjectInfo.OrderCheckTime && ok
		})
	}

	return projects, nil
}

func getSitePlan(sctx mongo.SessionContext, envOID primitive.ObjectID) ([]models.ProjectSite, map[primitive.ObjectID]SupplyPlanMedicineInfo, error) {
	var projectSite []models.ProjectSite
	var supplyPlanMedicineInfo []SupplyPlanMedicineInfo
	supplyPlanMedicineInfoMap := map[primitive.ObjectID]SupplyPlanMedicineInfo{}
	match := bson.M{"env_id": envOID, "deleted": 2, "supply_plan_id": bson.M{"$ne": primitive.NilObjectID}}

	cursor, err := tools.Database.Collection("project_site").Aggregate(sctx, mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "medicine_order",
			"localField":   "_id",
			"foreignField": "receive_id",
			"as":           "medicine_order",
		}}},
		// 过滤未创建订单的中心  即订单数量 == 0  jira 1916
		{{"$match", bson.M{"medicine_order.status": bson.M{"$in": bson.A{1, 2, 3, 4, 5, 8, 9}}}}},
		{{"$project", bson.M{
			"medicine_order": 0,
		}}},
	})
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectSite)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	// 根据中心过滤已经全部发药的中心 8750-2

	projectSiteOIDs := slice.Map(projectSite, func(index int, item models.ProjectSite) primitive.ObjectID {
		return item.ID
	})

	type IDsStruct struct {
		ID primitive.ObjectID `bson:"_id"`
	}
	var IDs []IDsStruct

	cursor, err = tools.Database.Collection("subject").Aggregate(sctx, mongo.Pipeline{
		{{"$match", bson.M{"project_site_id": bson.M{"$in": projectSiteOIDs}}}},
		{{"$lookup", bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
		{{"$match", bson.M{"dispensing": bson.M{
			"$elemMatch": bson.M{
				"visit_info.dispensing": true,
				"status":                1,
			}},
		}}},
		{{"$group", bson.M{"_id": "$project_site_id"}}},
	})

	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &IDs)
	if err != nil {
		return nil, nil, err
	}

	projectSite = slice.Filter(projectSite, func(index int, item models.ProjectSite) bool {
		_, ok := slice.Find(IDs, func(index int, ID IDsStruct) bool {
			return item.ID == ID.ID
		})
		return ok
	})

	cursor, err = tools.Database.Collection("supply_plan").Aggregate(sctx, mongo.Pipeline{
		{{"$match", bson.M{"env_id": envOID}}},
		{{Key: "$lookup", Value: bson.M{"from": "supply_plan_medicine", "localField": "_id", "foreignField": "supply_plan_id", "as": "medicine"}}},
		{{"$project", bson.M{
			"medicine": "$medicine.info",
			"info":     "$info",
		}}},
	})
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &supplyPlanMedicineInfo)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	slice.ForEach(supplyPlanMedicineInfo, func(index int, item SupplyPlanMedicineInfo) {
		supplyPlanMedicineInfoMap[item.ID] = item
	})
	return projectSite, supplyPlanMedicineInfoMap, nil
}

func getAllSiteMedicine(sctx mongo.SessionContext, envOID primitive.ObjectID, projectSites []models.ProjectSite, types string, attributes []models.Attribute) ([]MedicineAlarm, error) {
	var medicineList []MedicineAlarm
	status := bson.A{1, 2, 3}
	_, ok := slice.Find(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.IsFreeze
	})
	if ok {
		status = bson.A{1, 2, 3, 4}
	}
	col := tools.Database.Collection("medicine")
	if types == "medicine_others" {
		col = tools.Database.Collection("medicine_others")
	}
	siteOIDs := slice.Map(projectSites, func(index int, item models.ProjectSite) primitive.ObjectID {
		return item.ID
	})
	cursor, err := col.Find(sctx, bson.M{
		"env_id": envOID, "status": bson.M{"$in": status}, "site_id": bson.M{"$in": siteOIDs},
	}, &options.FindOptions{
		Projection: bson.M{
			"_id":             1,
			"name":            1,
			"site_id":         1,
			"expiration_date": bson.M{"$cond": bson.M{"if": bson.M{"$ne": bson.A{"$expiration_date", ""}}, "then": "$expiration_date", "else": "9999-12-31"}},
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &medicineList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return medicineList, nil
}

func getAllStoreMedicine(sctx mongo.SessionContext, envOID primitive.ObjectID, projectSites []models.ProjectSite, types string) ([]MedicineAlarm, error) {
	var medicineList []MedicineAlarm

	storeOIDs := []primitive.ObjectID{}

	slice.ForEach(projectSites, func(index int, item models.ProjectSite) {
		if len(item.StoreHouseID) == 1 && !item.StoreHouseID[0].IsZero() {
			storeOIDs = append(storeOIDs, item.StoreHouseID[0])
		}
	})

	col := tools.Database.Collection("medicine")
	if types == "medicine_others" {
		col = tools.Database.Collection("medicine_others")
	}

	cursor, err := col.Find(sctx, bson.M{
		"env_id": envOID, "status": 1, "storehouse_id": bson.M{"$in": storeOIDs},
	}, &options.FindOptions{
		Projection: bson.M{
			"_id":  1,
			"name": 1,
			//"number":                1,
			"storehouse_id":   1,
			"expiration_date": bson.M{"$cond": bson.M{"if": bson.M{"$ne": bson.A{"$expiration_date", ""}}, "then": "$expiration_date", "else": "9999-12-31"}},
			//"batch_number":          1,
			"package_number":        1,
			"package_serial_number": 1,
			"serial_number":         bson.M{"$ifNull": bson.A{"$serial_number", "$number"}},
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = cursor.All(nil, &medicineList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 按 expiration_date + serial_number 排序
	sort.Slice(medicineList, func(i, j int) bool {
		a := medicineList[i]
		b := medicineList[j]
		// 比较日期
		if a.ExpirationDate > b.ExpirationDate {
			return false
		} else if a.ExpirationDate < b.ExpirationDate {
			return true
		}
		// 日期相同则比较Number
		if a.SerialNumber > b.SerialNumber {
			return false
		} else if a.SerialNumber < b.SerialNumber {
			return true
		}

		return false
	})
	return medicineList, nil
}

func filterMedicine(medicine []models.MedicinePlanInfo, useType []int, blindMedicine map[string]bool) []models.MedicinePlanInfo {

	_, blindUse := slice.Find(useType, func(index int, item int) bool {
		return item == 1
	})
	_, openUse := slice.Find(useType, func(index int, item int) bool {
		return item == 2
	})
	filterMedicines := slice.Filter(medicine, func(index int, item models.MedicinePlanInfo) bool {
		return (blindMedicine[item.MedicineName] && blindUse) || (!blindMedicine[item.MedicineName] && openUse)
	})
	return filterMedicines
}

type AlarmGeneration struct {
	Generation          bool // 存在低于库存的值
	AlarmGenerationInfo []AlarmGenerationInfo
}
type AlarmGenerationInfo struct {
	Warn        int
	ForecastMin int
	ForecastMax int
	SiteCount   int
	Name        string
}

func formulaAlarm(ctx context.Context, types int, projectSite models.ProjectSite, medicinePlanInfo []models.MedicinePlanInfo, siteMedicines []MedicineAlarm, siteOtherMedicines []MedicineAlarm, subjectDispensing []models.ForecastSubjectDispensing) AlarmGeneration {
	now := ctx.Value("now").(time.Time)
	project := ctx.Value("project").(ProjectAlarm)
	isOtherMedicine := map[string]bool{}
	if ctx.Value("isOtherMedicine") != nil {
		isOtherMedicine = ctx.Value("isOtherMedicine").(map[string]bool)
	}
	var alarmGeneration AlarmGeneration

	for _, info := range medicinePlanInfo {

		tmpInfo := AlarmGenerationInfo{}

		// 1. 计算时区偏移（支持半时区）
		f, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
		hours := int(f)
		minutes := int((f - float64(hours)) * 60)
		loc := time.FixedZone("CustomZone", hours*3600+minutes*60)

		// 2. 计算最终时间
		UnProvideDate := now.In(loc).
			Add(time.Hour * 24 * time.Duration(info.NotCountedDate+info.UnProvideDate)).
			Format("2006-01-02")

		//UnProvideDate := now.UTC().Add(time.Hour * time.Duration(project.ProjectInfo.TimeZone)).Add(time.Hour * 24 * time.Duration(info.NotCountedDate+info.UnProvideDate)).Format("2006-01-02")
		siteCount := 0
		// 过滤不计入发放的数量
		if isOtherMedicine[info.MedicineName] {
			siteCount = slice.Count(siteOtherMedicines, func(index int, item MedicineAlarm) bool {
				return item.ExpirationDate > UnProvideDate && info.MedicineName == item.Name && projectSite.ID == item.SiteID
			})
		} else {
			siteCount = slice.Count(siteMedicines, func(index int, item MedicineAlarm) bool {
				return item.ExpirationDate > UnProvideDate && info.MedicineName == item.Name && projectSite.ID == item.SiteID
			})
		}

		if siteCount < info.Warning {
			alarmGeneration.Generation = true
			if types == 1 {
				break
			}

		}

		_, forecast := slice.Find(info.AutoSupplySize, func(index int, item int8) bool {
			return item == 3
		})
		forecastMax := 0
		forecastMin := 0
		if forecast {
			forecastMax, forecastMin = generationForecastNew(ctx, projectSite, info, subjectDispensing)
			if forecastMin > siteCount {
				alarmGeneration.Generation = true
				if types == 1 { //
					break
				}
			}
		}
		tmpInfo.SiteCount = siteCount
		tmpInfo.ForecastMin = forecastMin
		tmpInfo.ForecastMax = forecastMax
		tmpInfo.Warn = info.Warning
		tmpInfo.Name = info.MedicineName
		alarmGeneration.AlarmGenerationInfo = append(alarmGeneration.AlarmGenerationInfo, tmpInfo)
	}
	return alarmGeneration
}

func alarmLogic(ctx context.Context, sctx mongo.SessionContext, project ProjectAlarm, projectSite models.ProjectSite, medicinePlanInfo []models.MedicinePlanInfo, siteMedicines []MedicineAlarm, siteOtherMedicines []MedicineAlarm, subjectDispensing []models.ForecastSubjectDispensing, appPush *[]AppPush, mail *[]models.Mail) error {

	// 实时库存 当天是否推送过  推送过则不需要重复推送

	// 是否需要通知

	infos := formulaAlarm(ctx, 1, projectSite, medicinePlanInfo, siteMedicines, siteOtherMedicines, subjectDispensing)

	// 警戒通知
	if infos.Generation {
		// 邮件通知

		if project.ProjectInfo.OrderCheck == 2 {
			if orderCheckSendToday(ctx, projectSite, "order.no_automatic_title") {
				return nil
			} else {
				err := insertAlarmRecord(sctx, projectSite.EnvironmentID, projectSite.ID, "order.no_automatic_title")
				if err != nil {
					return err
				}
			}
		}

		err := generationAlarmEmail(ctx, projectSite, mail)
		if err != nil {
			return err
		}

		// app任务通知 // 极光推送
		err = appTaskPush(sctx, projectSite, appPush)
		if err != nil {
			return err
		}

	}
	return nil
}

func generationAlarmEmail(ctx context.Context, projectSite models.ProjectSite, mail *[]models.Mail) error {
	project := ctx.Value("project").(ProjectAlarm)
	noticeConfig := ctx.Value("noticeConfig").(models.NoticeConfig)
	alarmMailBody := ctx.Value("alarmMailBody").(map[string]interface{})
	alarmUserMail, err := tools.GetRoleUsersMail(projectSite.ProjectID, projectSite.EnvironmentID, "notice.medicine.alarm", projectSite.ID)
	if err != nil {
		return errors.WithStack(err)
	}
	langList, html := tools.LangListHtmlSelect(noticeConfig, "order_no_automatic_new_zh.html", "order_no_automatic_new_en.html", "order_no_automatic_new.html")
	data := bson.M{
		"customer_id":     projectSite.CustomerID,
		"project_site_id": projectSite.ID,
		"project_id":      projectSite.ProjectID,
		"env_id":          projectSite.EnvironmentID,
		"projectNumber":   project.ProjectInfo.Number,
		"projectName":     project.ProjectInfo.Name,
		"envName":         project.Environments.Name,
		"siteNumber":      projectSite.Number,
		"siteName":        tools.GetProjectSiteLangName(projectSite, "zh"),
		"siteNameEn":      tools.GetProjectSiteLangName(projectSite, "en")}

	for key, v := range alarmMailBody {
		data[key] = v
	}

	for _, user := range alarmUserMail {
		var toUserMail []string
		toUserMail = append(toUserMail, user)
		*mail = append(*mail, models.Mail{
			ID:           primitive.NewObjectID(),
			Subject:      "order.no_automatic_title",
			SubjectData:  data,
			ContentData:  data,
			To:           toUserMail,
			Lang:         "en-US",
			LangList:     langList,
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
			HTML:         html,
		})
	}
	return nil
}

func appTaskPush(sctx mongo.SessionContext, projectSite models.ProjectSite, appPush *[]AppPush) error {
	// 查询该环境下分配中心的用户
	CID := projectSite.CustomerID
	PID := projectSite.ProjectID
	EID := projectSite.EnvironmentID
	SID := projectSite.ID
	siteName := projectSite.Name
	var uIds []models.UserIds
	var us []primitive.ObjectID
	userProjectEnvironments := make([]models.UserProjectEnvironment, 0)
	upeCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"project_id": PID, "env_id": EID, "app": true})
	if err != nil {
		return errors.WithStack(err)
	}
	err = upeCursor.All(nil, &userProjectEnvironments)
	if err != nil {
		return errors.WithStack(err)
	}

	projectRolePermissions := make([]models.ProjectRolePermission, 0)
	prpCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": PID})
	if err != nil {
		return errors.WithStack(err)
	}
	err = prpCursor.All(nil, &projectRolePermissions)
	if err != nil {
		return errors.WithStack(err)
	}

	userSites := make([]models.UserSite, 0)
	userSitesCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"project_id": PID, "env_id": EID, "site_id": SID})
	if err != nil {
		return errors.WithStack(err)
	}
	err = userSitesCursor.All(nil, &userSites)
	if err != nil {
		return errors.WithStack(err)
	}
	for _, upe := range userProjectEnvironments {
		for _, ur := range upe.Roles {
			for _, prp := range projectRolePermissions {
				if ur == prp.ID {
					bl := true
					//去重逻辑
					for _, u := range uIds {
						if u.UserID == upe.UserID {
							bl = false
						}
					}
					if prp.Scope == "study" && bl {
						uIds = append(uIds, models.UserIds{
							UserID: upe.UserID,
							Read:   false,
						})
						us = append(us, upe.UserID)
					} else if prp.Scope == "site" && bl {
						for _, ust := range userSites {
							if ust.UserID == upe.UserID {
								uIds = append(uIds, models.UserIds{
									UserID: upe.UserID,
									Read:   false,
								})
								us = append(us, upe.UserID)
								break
							}
						}
					}
					break
				}
			}
		}
	}
	appTaskNotice := []interface{}{}

	appTaskNotices, _ := getAppTaskNotice(projectSite.EnvironmentID)

	atn := slice.Filter(appTaskNotices, func(index int, item models.AppTaskNotice) bool {
		return item.ExpireNotice.ProjectSiteID == SID
	})
	if (uIds != nil && len(uIds) > 0) && (atn == nil || len(atn) == 0) {
		appTaskNotice = append(appTaskNotice, models.AppTaskNotice{
			ID:            primitive.NewObjectID(),
			CustomerID:    CID,
			ProjectID:     PID,
			EnvironmentID: EID,
			NoticeType:    1,
			NoticeTime:    time.Duration(time.Now().Unix()),
			ExpireNotice: models.ExpireNotice{
				WorkType:      11,
				ProjectSiteID: SID,
			},
			UserIds: uIds,
		})

		// 极光推送
		wtv := models.WorkTaskView{}
		wtv.NoticeType = 1
		wtv.WorkNoticeType = 11

		var wk models.WorkTask
		//wk.Info.WorkType = 12
		wk.ProjectID = PID
		wk.EnvironmentID = EID
		//registrationId = append(registrationId, "101d85590841177bedb")
		userRegistrationIdAppLanguages, err := getUserRegistrationId(33, us, wk, primitive.NilObjectID, SID)
		if err != nil {
			return errors.WithStack(err)
		}
		projectInfo, _, _ := getAuroraPushProjectInfo(PID, EID)
		title_zh := locales.TrWithLang("zh-CN", "app_site_alert_notification_title")
		title_en := locales.TrWithLang("en-US", "app_site_alert_notification_title")

		alertBody_zh := "[" + siteName + "]" + locales.TrWithLang("zh-CN", "app_site_alert_notification_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]"
		alertBody_en := "[" + siteName + "]" + locales.TrWithLang("en-US", "app_site_alert_notification_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]"

		//转JSON
		sJson, err := json.Marshal(wtv)
		if err != nil {
			return errors.WithStack(err)
		}

		var registrationId_zh []string
		var registrationId_en []string
		for _, ural := range userRegistrationIdAppLanguages {
			if ural.AppLanguage == "" || ural.AppLanguage == "zh-CN" {
				registrationId_zh = append(registrationId_zh, ural.RegistrationId)
			} else {
				registrationId_en = append(registrationId_en, ural.RegistrationId)
			}
		}

		*appPush = append(*appPush, AppPush{
			AlertbodyEn:      alertBody_en,
			AlertbodyZh:      alertBody_zh,
			RegistrationidEn: registrationId_en,
			RegistrationidZh: registrationId_zh,
			TitleEn:          title_en,
			TitleZh:          title_zh,
			SJson:            sJson,
		})

	}
	// 添加app任务通知
	if appTaskNotice != nil && len(appTaskNotice) > 0 {
		_, err = tools.Database.Collection("app_task_notice").InsertMany(sctx, appTaskNotice)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func generationForecastNew(ctx context.Context, projectSite models.ProjectSite, medicine models.MedicinePlanInfo, subjectDispensingAll []models.ForecastSubjectDispensing) (int, int) {
	maxSupply := 0
	minForecast := 0
	minDay := medicine.ForecastMin
	maxDay := medicine.ForecastMax
	drugConfigures := ctx.Value("drugConfigures").([]models.DrugConfigure)
	attributes := ctx.Value("attributes").([]models.Attribute)
	visitCycles := ctx.Value("visitCycles").([]models.VisitCycle)
	subjectMap := ctx.Value("subjectMap").(map[string]models.Subject)

	// 查询有环境 或者cohort下的访视计划、 受试者

	// 查询未来最低预测的量，预测最高使用的量

	/*
		计算单个受试者下 未来最低预测的量，预测最高使用的量 既有多少个访视需要发药
		for cohort
			if visit_type == baseline
				计算单个受试者下 未来最低预测的量，预测最高使用的量 既有多少个访视需要发药
				根据配置查询这个药要发到哪个访视 筛选 这个药配在哪几个访视上
			else
				获取每个受试者的发药数据获取最新的一条发过药的访视
	*/

	// 7064
	for _, attribute := range attributes {
		subjectDispensing := slice.Filter(subjectDispensingAll, func(index int, item models.ForecastSubjectDispensing) bool {
			// 状态筛选
			if item.ProjectSiteID != projectSite.ID {
				return false
			}

			if item.CohortID != attribute.CohortID {
				return false
			}

			if attribute.AttributeInfo.IsScreen {
				if item.Status == 1 {
					return false
				}
			} else {
				if item.Status == 7 {
					return false
				}
			}
			// 是否需要计算揭盲的
			if attribute.AttributeInfo.UnBlindingRestrictions {
				if item.UrgentUnblindingStatus != 0 {
					return false
				}
			}
			// 是否需要计算pv揭盲
			if attribute.AttributeInfo.PvUnBlindingRestrictions {
				if item.PvUnblindingStatus != 0 {
					return false
				}
			}
			return true
		})

		visitCycleP, ok := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
			return item.CohortID == attribute.CohortID
		})
		if !ok {
			continue
		}
		visitCycle := *visitCycleP
		// 查询药物在各个访视最大发药量

		// 获取每个访视在各个组别中发的药
		type VisitDispensingNumber struct {
			visitNumber      string
			DispensingNumber int
		}
		// 组别-访视-最大发药量、组别
		drugConfigureP, ok := slice.Find(drugConfigures, func(index int, item models.DrugConfigure) bool {
			return item.CohortID == attribute.CohortID
		})
		if !ok {
			continue
		}
		drugConfigure := *drugConfigureP
		medicineCount, _ := getGroupAndVisitGroupNew(drugConfigure, medicine)

		if visitCycle.VisitType == 0 {
			for _, subject := range subjectDispensing {
				// 以随机为基线
				if subject.Group == "" {
					subject.Group = "N/A"
				}
				if attribute.AttributeInfo.Random && subject.RegisterGroup != "" {
					subject.Group = subject.RegisterGroup
				}
				// 7064
				if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
					subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
				}

				firstTime := time.Duration(0)
				for _, resDispensing := range subject.Dispensing {
					visitCycleInfoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
						return item.Number == resDispensing.VisitInfo.Number
					})
					if ok {
						visitCycleInfo := *visitCycleInfoP
						if firstTime == 0 && resDispensing.DispensingTime != 0 && visitCycleInfo.Interval != nil && subject.RandomTime == 0 {
							firstTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfo.Interval)).Unix())
							subject.RandomTime = firstTime
							break
						}
					}

				}

				countForecastBaseLineNew(drugConfigure.Configures, medicine.MedicineName, medicineCount, subject, subject.RandomTime, visitCycle, *minDay, *maxDay, &minForecast, &maxSupply, attribute, subject.JoinTime)
			}
		} else {
			for _, dispensing := range subjectDispensing {
				// 获取最新的一次发药时间
				err := slice.SortByField(dispensing.Dispensing, "DispensingTime", "desc")
				if err != nil {
					return 0, 0
				}
				if dispensing.Dispensing[0].DispensingTime == 0 { // 没发过药
					continue
				}
				if dispensing.Group == "" {
					dispensing.Group = "N/A"
				}
				countForecastNew(medicineCount, dispensing, dispensing.Dispensing[0].DispensingTime, visitCycle, *minDay, *maxDay, &minForecast, &maxSupply)

			}
		}

	}

	return maxSupply, minForecast
}

func getGroupAndVisitGroupNew(drugConfigure models.DrugConfigure, medicine models.MedicinePlanInfo) (map[string]map[string]int, []string) {
	// 查询组别-访视-最大发药量
	medicineCount := map[string]map[string]int{}
	// 过滤访视
	for _, configure := range drugConfigure.Configures {

		for _, value := range configure.Values {
			if value.DrugName == medicine.MedicineName {
				for _, cycle := range configure.VisitCycles {
					if configure.OpenSetting == 3 {
						medicineCount[configure.Group] = map[string]int{cycle.Hex(): 0}
						continue
					}
					if medicineCount[configure.Group] != nil {
						if medicineCount[configure.Group][cycle.Hex()] < value.DispensingNumber {
							medicineCount[configure.Group][cycle.Hex()] = value.DispensingNumber //组别-访视-最大发药量
						}
					} else {
						medicineCount[configure.Group] = map[string]int{cycle.Hex(): value.DispensingNumber}
					}
				}
			}
		}
	}
	groups := []string{}
	for key := range medicineCount {
		if key != "N/A" {
			groups = append(groups, key)
		}
	}
	return medicineCount, groups
}

func countForecastNew(medicineCount map[string]map[string]int, dispensing models.ForecastSubjectDispensing, dispensingTime time.Duration, visitCycle models.VisitCycle, minDay int, maxDay int, minForecacst *int, maxSupply *int) {
	betweenHours := getHoursBetween(time.Now().Unix(), int64(dispensingTime))

	totalMin := betweenHours + (minDay * 24)
	totalMax := betweenHours + (maxDay * 24)
	baseInt := float64(0)
	for _, info := range visitCycle.Infos {
		// 当前受试者是否有这个访视，是否发过药
		_, ok := slice.Find(dispensing.Dispensing, func(index int, item models.ForecastDispensing) bool {
			return info.Number == item.VisitInfo.Number && item.Status == 1
		})
		if ok {
			Unit, Interval, PeriodMin := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)

			addTime := tools.ConvertTime(Unit, Interval, PeriodMin) + baseInt
			// 非小时制的转成小时
			if addTime <= float64(totalMin) { // 访视在最小窗口期内
				*minForecacst += medicineCount[dispensing.Group][info.ID.Hex()]
			}
			if addTime <= float64(totalMax) { // 访视在最大窗口期内
				*maxSupply += medicineCount[dispensing.Group][info.ID.Hex()]
			}

			baseInt = baseInt + tools.ConvertTime(Unit, Interval, 0)
		}
	}
}

func countForecastBaseLineNew(configure []models.DrugConfigureInfo, medicineName string, medicineCount map[string]map[string]int, dispensing models.ForecastSubjectDispensing, dispensingTime time.Duration, visitCycle models.VisitCycle, minDay int, maxDay int, minForecacst *int, maxSupply *int, attribute models.Attribute, joinTime string) {
	if dispensingTime == 0 {
		return
	}
	betweenHours := getHoursBetween(time.Now().Unix(), int64(dispensingTime))

	if !attribute.AttributeInfo.Random && joinTime != "" {
		parse, _ := time.Parse("2006-01-02", joinTime)
		betweenHours = getHoursBetween(time.Now().Unix(), parse.Unix())
	}

	var w *float64
	var h *float64
	var age *string

	_ = slice.SortByField(dispensing.Dispensing, "DispensingTime")
	for _, d := range dispensing.Dispensing {
		if d.FormulaInfo.Weight != nil {
			w = d.FormulaInfo.Weight
		}
		if d.FormulaInfo.Height != nil {
			h = d.FormulaInfo.Height
		}
		if d.FormulaInfo.Height != nil {
			age = d.FormulaInfo.Age
		}
	}

	totalMin := betweenHours + (minDay * 24)
	totalMax := betweenHours + (maxDay * 24)
	for _, info := range visitCycle.Infos {
		// 当前受试者是否有这个访视，是否发过药
		_, ok := slice.Find(dispensing.Dispensing, func(index int, item models.ForecastDispensing) bool {
			return info.Number == item.VisitInfo.Number && item.Status == 1
		})
		if ok {
			if info.Interval == nil && dispensing.Group == "N/A" {
				continue
			}
			// 非小时制的转成小时
			Unit, Interval, PeriodMin := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)
			converTimes := tools.ConvertTime(Unit, Interval, PeriodMin)
			if !info.Random && converTimes == 0 {
				continue
			}
			if converTimes <= float64(totalMin) { // 访视在最小窗口期内
				*minForecacst += medicineCount[dispensing.Group][info.ID.Hex()]
				if medicineCount[dispensing.Group][info.ID.Hex()] == 0 { // dispensingNumber == 0 则是公式计算
					forest, _ := formulaForest(configure, dispensing.Group, medicineName, info.ID, age, w, h)
					*minForecacst += forest
				}
			}
			if converTimes <= float64(totalMax) { // 访视在最大窗口期内
				*maxSupply += medicineCount[dispensing.Group][info.ID.Hex()]
				if medicineCount[dispensing.Group][info.ID.Hex()] == 0 { // dispensingNumber == 0 则是公式计算
					forest, _ := formulaForest(configure, dispensing.Group, medicineName, info.ID, age, w, h)
					*maxSupply += forest
				}
			}
		}
	}
}

func GetEnvAllSubjectDispensing(sctx mongo.SessionContext, envOID primitive.ObjectID) ([]models.ForecastSubjectDispensing, error) {

	//TODO 如果没开启自动预测 不用查

	match := bson.M{"env_id": envOID, "deleted": bson.M{"$ne": true}}
	// 排除退出 替换的受试者
	match["status"] = bson.M{"$in": bson.A{1, 3, 6, 7}}

	cursor, err := tools.Database.Collection("subject").Aggregate(sctx, mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing",
			"let":  bson.M{"subject_id": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
				bson.M{"$sort": bson.D{{"serial_number", 1}}},
			},
			"as": "dispensing",
		}}},
		{{"$project",
			bson.M{
				"_id":                      1, // 主键 ObjectId
				"customer_id":              1, // CustomerID 字段
				"project_id":               1, // ProjectID 字段
				"env_id":                   1, // EnvironmentID 字段
				"cohort_id":                1, // CohortID 字段
				"project_site_id":          1, // ProjectSiteID 字段
				"random_number":            1, // RandomNumber 字段
				"info":                     1, // Info 数组字段
				"last_group":               1, // LastGroup 字段
				"group":                    1, // Group 字段
				"status":                   1, // Status 字段
				"random_time":              1, // RandomTime 字段
				"join_time":                1, // JoinTime 字段（BSON 标签为 join_time）
				"join_time_for_time":       1, // JoinTimeForTime 字段
				"register_group":           1, // RegisterGroup 字段
				"pv_unblinding_status":     1,
				"urgent_unblinding_status": 1,
				"deleted":                  1, // Deleted 字段
				"dispensing":               1, // Dispensing 数组字段
			}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjectDispensing []models.ForecastSubjectDispensing
	err = cursor.All(nil, &subjectDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return subjectDispensing, nil
}

func generationOrder(ctx context.Context, sctx mongo.SessionContext, orderNumber string, projectSite models.ProjectSite, medicinePlanInfo []models.MedicinePlanInfo, siteMedicines []MedicineAlarm, siteOtherMedicines []MedicineAlarm, storeMedicine *[]MedicineAlarm, storeOtherMedicine *[]MedicineAlarm, subjectDispensing []models.ForecastSubjectDispensing, mail *[]models.Mail, autoSupply []int) (primitive.ObjectID, error) {
	enoughStoreDrug := true // 到达警戒 需要补充库存 切库房库存足够

	enoughSiteDrug := 0 // 到达警戒 然后不需要补充库存

	medicineOIDs := []primitive.ObjectID{}      // 总补充药物ID
	medicineOtherOIDs := []primitive.ObjectID{} // 总补充未编号药物ID

	isOtherMedicine := map[string]bool{}
	if ctx.Value("isOtherMedicine") != nil {
		isOtherMedicine = ctx.Value("isOtherMedicine").(map[string]bool)
	}

	blindMedicineName := map[string]bool{}
	if ctx.Value("blindMedicineName") != nil {
		blindMedicineName = ctx.Value("blindMedicineName").(map[string]bool)
	}

	infos := formulaAlarm(ctx, 2, projectSite, medicinePlanInfo, siteMedicines, siteOtherMedicines, subjectDispensing)
	if !infos.Generation { // 全未达到警戒值
		return primitive.ObjectID{}, nil
	}

	OtherUseKeyValue := map[string]int{}

	allSupply := false
	addOne := false
	ussPageCount := map[string]int{}

	useStoreMedicine := slice.Filter(*storeMedicine, func(index int, item MedicineAlarm) bool {
		return item.StorehouseID == projectSite.StoreHouseID[0]
	})
	useStoreOtherMedicine := slice.Filter(*storeOtherMedicine, func(index int, item MedicineAlarm) bool {
		return item.StorehouseID == projectSite.StoreHouseID[0]
	})

	medicinesPackage := []models.MedicinePackage{}

	_, blindForecastOK := slice.Find(autoSupply, func(index int, item int) bool {
		return item == 3
	})

	for _, info := range medicinePlanInfo {
		allSupply = info.SupplyMode == 1 || info.SupplyMode == 3
		addOne = info.SupplyMode == 3 || info.SupplyMode == 4
		checkInfoP, _ := slice.Find(infos.AlarmGenerationInfo, func(index int, item AlarmGenerationInfo) bool {
			return item.Name == info.MedicineName
		})
		checkInfo := *checkInfoP

		needCount := 0

		_, buffer := slice.Find(info.AutoSupplySize, func(index int, item int8) bool {
			return item == 1
		})

		_, secondSupply := slice.Find(info.AutoSupplySize, func(index int, item int8) bool {
			return item == 2
		})

		_, forecast := slice.Find(info.AutoSupplySize, func(index int, item int8) bool {
			return item == 3
		})

		if blindForecastOK && (info.ForecastMin == nil || *info.ForecastMin == 0) && (info.ForecastMax == nil || *info.ForecastMax == 0) && blindMedicineName[info.MedicineName] {
			forecast = false
		}

		if checkInfo.Warn > checkInfo.SiteCount || (info.SupplyMode == 1 || info.SupplyMode == 3) { // 全研究产品补充 需要使用最大量   单研究产品跳过
			if buffer {
				needCount = info.Buffer - checkInfo.SiteCount
			}
			if secondSupply {
				needCount = info.SecondSupply
			}
		}

		if forecast && (checkInfo.ForecastMin > checkInfo.SiteCount || (info.SupplyMode == 1 || info.SupplyMode == 3)) { // 全研究产品补充 需要使用最大量   单研究产品跳过
			if checkInfo.ForecastMax-checkInfo.SiteCount > needCount {
				needCount = checkInfo.ForecastMax - checkInfo.SiteCount
			}
		}

		if needCount == 0 { // 自动订单 药物警戒
			enoughSiteDrug++
		}

		if needCount > 0 {
			medicineOIDsMap := map[primitive.ObjectID]bool{}
			medicineOtherOIDsMap := map[primitive.ObjectID]bool{}
			enoughStoreDrugTmp := getStoreMedicine(ctx, info, useStoreMedicine, useStoreOtherMedicine, needCount, ussPageCount, &medicineOIDs, &medicineOtherOIDs, medicineOIDsMap, medicineOtherOIDsMap, &medicinesPackage)

			// 库房过滤已经使用的
			if enoughStoreDrugTmp && len(medicineOIDsMap) > 0 {
				*storeMedicine = slice.Filter(*storeMedicine, func(index int, item MedicineAlarm) bool {
					return !medicineOIDsMap[item.ID]
				})
				useStoreMedicine = slice.Filter(useStoreMedicine, func(index int, item MedicineAlarm) bool {
					return !medicineOIDsMap[item.ID]
				})
			}
			// 库房过滤已经使用的
			if enoughStoreDrugTmp && len(medicineOtherOIDsMap) > 0 {
				*storeOtherMedicine = slice.Filter(*storeOtherMedicine, func(index int, item MedicineAlarm) bool {
					if medicineOtherOIDsMap[item.ID] {
						OtherUseKeyValue[item.Name+"-"+item.Batch+"-"+item.ExpirationDate] = OtherUseKeyValue[item.Batch+"-"+item.ExpirationDate] + 1
					}
					return !medicineOtherOIDsMap[item.ID]
				})
				useStoreOtherMedicine = slice.Filter(useStoreOtherMedicine, func(index int, item MedicineAlarm) bool {
					return !medicineOtherOIDsMap[item.ID]
				})
			}
			if !enoughStoreDrugTmp {
				enoughStoreDrug = false
			}

		}

	}

	if enoughSiteDrug == len(medicinePlanInfo) { // 自动订单 药物警戒
		err := AlarmOrderMailNew(ctx, sctx, projectSite, mail)
		if err != nil {
			return primitive.ObjectID{}, err

		}
		return primitive.ObjectID{}, nil
	}

	if addOne { // 研究产品+1
		tmpMedicineOIDsMap := map[string][]primitive.ObjectID{}
		medicineOIDsMap := map[primitive.ObjectID]bool{}
		medicineOtherOIDsMap := map[primitive.ObjectID]bool{}

		for _, info := range medicinePlanInfo {
			tmpMedicineOIDs := []primitive.ObjectID{}
			enoughStoreDrug = getStoreMedicine(ctx, info, useStoreMedicine, useStoreOtherMedicine, 1, ussPageCount, &tmpMedicineOIDs, &tmpMedicineOIDs, medicineOIDsMap, medicineOtherOIDsMap, &medicinesPackage)
			if enoughStoreDrug {
				tmpMedicineOIDsMap[info.MedicineName] = tmpMedicineOIDs
			}
		}

		if len(tmpMedicineOIDsMap) != 0 {

			// 随机一个药物名称
			oneMedicine := []string{}
			for key, _ := range tmpMedicineOIDsMap {
				oneMedicine = append(oneMedicine, key)
			}
			random := rand.Intn(len(oneMedicine))
			name := oneMedicine[random]

			// 选择一个写入订单 过滤仓库数据
			if isOtherMedicine[name] {
				medicineOtherOIDs = append(medicineOtherOIDs, tmpMedicineOIDsMap[name]...)
				*storeOtherMedicine = slice.Filter(*storeOtherMedicine, func(index int, item MedicineAlarm) bool {
					//
					if medicineOtherOIDsMap[item.ID] {
						OtherUseKeyValue[item.Name+"-"+item.Batch+"-"+item.ExpirationDate] = OtherUseKeyValue[item.Batch+"-"+item.ExpirationDate] + 1
					}

					return !medicineOtherOIDsMap[item.ID]
				})
			} else {
				medicineOIDs = append(medicineOIDs, tmpMedicineOIDsMap[name]...)

				*storeMedicine = slice.Filter(*storeMedicine, func(index int, item MedicineAlarm) bool {
					return !medicineOIDsMap[item.ID]
				})

			}
		} else { // 补充加一不够
			enoughStoreDrug = false
		}

		//
	}

	if !enoughStoreDrug {
		// 订单库存不够  通知
		err := OrderErrorMail(ctx, sctx, projectSite, mail)
		if err != nil {
			return primitive.ObjectID{}, err
		}
		if allSupply || (len(medicineOIDs) == 0 || len(medicineOtherOIDs) == 0) { // 全研究   || 所有研究产品都不够
			return primitive.ObjectID{}, nil
		}
	}

	orderID := primitive.NilObjectID
	var err error
	if len(medicineOIDs) > 0 || len(medicineOtherOIDs) > 0 { // 生成订单 推送订单邮件
		orderID, err = SaveOrder(ctx, sctx, projectSite, orderNumber, medicineOIDs, medicineOtherOIDs, medicinesPackage, mail, OtherUseKeyValue)
		if err != nil {
			return primitive.ObjectID{}, err
		}
	}
	return orderID, nil
}

func SaveOrder(ctx context.Context, sctx mongo.SessionContext, projectSite models.ProjectSite, orderNumber string, medicineID, otherMedicineID []primitive.ObjectID, medicinesPackage []models.MedicinePackage, mail *[]models.Mail, OtherUseKeyValue map[string]int) (primitive.ObjectID, error) {

	project := ctx.Value("project").(ProjectAlarm)
	noticeConfig := ctx.Value("noticeConfig").(models.NoticeConfig)
	packageIsOpen := ctx.Value("packageIsOpen").(bool)
	orderMailBody := ctx.Value("orderMailBody").(map[string]interface{})
	OrderSuccessState := ctx.Value("OrderSuccessState").(bool)
	projectStores := ctx.Value("projectStores").([]models.ProjectStorehouseInfo)
	projectStore, _ := slice.Find(projectStores, func(index int, item models.ProjectStorehouseInfo) bool {
		return item.ID == projectSite.StoreHouseID[0]
	})

	contacts := ""
	phone := ""
	email := ""
	address := ""
	if len(projectSite.ContactGroup) != 0 {
		for _, v := range projectSite.ContactGroup {
			if v.IsDefault == 1 {
				contacts = v.Contacts
				phone = v.Phone
				email = v.Email
				address = v.Address
			}
		}
	} else {
		contacts = projectSite.Contacts
		phone = projectSite.Phone
		email = projectSite.Email
		address = projectSite.Address
	}

	order := models.MedicineOrder{
		ID:                primitive.NewObjectID(),
		CustomerID:        projectSite.CustomerID,
		ProjectID:         projectSite.ProjectID,
		EnvironmentID:     projectSite.EnvironmentID,
		SendID:            projectSite.StoreHouseID[0],
		ReceiveID:         projectSite.ID,
		MedicinesPackage:  medicinesPackage,
		Status:            1,
		SortIndex:         10,
		Mode:              1,
		Medicines:         medicineID,
		OtherMedicinesNew: otherMedicineID,
		//OtherMedicines:   otherMedicine,
		OrderNumber: orderNumber,
		Contacts:    contacts,
		Phone:       phone,
		Email:       email,
		Address:     address,
		Type:        1,
		Meta: models.Meta{
			CreatedAt: time.Duration(time.Now().Unix()),
			UpdatedAt: time.Duration(time.Now().Unix()),
		},
	}

	// 插入订单
	result, err := tools.Database.Collection("medicine_order").InsertOne(sctx, order)
	if err != nil {
		return primitive.NilObjectID, errors.WithStack(err)
	}
	// 插入订单轨迹 研究产品轨迹
	err = insertOrderHistoryNew(ctx, sctx, order, OtherUseKeyValue)
	if err != nil {
		return primitive.NilObjectID, errors.WithStack(err)
	}
	//创建待运送的订单任务
	permissions := []string{"operation.supply.shipment.send", "operation.supply.shipment.lose", "operation.supply.shipment.receive", "operation.supply.shipment.close"}
	var siteOrStoreIDs = []primitive.ObjectID{order.SendID, order.ReceiveID}
	userIds, err := tools.GetPermissionUserIds(sctx, permissions, order.ProjectID, order.EnvironmentID, siteOrStoreIDs...)
	if err != nil {
		return primitive.NilObjectID, errors.WithStack(err)
	}
	if len(userIds) > 0 {
		createWorkTask := models.WorkTask{
			ID:            primitive.NewObjectID(),
			CustomerID:    order.CustomerID,
			ProjectID:     order.ProjectID,
			EnvironmentID: order.EnvironmentID,
			//CohortID:      order.CohortID,
			UserIDs: userIds,
			Info: models.WorkTaskInfo{
				WorkType:        6,
				Status:          0,
				CreatedTime:     time.Duration(time.Now().Unix()),
				Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
				MedicineIDs:     []primitive.ObjectID{},
				MedicineOrderID: order.ID,
				DispensingID:    primitive.NilObjectID,
			},
		}
		_, err = tools.Database.Collection("work_task").InsertOne(sctx, createWorkTask)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
	}

	medicineFilter := bson.M{"_id": bson.M{"$in": medicineID}}
	update := bson.M{
		"$set": bson.M{
			"status":        2,
			"order_id":      result.InsertedID,
			"site_id":       projectSite.ID,
			"storehouse_id": nil,
		},
	}

	_, err = tools.Database.Collection("medicine").UpdateMany(sctx, medicineFilter, update)
	if err != nil {
		return primitive.NilObjectID, errors.WithStack(err)
	}
	otherMedicineFilter := bson.M{"_id": bson.M{"$in": otherMedicineID}}
	_, err = tools.Database.Collection("medicine_others").UpdateMany(sctx, otherMedicineFilter, update)
	if err != nil {
		return primitive.NilObjectID, errors.WithStack(err)
	}

	// 推送邮件

	if OrderSuccessState {
		userMail, err := tools.GetRoleUsersMailWithRole(projectSite.ProjectID, projectSite.EnvironmentID, "notice.medicine.order", siteOrStoreIDs...)

		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		langList, html := tools.LangListHtmlSelect(noticeConfig, "medicine_order_new_zh.html", "medicine_order_new_en.html", "medicine_order_new_zh_en.html")

		medicineData, blindMedicineData, err := getMedicineDataForMail(medicineID, otherMedicineID, projectSite.EnvironmentID, packageIsOpen)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}

		contentData := bson.M{
			"projectNumber": project.ProjectInfo.Number,
			"projectName":   project.ProjectInfo.Name,
			"envName":       project.Environments.Name,
			"destination":   fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "zh")),
			"destinationEn": fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "en")),
			"start":         projectStore.Storehouse.Name,
			"startEn":       projectStore.Storehouse.Name,
			"orderNumber":   orderNumber,
			"contacts":      contacts,
			"phone":         phone,
			"email":         email,
			"address":       address,
			"sendCompany":   projectStore.Storehouse.Name,
			"results":       medicineData,
			"packageIsOpen": packageIsOpen,

			//"other":       otherMedicine
		}

		blindContentData := bson.M{
			"projectNumber": project.ProjectInfo.Number,
			"projectName":   project.ProjectInfo.Name,
			"envName":       project.Environments.Name,
			"destination":   fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "zh")),
			"destinationEn": fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "en")),
			"start":         projectStore.Storehouse.Name,
			"startEn":       projectStore.Storehouse.Name,
			"orderNumber":   orderNumber,
			"contacts":      contacts,
			"phone":         phone,
			"email":         email,
			"address":       address,
			"sendCompany":   projectStore.Storehouse.Name,
			"results":       blindMedicineData,
			"packageIsOpen": packageIsOpen,
		}
		for key, v := range orderMailBody {
			contentData[key] = v
			blindContentData[key] = v
		}
		for _, userEmail := range userMail {
			sendContendData := contentData
			if userEmail.IsBlind {
				sendContendData = blindContentData
			}
			var toUserMail []string
			toUserMail = append(toUserMail, userEmail.Email)
			*mail = append(*mail, models.Mail{
				ID:      primitive.NewObjectID(),
				Subject: "order.automatic_success_title",
				SubjectData: bson.M{
					"customer_id":     projectSite.CustomerID,
					"project_id":      projectSite.ProjectID,
					"env_id":          projectSite.EnvironmentID,
					"project_site_id": projectSite.ID,
					"orderNumber":     orderNumber,
					"projectNumber":   project.ProjectInfo.Number,
					"projectName":     project.ProjectInfo.Name,
					"envName":         project.Environments.Name,
					"destination":     fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "zh")),
					"destinationEn":   fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "en")),
					//
					//"siteNumber":    info["siteNumber"],
					//"siteName":      info["siteName"],
				},
				Content:      "order.automatic_success",
				ContentData:  sendContendData,
				To:           toUserMail,
				Lang:         "en-US",
				LangList:     langList,
				Status:       0,
				CreatedTime:  time.Duration(time.Now().Unix()),
				ExpectedTime: time.Duration(time.Now().Unix()),
				SendTime:     time.Duration(time.Now().Unix()),
				HTML:         html,
			})
		}
	}
	return order.ID, nil
}

func AlarmOrderMailNew(ctx context.Context, sctx mongo.SessionContext, projectSite models.ProjectSite, mail *[]models.Mail) error {
	project := ctx.Value("project").(ProjectAlarm)
	noticeConfig := ctx.Value("noticeConfig").(models.NoticeConfig)
	alarmOrderState := ctx.Value("alarmOrderState").(bool)
	orderMailBody := ctx.Value("orderMailBody").(map[string]interface{})
	if !alarmOrderState {
		return nil
	}

	if project.ProjectInfo.OrderCheck == 2 {
		if orderCheckSendToday(ctx, projectSite, "order.automatic_alarm_title") {
			return nil
		} else {
			err := insertAlarmRecord(sctx, projectSite.EnvironmentID, projectSite.ID, "order.automatic_alarm_title")
			if err != nil {
				return err
			}
		}
	}
	userMail, err := tools.GetRoleUsersMailWithRole(projectSite.ProjectID, projectSite.EnvironmentID, "notice.medicine.order", projectSite.ID)

	if err != nil {
		return errors.WithStack(err)
	}

	langList, html := tools.LangListHtmlSelect(noticeConfig, "medicine_alarm_zh.html", "medicine_alarm_en.html", "medicine_alarm.html")

	for _, userEmail := range userMail {
		subjectData := bson.M{
			"projectNumber":   project.ProjectInfo.Number,
			"envName":         project.Environments.Name,
			"customer_id":     projectSite.CustomerID,
			"project_id":      projectSite.ProjectID,
			"env_id":          projectSite.EnvironmentID,
			"project_site_id": projectSite.ID,
		}
		contentData := bson.M{
			"projectNumber": project.ProjectInfo.Number,
			"envName":       project.Environments.Name,
			"projectName":   project.ProjectInfo.Name,
			"siteNumber":    projectSite.Number,
			"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
			"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
		}
		for key, v := range orderMailBody {
			contentData[key] = v
		}

		var toUserMail []string
		toUserMail = append(toUserMail, userEmail.Email)
		*mail = append(*mail, models.Mail{
			ID:           primitive.NewObjectID(),
			Subject:      "order.automatic_alarm_title",
			SubjectData:  subjectData,
			ContentData:  contentData,
			To:           toUserMail,
			Lang:         "en-US",
			LangList:     langList,
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
			HTML:         html,
		})
	}

	return nil
}

func insertAlarmRecord(sctx mongo.SessionContext, envOID, SiteOID primitive.ObjectID, subject string) error {
	doc := []interface{}{}
	doc = append(doc, models.AlarmMedicineRecords{
		ID:            primitive.NewObjectID(),
		EnvID:         envOID,
		ProjectSiteID: SiteOID,
		Subject:       subject,
		CreatedTime:   time.Duration(time.Now().Unix()),
	})
	_, err := tools.Database.Collection("alarm_medicine_records").InsertMany(sctx, doc)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func OrderErrorMail(ctx context.Context, sctx mongo.SessionContext, projectSite models.ProjectSite, mail *[]models.Mail) error {
	project := ctx.Value("project").(ProjectAlarm)
	noticeConfig := ctx.Value("noticeConfig").(models.NoticeConfig)
	OrderErrorState := ctx.Value("OrderErrorState").(bool)
	orderMailBody := ctx.Value("orderMailBody").(map[string]interface{})
	if !OrderErrorState {
		return nil
	}
	projectStores := ctx.Value("projectStores").([]models.ProjectStorehouseInfo)
	projectStore, _ := slice.Find(projectStores, func(index int, item models.ProjectStorehouseInfo) bool {
		return item.ID == projectSite.StoreHouseID[0]
	})
	if project.ProjectInfo.OrderCheck == 2 {
		if orderCheckSendToday(ctx, projectSite, "order.automatic_error_title") {
			return nil
		} else {
			err := insertAlarmRecord(sctx, projectSite.EnvironmentID, projectSite.ID, "order.automatic_error_title")
			if err != nil {
				return err
			}
		}
	}

	var siteOrStoreIDs = []primitive.ObjectID{projectSite.ID}

	userMail, err := tools.GetRoleUsersMailWithRole(projectSite.ProjectID, projectSite.EnvironmentID, "notice.medicine.order", siteOrStoreIDs...)

	if err != nil {
		return errors.WithStack(err)
	}

	contentData := bson.M{
		"project_id":      project.ID,
		"env_id":          projectSite.EnvironmentID,
		"project_site_id": projectSite.ID,
		"projectNumber":   project.ProjectInfo.Number,
		"projectName":     project.ProjectInfo.Name,
		"envName":         project.Environments.Name,
		"destination":     fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "zh")),
		"destinationEn":   fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "en")),
		// TODO
		"start":   projectStore.Storehouse.Name,
		"startEn": projectStore.Storehouse.Name,
	}
	for key, v := range orderMailBody {
		contentData[key] = v
	}

	langList, html := tools.LangListHtmlSelect(noticeConfig, "order_automatic_error_new_zh.html", "order_automatic_error_new_en.html", "order_automatic_error_new.html")
	for _, userEmail := range userMail {
		var toUserMail []string
		toUserMail = append(toUserMail, userEmail.Email)
		*mail = append(*mail, models.Mail{
			ID:      primitive.NewObjectID(),
			Subject: "order.automatic_error_title",
			SubjectData: bson.M{
				"customer_id":     project.CustomerID,
				"project_id":      project.ID,
				"env_id":          projectSite.EnvironmentID,
				"project_site_id": projectSite.ID,
				"projectNumber":   project.ProjectInfo.Number,
				"projectName":     project.ProjectInfo.Name,
				"envName":         project.Environments.Name,
				"destination":     fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "zh")),
				"destinationEn":   fmt.Sprintf("%s-%s", projectSite.Number, tools.GetProjectSiteLangName(projectSite, "en")),
			},
			//Content:      "order.automatic_error_dual",
			ContentData:  contentData,
			To:           toUserMail,
			Lang:         "en-US",
			LangList:     langList,
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
			HTML:         html,
		})
	}
	return nil
}

func orderCheckSendToday(ctx context.Context, projectSite models.ProjectSite, subject string) bool {
	sign := false
	project := ctx.Value("project").(ProjectAlarm)
	alarmMedicineRecords := ctx.Value("alarmMedicineRecords").([]models.AlarmMedicineRecords)
	for _, record := range alarmMedicineRecords {
		if record.ProjectSiteID == projectSite.ID && record.Subject == subject {

			f, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")

			// 将小数小时转换为 Duration
			// 1. 计算小时部分
			hours := int(f)
			// 2. 计算分钟部分（0.75 * 60 = 45 分钟）
			minutes := int((f - float64(hours)) * 60)
			// 3. 组合成 Duration
			offsetDuration := time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute
			recordDate := time.Unix(int64(record.CreatedTime), 0).UTC().Add(offsetDuration).Format("2006-01-02")
			//recordDate := time.Unix(int64(record.CreatedTime), 0).UTC().Add(time.Hour * time.Duration(project.ProjectInfo.TimeZone)).Format("2006-01-02")
			//date := time.Now().UTC().Add(time.Hour * time.Duration(project.ProjectInfo.TimeZone)).Format("2006-01-02")
			date := time.Unix(int64(time.Now().Unix()), 0).UTC().Add(offsetDuration).Format("2006-01-02")
			if recordDate == date {
				sign = true
			}
		}
	}
	return sign
}

func getStoreMedicine(ctx context.Context, info models.MedicinePlanInfo, storeMedicine, storeOtherMedicine []MedicineAlarm, needCount int, ussPageCount map[string]int, medicineOIDs, medicineOtherOIDs *[]primitive.ObjectID, medicineOIDsMap, medicineOtherOIDsMap map[primitive.ObjectID]bool, medicinePackage *[]models.MedicinePackage) bool {
	now := ctx.Value("now").(time.Time)
	project := ctx.Value("project").(ProjectAlarm)

	var packageIsOpen bool
	var packageAllCount map[string]int
	var packageConfigs map[string]int
	var mixPackageConfig map[string][]string
	packageIsOpen = ctx.Value("packageIsOpen").(bool)
	if ctx.Value("packageAllCount") != nil {
		packageAllCount = ctx.Value("packageAllCount").(map[string]int)
	}
	if ctx.Value("packageConfigs") != nil {
		packageConfigs = ctx.Value("packageConfigs").(map[string]int)
	}
	if ctx.Value("mixPackageConfig") != nil {
		mixPackageConfig = ctx.Value("mixPackageConfig").(map[string][]string)
	}
	isOtherMedicine := map[string]bool{}
	if ctx.Value("isOtherMedicine") != nil {
		isOtherMedicine = ctx.Value("isOtherMedicine").(map[string]bool)
	}
	isPageNumber, ok := packageConfigs[info.MedicineName]
	needPageCount := packageIsOpen && ok && isPageNumber != 0

	f, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
	hours := int(f)
	minutes := int((f - float64(hours)) * 60)
	loc := time.FixedZone("CustomZone", hours*3600+minutes*60)

	// 2. 计算最终时间
	unDistributionDate := now.In(loc).
		Add(time.Hour * 24 * time.Duration(info.UnDistributionDate)).
		Format("2006-01-02")

	//unDistributionDate := now.UTC().Add(time.Hour * time.Duration(project.ProjectInfo.TimeZone)).Add(time.Hour * 24 * time.Duration(info.UnDistributionDate)).Format("2006-01-02")

	if needPageCount { // 需要包装 计算包装数量 以及整包配送
		if isOtherMedicine[info.MedicineName] {
			tmpMedicineIDs, enough := generationPageMedicine(ctx, info, true, needCount, unDistributionDate, isPageNumber, packageAllCount, mixPackageConfig, ussPageCount, storeOtherMedicine)
			if !enough {
				return false
			}

			*medicineOtherOIDs = append(*medicineOtherOIDs, tmpMedicineIDs...)
			for _, ID := range tmpMedicineIDs {
				medicineOtherOIDsMap[ID] = true

			}
			// 包装订单生成 需要 包装配置字段
			mixPackage, _ := mixPackageConfig[info.MedicineName]
			for _, tmpName := range mixPackage {
				_, ok = slice.Find(*medicinePackage, func(index int, item models.MedicinePackage) bool {
					return item.Name == tmpName
				})
				if !ok {
					*medicinePackage = append(*medicinePackage, models.MedicinePackage{
						Name:          tmpName,
						PackageMethod: true,
						PackageNumber: packageAllCount[info.MedicineName],
					})
				}
			}

		} else {
			IDs, enough := generationPageMedicine(ctx, info, false, needCount, unDistributionDate, isPageNumber, packageAllCount, mixPackageConfig, ussPageCount, storeMedicine)
			if !enough {
				return false
			}
			*medicineOIDs = append(*medicineOIDs, IDs...)
			for _, ID := range IDs {
				medicineOIDsMap[ID] = true
			}

		}

	} else {
		// 生成订单
		tmpMedicineIDs := []primitive.ObjectID{}
		useMedicine := storeMedicine
		if isOtherMedicine[info.MedicineName] {
			useMedicine = storeOtherMedicine
		}
		for _, medicine := range useMedicine {
			if medicine.Name == info.MedicineName && (medicine.ExpirationDate > unDistributionDate || medicine.ExpirationDate == "") {
				tmpMedicineIDs = append(tmpMedicineIDs, medicine.ID)
				if !isOtherMedicine[info.MedicineName] {
					medicineOIDsMap[medicine.ID] = true
				} else {
					medicineOtherOIDsMap[medicine.ID] = true
				}
			}
			if needCount == len(tmpMedicineIDs) {
				break
			}
		}

		if needCount != len(tmpMedicineIDs) { // 库房库存不够
			return false
		}
		// 写入订单
		if isOtherMedicine[info.MedicineName] {
			*medicineOtherOIDs = append(*medicineOtherOIDs, tmpMedicineIDs...)
		} else {
			*medicineOIDs = append(*medicineOIDs, tmpMedicineIDs...)
		}
	}
	return true
}

func generationPageMedicine(ctx context.Context, info models.MedicinePlanInfo, isOtherMedicine bool, needCount int, unDistributionDate string, isPageNumber int, packageAllCount map[string]int, mixPackageConfig map[string][]string, usePageCount map[string]int, storeOrOtherMedicine []MedicineAlarm) ([]primitive.ObjectID, bool) {

	IDs := []primitive.ObjectID{}

	// 当前药物名称 所需要的
	needCount = int(math.Ceil(float64(needCount) / float64(isPageNumber)))

	// 如果是混包的 检查是否上次已经拼够足够包装
	if needCount <= usePageCount[info.MedicineName] {
		return IDs, true
	}

	// 补充包装剩余的
	needCount = needCount - usePageCount[info.MedicineName]
	useMedicine := storeOrOtherMedicine
	groupPage := slice.GroupWith(useMedicine, func(item MedicineAlarm) string {
		return item.ExpirationDate + item.PackageSerialNumber
	})

	packageNumbers := []string{}
	for key, _ := range groupPage {
		packageNumbers = append(packageNumbers, key)
	}
	sort.Strings(packageNumbers)

	IDsMap := map[primitive.ObjectID]bool{}

	selectCount := 0

	for _, number := range packageNumbers {
		if len(groupPage[number]) != packageAllCount[info.MedicineName] {
			continue
		}
		if selectCount == needCount {
			break
		}
		isSelect := false
		for _, medicine := range groupPage[number] {
			//  混包需要过滤对应不发放日期的
			if medicine.ExpirationDate > unDistributionDate || medicine.ExpirationDate == "" {
				isSelect = true
				IDs = append(IDs, medicine.ID)
				IDsMap[medicine.ID] = true
			}

		}
		if isSelect {
			selectCount++
		}
	}

	if selectCount != needCount { // 不够 返回空
		return IDs, false
	}

	// 将混包置为更新的数量
	for _, mixName := range mixPackageConfig[info.MedicineName] {
		usePageCount[mixName] = usePageCount[info.MedicineName] + needCount
	}
	return IDs, true
}

func AutoOrderMailCustomContentStates(project ProjectAlarm) (models.NoticeConfig, bool, bool, bool, bool, map[string]interface{}, map[string]interface{}, error) {
	// 查询通知配置 是否需要邮件通知
	alarm := false
	orderAlarm := false
	OrderErrorState := false
	orderSend := false

	alarmMailBody := make(map[string]interface{})
	orderMailBody := make(map[string]interface{})
	var noticeBase models.NoticeConfig

	var noticeConfig []models.NoticeConfig
	cursor, err := tools.Database.Collection("notice_config").Find(nil, bson.M{"env_id": project.Environments.ID})
	if err != nil {
		return noticeBase, alarm, orderAlarm, OrderErrorState, orderSend, alarmMailBody, orderMailBody, errors.WithStack(err)

	}

	err = cursor.All(nil, &noticeConfig)
	if err != nil {
		return noticeBase, alarm, orderAlarm, OrderErrorState, orderSend, alarmMailBody, orderMailBody, errors.WithStack(err)

	}

	for _, notice := range noticeConfig {
		if notice.Key == "notice.basic.settings" {
			noticeBase = notice
		}
		for _, item := range notice.State {
			if item == "order.no_automatic_title" {
				alarm = true
			}
			if item == "order.automatic_alarm_title" {
				orderAlarm = true
			}
			if item == "order.automatic_success_title" {
				orderSend = true
			}
			if item == "order.automatic_error_title" {
				OrderErrorState = true
			}
		}

		if notice.Key == "notice.medicine.alarm" {
			alarmMailBody = AutoMailBodyContent(notice, project, "notice.medicine.alarm")
		}
		if notice.Key == "notice.medicine.order" {
			orderMailBody = AutoMailBodyContent(notice, project, "notice.medicine.order")
		}
	}
	return noticeBase, alarm, orderAlarm, OrderErrorState, orderSend, alarmMailBody, orderMailBody, errors.WithStack(err)

}

func AutoMailBodyContent(notice models.NoticeConfig, project ProjectAlarm, emailType string) map[string]interface{} {
	bodyShow := make(map[string]interface{})
	noticeMap := make(map[string]bool)
	for _, item := range notice.FieldsConfig {
		noticeMap[item] = true
	}

	for key, v := range tools.BodyContent {
		if noticeMap[v] {
			bodyShow[key] = noticeMap[v]
		} else {
			bodyShow[key] = false
		}
	}
	bodyShow["envNameShow"] = true
	return bodyShow
}

func insertOrderHistoryNew(ctx context.Context, sctx mongo.SessionContext, order models.MedicineOrder, OtherUseKeyValue map[string]int) error {
	var medicineOtherKeys []models.MedicineOtherKey
	if ctx.Value("medicineOtherKey") != nil {
		medicineOtherKeys = ctx.Value("medicineOtherKey").([]models.MedicineOtherKey)
	}
	histories := []interface{}{}

	for key, value := range OtherUseKeyValue {
		medicineOtherKeyP, ok := slice.Find(medicineOtherKeys, func(index int, item models.MedicineOtherKey) bool {
			tmpKey := item.Name + "-" + item.Batch + "-" + item.ExpireDate
			return item.SiteID == order.ReceiveID && tmpKey == key
		})
		ID := primitive.NewObjectID()
		medicineOtherKey := models.MedicineOtherKey{}
		if ok {
			medicineOtherKey = *medicineOtherKeyP
			ID = medicineOtherKey.ID
		}

		// 写入轨迹
		histories = append(histories, models.History{
			Key:  "history.medicine.otherConfirmed",
			OID:  ID,
			Data: bson.M{"name": medicineOtherKey.Name, "batch": medicineOtherKey.Batch, "expireDate": medicineOtherKey.ExpireDate, "count": value},
			Time: time.Duration(time.Now().Unix()),
			User: "System（Automatic Order）",
			UID:  primitive.NilObjectID,
		})
	}
	for _, medicineID := range order.Medicines {
		history := models.History{
			Key:  "history.medicine.confirmedNew",
			Data: bson.M{"orderNumber": order.OrderNumber},
			OID:  medicineID,
			Time: time.Duration(time.Now().Unix()),
			User: "System（Automatic Order）",
			UID:  primitive.NilObjectID,
		}
		histories = append(histories, history)
	}
	histories = append(histories, models.History{
		Key:  "history.order.confrim-new",
		OID:  order.ID,
		Time: time.Duration(time.Now().Unix()),
		Data: map[string]interface{}{"orderNumber": order.OrderNumber},
		User: "System（Automatic Order）",
		UID:  primitive.NilObjectID,
	})
	_, err := tools.Database.Collection("history").InsertMany(sctx, histories)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}
