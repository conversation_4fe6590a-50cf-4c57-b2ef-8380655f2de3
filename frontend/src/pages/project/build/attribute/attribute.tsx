import styled from "@emotion/styled";
import React, { useCallback, useEffect } from "react";
import { <PERSON><PERSON>, Button, Checkbox, Col, Divider, Form, Input, InputNumber, message, notification, Radio, Row, Select, Space, Spin, Switch, Table, Tooltip, } from "antd";
import { CheckCircleTwoTone, MinusCircleFilled, PlusOutlined, QuestionCircleFilled, CloseCircleFilled, } from "@ant-design/icons";
import { HistoryList } from "../../../common/history-list";
import { permissions, permissionsCohort } from "../../../../tools/permission";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { useGlobal } from "../../../../context/global";
import { useFetch } from "../../../../hooks/request";
import {
  getProjectAttribute, getProjectAttributeConnectAli, getRandomization, getRandomizationType, updateProjectAttribute,
} from "../../../../api/randomization";
import { Title as CustomTitle } from "components/title";
import { getSubjectCount } from "api/subject";
import { inRandomIsolation } from "../../../../utils/in_random_isolation";
import _ from "lodash";
import { CodeRule } from "./code-rule";
import { getBarcodeConfig, saveBarcodeConfig } from "../../../../api/barcode";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { AuthButton } from "../../../common/auth-wrap";

export const Attribute = (props: any) => {
  const g = useGlobal();
  const auth = useAuth();
  const intl = useTranslation();
  const { formatMessage } = intl;
  const [oldData, setOldData] = useSafeState<any>(null);
  const [data, setData] = useSafeState<any>(null);
  const [prefix, setPrefix] = useSafeState<any>(false);
  const [blind, setBlind] = useSafeState<any>(false);
  // const [sitePrefixBool, setSitePrefixBool] = useSafeState<any>(false);
  const [randomBool, setRandomBool] = useSafeState<any>(false);
  const [randomControl, setRandomControl] = useSafeState<any>(false);
  const [randomControlRule, setRandomControlRule] = useSafeState<any>(null);
  const [subjectNumberRule, setSubjectNumberRule] = useSafeState<any>(1);
  const [replaceRule, setReplaceRule] = useSafeState<any>(0);
  // const [otherPrefixBool, setOtherPrefixBool] = useSafeState<any>(false);
  const [dispensing, setDispensing] = useSafeState<any>(false);
  const [dtpRuleValue, setDtpRuleValue] = useSafeState<any>(false);

  const [segment, setSegment] = useSafeState<any>(false);
  const [oldGroupsData, setOldGroupsData] = useSafeState<any>(null);
  const [groupsData, setGroupsData] = useSafeState<any>(null);
  const [groupsInfo, setGroupsInfo] = useSafeState<any>(null);
  const [maxGroupsCount, setMaxGroupsCount] = useSafeState<any>(0);
  const [send, setSend] = useSafeState<any>(true);
  const [show, setShow] = useSafeState<any>(false);
  const [replace, setReplace] = useSafeState<any>(true);
  const [registerGroup, setRegisterGroup] = useSafeState<any>(false);
  const [minimizeCalc, setMinimizeCalc] = useSafeState<any>(false);
  const [IPInheritance, setIPInheritance] = useSafeState<any>(false);
  const [edit, setEdit] = useSafeState<any>(true);
  const [blindRule, setBlindRule] = useSafeState<any>(0);

  const [randomizationType, setRandomizationType] = useSafeState<any>(0);
  const [codeRuleConfig, setCodeRuleConfig] = useSafeState<any>({ codeRule: 0, codeConfigInit: false })
  const [codeRuleConfigId, setCodeRuleConfigId] = useSafeState<any>(null)
  const cohort = auth.env.cohorts.find((item: any) => item.id === props.cohortId)
  const [form] = Form.useForm();
  const [traceabilityCodeShow, setTraceabilityCodeShow] = useSafeState(false);
  const projectId = auth.project.id;
  const projectType = auth.project.info.type;
  const envId = auth.env ? auth.env.id : null;
  const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
  const projectStatus = auth.project.status ? auth.project.status : 0;
  const customerId = auth.customerId;
  const connectEdc = auth.project.info.connect_edc;
  const pushMode = auth.project.info.push_mode;
  const history_ref: any = React.useRef();
  const researchAttribute = auth.project.info.research_attribute
    ? auth.project.info.research_attribute
    : 0;
  const [disabled, setDisabled] = useSafeState(true);
  const [subjectNumberDisabled, setSubjectNumberDisabled] = useSafeState(true);
  const [refresh, setRefresh] = useSafeState(0);
  const [isRandomSequenceNumber, setIsRandomSequenceNumber] = useSafeState(false);
  const {
    runAsync: getProjectAttributeRun,
    loading: getProjectAttributeLoading,
  } = useFetch(getProjectAttribute, { manual: true });

  const {
    runAsync: getRandomizationTypeRun,
    loading: getRandomizationTypeLoading,
  } = useFetch(getRandomizationType, { manual: true });


  const { runAsync: getSubjectCountRun, loading: getSubjectCountLoading } =
    useFetch(getSubjectCount, { manual: true });
  const {
    runAsync: updateProjectAttributeRun,
    loading: updateProjectAttributeLoading,
  } = useFetch(
    updateProjectAttribute,
    {
      manual: true,
      onError: (err: any) => {
        form.resetFields();
        setRefresh(refresh + 1);
        setEdit(true);
        setDisabled(true);
        err.json().then((data: any) =>
          notification.open({
            message: (
              <div
                style={{
                  fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                  fontSize: "14px",
                }}
              >
                <CloseCircleFilled
                  style={{
                    color: "#F96964",
                    paddingRight: "8px",
                  }}
                />
                {data.msg}
              </div>
            ),
            // description: (
            //     <div
            //         style={{
            //             paddingLeft: "20px",
            //             color: "#646566",
            //         }}
            //     >
            //         {data.msg}
            //     </div>
            // ),
            duration: 5,
            placement: "top",
            style: {
              width: "720px",
              // height: "88px",
              background: "#FEF0EF",
              borderRadius: "4px",
            },
          })
        );
      },
    }
  );
  const { runAsync: getRandomizationRun, loading: getRandomizationLoading } =
    useFetch(getRandomization, { manual: true });

  const {
    runAsync: getProjectAttributeConnectAliRun,
    loading: getProjectAttributeConnectAliLoading,
  } = useFetch(getProjectAttributeConnectAli, { manual: true });

  const get_project_attributes = useCallback(() => {
    setDisabled(
      !(
        permissionsCohort(
          auth.project.permissions,
          "operation.build.attribute.edit",
          props.cohort?.status
        ) && !lockConfig
      ) ||
      projectStatus === 2 ||
      edit
    );
    getCodeRule()

    getRandomizationTypeRun({
      projectId,
      env: envId,
      cohort: props.cohortId ? props.cohortId : null,
      customer: customerId,
    }).then((result: any) => {
      let da: any = result.data;
      setRandomizationType(da);
    });
    getProjectAttributeRun({
      projectId,
      env: envId,
      cohort: props.cohortId ? props.cohortId : null,
      customer: customerId,
    }).then((result: any) => {
      let da: any = result.data;
      auth.setAttribute(da);
      let d: any = da.info;
      if (d.dispensing && d.dtpRule === 0) {
        d.dtpRule = 3;
      }
      defaultValue(d);
      setData(d);
      setOldData(d);
      auth.setAttributeUpdate(auth.attributeUpdate + 1)
      form.setFieldsValue({
        isScreen: d.isScreen,
        allowReplace: d.allowReplace,
        segmentType: d.segmentType,
        allowRegisterGroup: d.allowRegisterGroup,
        unblindingReasonConfig: d.unblindingReasonConfig,
        freezeReasonConfig: d.freezeReasonConfig,
      });
      setPrefix(d.prefix);
      setRandomControl(d.randomControl);
      setRandomControlRule(d.randomControlRule);
      // setSitePrefixBool(d.sitePrefix);
      setRandomBool(d.random);
      setIsRandomSequenceNumber(d.isRandomSequenceNumber)
      setReplaceRule(d.replaceRule);
      // setOtherPrefixBool(d.otherPrefix);
      setDispensing(d.dispensing);
      setBlind(d.blind);
      setSegment(d.segment);
      switchSegment(d.segment, d.randomControlRule);
      setBlindRule(d.blindingRestrictions);
      setTraceabilityCodeShow(d.connectAli);
      setReplace(d.allowReplace);
      setRegisterGroup(d.allowRegisterGroup)
      setMinimizeCalc(d.minimizeCalc)
      setIPInheritance(d.IPInheritance)
      if (d.subjectNumberRule === 2 || d.subjectNumberRule === 3) {
        //查询受试者数量
        getSubjectCountRun({
          projectId,
          envId: envId,
          cohort: props.cohortId ? props.cohortId : null,
          customerId: customerId,
        }).then((result: any) => {
          let data: any = result.data;
          if (data > 0) {
            setSubjectNumberDisabled(true);
          } else {
            setSubjectNumberDisabled(false);
          }
        });
      } else {
        setSubjectNumberDisabled(false);
      }
      setShow(true);
      console.log(cohort);
    });
  }, [refresh, customerId, projectId, envId, props.cohortId]);

  // 默认值
  const defaultValue = (data: any) => {
    // 标签
    // if (data.subjectReplaceText === "") {
    //     data.subjectReplaceText = formatMessage({ id: 'subject.number' })
    // };
    // 位数精确值
    if (data.accuracy === 0) {
      data.accuracy = 1;
    }
    return data;
  };

  const formChange = () => {
    setReplaceRule(form.getFieldsValue().replaceRule);
    setRandomBool(form.getFieldsValue().random);
    // 如果不随机就把是否展示随机号置为false
    if (!form.getFieldsValue().random) {
      form.setFieldsValue({ isRandomNumber: false });
      form.setFieldsValue({ isRandomSequenceNumber: false });
    }
    setPrefix(form.getFieldsValue().prefix);
    // setSitePrefixBool(form.getFieldsValue().sitePrefix);
    // setOtherPrefixBool(form.getFieldsValue().otherPrefix);
    setDispensing(form.getFieldsValue().dispensing);
    // @ts-ignore
    setSegment(form.getFieldsValue().segment);
    // @ts-ignore
    setBlind(form.getFieldsValue().blind);
    if (!isEquivalent()) {
      setSend(false);
    } else {
      setSend(true);
    }
  };


  function isEquivalent() {
    // let b = form.getFieldsValue();
    const b = _.cloneDeep(form.getFieldsValue());
    b["dtpRule"] = form.getFieldValue("dtpRule"); // dtp规则
    b["instituteLayered"] = data.instituteLayered; // 中心分层
    b["countryLayered"] = data.countryLayered; // 国家分层
    b["regionLayered"] = data.regionLayered; // 区域分层
    b["isRandom"] = data.isRandom; // 中心没有随机号是否可以随机
    b["groupsSegment"] = groupsData; //号段随机
    b["IPInheritance"] = form.getFieldValue("IPInheritance"); //号段随机
    b["remainingVisit"] = form.getFieldValue("remainingVisit"); //号段随机
    b["codeRule"] = form.getFieldValue("codeRule"); //号段随机
    // b["connectAli"] = data.connectAli; // 追溯码
    // b["aliProjectNo"] = data.aliProjectNo; // 追溯码--项目编号
    // console.log("dtpRule===" + JSON.stringify(form.getFieldValue("dtpRule"))); 
    // console.log("1===" + JSON.stringify(oldData.dtpRule)); 
    // console.log("2===" + JSON.stringify(b.dtpRule)); 
    // 比较两个字符串是否相同
    let result = compareObjects(oldData, b);
    // console.log(result); // 输出比较结果
    return result;
  }

  //比较两个JavaScript对象是否相同
  function compareObjects(obj1: any, obj2: any) {
    for (let key in obj1) {
      if ((obj1[key] === undefined || obj1[key] === null) && obj2[key] !== undefined) {
        return false;
      }
      if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
        if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
          if (!arraysAreEqual(obj1[key], obj2[key])) {
            return false;
          }
        } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
          if (!compareObjects(obj1[key], obj2[key])) {
            return false;
          }
        } else {
          if (obj1[key] !== obj2[key]) {
            return false;
          }
        }
      }
    }
    return true;
  }

  //比较两个数组是否相同
  function arraysAreEqual(arr1: any, arr2: any) {
    // 检查数组长度是否相同
    if (arr1.length !== arr2.length) {
      return false;
    }

    const a = _.cloneDeep(arr1);
    const b = _.cloneDeep(arr2);
    // 将数组转换为字符串并比较
    const str1 = JSON.stringify(a.sort());
    const str2 = JSON.stringify(b.sort());

    return str1 === str2;
  }


  // const setTraceabilityCodeShowFun = (e) => {
  //     if (e) {
  //         setTraceabilityCodeShow(true);
  //     } else {
  //         setTraceabilityCodeShow(false);
  //     }
  // };

  const updateAttribute = () => {
    let formData = form.getFieldsValue();
    formData["instituteLayered"] = data.instituteLayered; // 中心分层
    formData["countryLayered"] = data.countryLayered; // 国家分层
    formData["regionLayered"] = data.regionLayered; // 区域分层
    formData["isRandom"] = data.isRandom; // 中心没有随机号是否可以随机
    formData["groupsSegment"] = groupsData; //号段随机
    formData["codeRule"] = codeRuleConfig.codeRule // 编码配置
    //如果随机研究产品供应核查开启，规则必选
    let randomControl = formData["randomControl"];
    let randomControlRule = formData["randomControlRule"];
    if (
      randomControl &&
      (randomControlRule === undefined ||
        randomControlRule === null ||
        randomControlRule === 0)
    ) {
      message.error(
        formatMessage({ id: "project.attribute.randomSubject.err" })
      );
      return;
    }

    var prefixExpression: string = formData["prefixExpression"];
    if (subjectNumberRule === 3) {
      //如果受试者号录入规则是中心中唯一，验证受试者号前缀必须已{siteNO}开始
      if (
        prefixExpression !== undefined &&
        prefixExpression !== null &&
        prefixExpression.length > 0
      ) {
        let subPrefix = prefixExpression.slice(0, 8);
        if (subPrefix !== "{siteNO}") {
          message.error(
            formatMessage({ id: "project.attribute.subjectNumberPrex.err" })
          );
          return;
        }
      }
    }
    if (formData["connectAli"]) {
      getProjectAttributeConnectAliRun({
        projectId: projectId,
        envId: envId,
        connectAli: formData["connectAli"],
        aliProjectNo: formData["aliProjectNo"],
      }).then((res: any) => {
        if (!res.data) {
          message.warn(formatMessage({ id: "project.bound.repeat" }));
          return;
        } else {
          form.validateFields().then(() => {
            updateProjectAttributeRun({
              attribute: formData,
              envId: envId,
              projectId: projectId,
              customerId: customerId,
              cohortId: props.cohortId ? props.cohortId : null,
              groupsSegment: formData["groupsSegment"],
            }).then((res: any) => {
              var messageInfo = res.data;
              setRefresh(refresh + 1);
              if (formData["dispensing"]) {
                if (messageInfo.length > 0) {
                  notification.open({
                    message: (
                      <div
                        style={{
                          fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                          fontSize: "14px",
                        }}
                      >
                        <CheckCircleTwoTone
                          twoToneColor="#2DA641"
                          style={{ paddingRight: "8px" }}
                        />
                        {formatMessage({ id: "common.success" })}
                      </div>
                    ),
                    description: (
                      <div
                        style={{
                          paddingLeft: "20px",
                          color: "#646566",
                        }}
                      >
                        {formatMessage({
                          id: messageInfo,
                        })}
                      </div>
                    ),
                    // duration: 5,
                    placement: "top",
                    style: {
                      width: "720px",
                      height: "95px",
                      background: "#F0FAF2",
                      borderStyle: "solid",
                      border: "1px",
                      borderColor: "#41CC82",
                      borderRadius: "4px",
                    },
                    // closeIcon: null, // 隐藏关闭按钮，用户必须手动关闭
                    duration: null, // 设置为null，让通知框不自动关闭
                  });
                } else {
                  message.success(
                    formatMessage({ id: "message.save.success" })
                  );
                }
              } else {
                notification.open({
                  message: (
                    <div
                      style={{
                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                        fontSize: "14px",
                      }}
                    >
                      <CheckCircleTwoTone
                        twoToneColor="#2DA641"
                        style={{ paddingRight: "8px" }}
                      />
                      {formatMessage({
                        id: "common.success",
                      })}
                    </div>
                  ),
                  description: (
                    <div
                      style={{
                        paddingLeft: "20px",
                        color: "#646566",
                      }}
                    >
                      {formatMessage({
                        id: "projects.attributes.saveInfo",
                      })}
                      <br></br>
                      {messageInfo.length > 0 &&
                        formatMessage({
                          id: messageInfo,
                        })}
                    </div>
                  ),
                  // duration: 5,
                  duration: null, // 设置为null，让通知框不自动关闭
                  placement: "top",
                  style: {
                    width: "720px",
                    height: "95px",
                    background: "#F0FAF2",
                    borderStyle: "solid",
                    border: "1px",
                    borderColor: "#41CC82",
                    borderRadius: "4px",
                  },
                });
              }
            });
            setSend(true);
            setDisabled(true);
            setEdit(true);
          });
        }
      });
    } else {
      form.validateFields().then(() => {
        updateProjectAttributeRun({
          attribute: formData,
          envId: envId,
          projectId: projectId,
          customerId: customerId,
          cohortId: props.cohortId ? props.cohortId : null,
          groupsSegment: formData["groupsSegment"],
        }).then((res: any) => {
          var messageInfo = res.data;
          setRefresh(refresh + 1);
          if (formData["dispensing"]) {
            if (messageInfo.length > 0) {
              notification.open({
                message: (
                  <div
                    style={{
                      fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                      fontSize: "14px",
                    }}
                  >
                    <CheckCircleTwoTone
                      twoToneColor="#2DA641"
                      style={{ paddingRight: "8px" }}
                    />
                    {formatMessage({ id: "common.success" })}
                  </div>
                ),
                description: (
                  <div
                    style={{
                      paddingLeft: "20px",
                      color: "#646566",
                    }}
                  >
                    {formatMessage({
                      id: messageInfo,
                    })}
                  </div>
                ),
                // duration: 5,
                duration: null, // 设置为null，让通知框不自动关闭
                placement: "top",
                style: {
                  width: "720px",
                  height: "95px",
                  background: "#F0FAF2",
                  borderStyle: "solid",
                  border: "1px",
                  borderColor: "#41CC82",
                  borderRadius: "4px",
                },
              });
            } else {
              message.success(formatMessage({ id: "message.save.success" }));
            }
          } else {
            notification.open({
              message: (
                <div
                  style={{
                    fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                    fontSize: "14px",
                  }}
                >
                  <CheckCircleTwoTone
                    twoToneColor="#2DA641"
                    style={{ paddingRight: "8px" }}
                  />
                  {formatMessage({ id: "common.success" })}
                </div>
              ),
              description: (
                <div
                  style={{
                    paddingLeft: "20px",
                    color: "#646566",
                  }}
                >
                  {formatMessage({
                    id: "projects.attributes.saveInfo",
                  })}
                  <br></br>
                  {messageInfo.length > 0 &&
                    formatMessage({
                      id: messageInfo,
                    })}
                </div>
              ),
              // duration: 5,
              duration: null, // 设置为null，让通知框不自动关闭
              placement: "top",
              style: {
                width: "720px",
                height: "95px",
                background: "#F0FAF2",
                borderStyle: "solid",
                border: "1px",
                borderColor: "#41CC82",
                borderRadius: "4px",
              },
            });
          }
        });
        setSend(true);
        setDisabled(true);
        setEdit(true);
      });
    }

    // 更新编码配置
    if (!codeRuleConfig.codeConfigInit) {
      run_saveBarcodeConfig({ barcodeRuleId: codeRuleConfigId }, { ...codeRuleConfig })
        .then(() => {
          auth.setCodeRule(codeRuleConfig.codeRule);
          setRefresh(refresh + 1);
        })
        .catch(() => { })
    }

  };
  const { runAsync: run_saveBarcodeConfig } = useFetch(saveBarcodeConfig, {
    manual: true,
  });
  const { runAsync: run_getBarcodeConfig, loading } = useFetch(
    getBarcodeConfig,
    { manual: true }
  );
  const getCodeRule = () => {
    run_getBarcodeConfig({
      projectId: projectId,
      customerId: customerId,
      envId: envId,
      cohortId: props.cohortId ? props.cohortId : null,
    }).then((response: any) => {
      let result = response.data;
      setCodeRuleConfigId(result.id)
      setCodeRuleConfig({ codeRule: result.codeRule, codeConfigInit: result.codeConfigInit })
    });
  }

  const cancel = () => {
    form.resetFields();
    setRefresh(refresh + 1);
    setEdit(true);
    setDisabled(true);
  };

  const otherHandleChange = (value: any, record: any) => {
    // const _data = [...groupsData];
    const _data = _.cloneDeep(groupsData);
    const _oldData = _.cloneDeep(oldGroupsData);
    _data.forEach((it: any) => {
      if (it.name === record.name) {
        it.segmentLength = value["segmentLength"];
      }
    });
    setGroupsData(_data);
    let groupResult = arraysAreEqual(_oldData, _data);
    // console.log("groupResult===" + groupResult);
    if (!groupResult) {
      setSend(false);
    } else {
      setSend(true);
    }

  };
  useEffect(() => {
    if (segment || randomControlRule === 3) {
      //号段随机开关打开
      //获取实验组信息
      getRandomizationRun({
        projectId,
        env: envId,
        cohort: props.cohortId ? props.cohortId : null,
        customer: customerId,
        roleId: auth.project.permissions.role_id,
      }).then((result: any) => {
        let data: any = result.data;
        if (data.groups != null && data.groups.length > 0) {
          let count = 0;
          data.groups?.forEach((it: any) => {
            if (it.subGroup != null) {
              count = count + it.subGroup.length;
            } else {
              count = count + 1;
            }
          });
          setMaxGroupsCount(count);
          let groups: any = [];
          data.groups.forEach((value: any) => {
            if (value.subGroup !== null && value.subGroup.length !== 0) {
              for (let i = 0; i < value.subGroup.length; i++) {
                groups.push({
                  id: value.id,
                  name: value.name + " " + value.subGroup[i].name,
                  segmentLength: value.subGroup[i].segmentLength,
                });
              }
            } else {
              groups.push({
                id: value.id,
                name: value.name,
                segmentLength: value.segmentLength,
              });
            }
          });
          setOldGroupsData(groups);
          setGroupsData(groups);
        } else {
          setOldGroupsData(null);
          setGroupsData(null);
          setGroupsInfo(
            formatMessage({ id: "projects.attributes.groupsInfo" })
          );
        }
      });
    }
  }, [segment, randomControlRule])
  //查询组别信息
  const switchSegment = (segment: any, randomControlRule: any) => {
    setGroupsData(null);
    setOldGroupsData(null);
    setGroupsInfo(null);
    setSegment(segment);
  };

  const switchBlind = (blind: any) => {
    if (blind) {
      //如果是盲法项目，展示号段随机
      setBlind(blind);
    } else {
      form.setFieldsValue({ segment: false });
      setSegment(false);
      setOldGroupsData(null);
      setGroupsData(null);
      setGroupsInfo(null);
      setBlind(blind);
    }
  };

  const onChangeRandom = (value: any) => {
    setRandomBool(value);
  };

  const updateEdit = (value: any) => {
    const hasPermission = permissionsCohort(
        auth.project.permissions,
            "operation.build.attribute.edit",
            props.cohort?.status
    )
    setDisabled(!(hasPermission && !lockConfig));
    setEdit(false);
  };

  const [labelWidth, setLabelWidth] = useSafeState<number>(g.lang === "en" ? 320 : 260);



  const layout = {
    labelCol: { style: { width: g.lang === "en" ? "320px" : "260px" } },
    // wrapperCol: { flex: 1 },
  };
  useEffect(get_project_attributes, [get_project_attributes]);
  return (
    <div style={{ position: "relative" }}>
      {show && (
        <>
          <Spin spinning={getProjectAttributeLoading}>
            {!getProjectAttributeLoading &&
              permissions(
                auth.project.permissions,
                "operation.build.attribute.view"
              ) && (
                <Form
                  form={form}
                  onValuesChange={formChange}
                  {...layout}
                  style={{
                    height:
                      auth.project.info.type === 1
                        ? "calc(100vh - 66px)"
                        : "calc(100vh - 120px)",
                    overflowY: "auto",
                    padding: "16px 24px 60px 24px",
                  }}
                >
                  <div style={{ marginBottom: 16 }}>
                    <CustomTitle
                      name={intl.formatMessage({
                        id: "projects.attributes.systemRules", allowComponent: true
                      })}
                    />
                  </div>
                  <Row>
                    <Col>
                      <Form.Item
                        label={formatMessage({
                          id: "projects.attributes.isRandom", allowComponent: true
                        })}
                        id="randomItem"
                      >
                        <Form.Item name="random" initialValue={data.random}>
                          <Radio.Group
                            disabled={
                              edit || researchAttribute === 1 || disabled
                            }
                            onChange={(v) => onChangeRandom(v.target.value)}
                          >
                            <Radio value={true}>
                              <FormattedMessage id="projects.attributes.randomYes" />
                            </Radio>
                            {projectType === 1 || inRandomIsolation(auth.project.info.number) ||(projectType === 2 && cohort.type === 0) ?
                              <Radio value={false}>
                                <FormattedMessage id="projects.attributes.randomNo" />
                              </Radio>
                              :
                              null
                            }
                          </Radio.Group>
                        </Form.Item>

                        {randomBool ? (
                          <>
                            <Divider
                              style={{
                                margin: "0px 0px 8px 0px",
                                width: "400px",
                                minWidth: "400px",
                              }}
                            ></Divider>
                            <Form.Item
                              label={formatMessage({
                                id: "projects.attributes.is.random.showNumber", allowComponent: true
                              })}
                              name="isRandomNumber"
                              initialValue={data.isRandomNumber}
                              style={{
                                marginTop: 8,
                                marginBottom: 8,
                              }}
                            >
                              <Radio.Group disabled={disabled}>
                                <Radio value={true}>
                                  <FormattedMessage id="projects.attributes.is.random.number.show" />
                                </Radio>
                                <Radio value={false}>
                                  <FormattedMessage id="projects.attributes.is.random.number.notShow" />
                                </Radio>
                              </Radio.Group>
                            </Form.Item>
                            <Form.Item
                              label={formatMessage({
                                id: "projects.attributes.is.random.showSequenceNumber", allowComponent: true
                              })}
                              name="isRandomSequenceNumber"
                              initialValue={data.isRandomSequenceNumber}
                              style={{
                                marginTop: 8,
                                marginBottom: 8,
                              }}
                              tooltip={{
                                title: formatMessage({
                                  id: "projects.attributes.is.random.sequenceNumber.tip", allowComponent: true
                                }),
                                icon: (
                                  <QuestionCircleFilled style={{ color: "#D0D0D0" }} />
                                ),
                              }}
                            >
                              <Radio.Group onChange={(v) => setIsRandomSequenceNumber(v.target.value)} disabled={disabled}>
                                <Radio value={true}>
                                  <FormattedMessage id="projects.attributes.is.random.number.show" />
                                </Radio>
                                <Radio value={false}>
                                  <FormattedMessage id="projects.attributes.is.random.number.notShow" />
                                </Radio>
                              </Radio.Group>
                            </Form.Item>
                            {isRandomSequenceNumber ? (
                              <>
                                <Divider
                                  style={{
                                    margin: "8px 0px 0px 0px",
                                    width: "400px",
                                    minWidth: "400px",
                                  }}
                                ></Divider>
                                <Row>
                                  <Col>
                                    <Form.Item
                                      label={formatMessage({
                                        id: "projects.attributes.is.random.sequenceNumberPrefix", allowComponent: true
                                      })}
                                      name="randomSequenceNumberPrefix"
                                      initialValue={data.randomSequenceNumberPrefix}
                                      style={{
                                        marginTop: 8,
                                        marginBottom: 8,
                                      }}
                                    >
                                      <Input
                                        disabled={disabled}
                                      />
                                    </Form.Item>
                                  </Col>
                                </Row>
                                <Row>
                                  <Col>
                                    <Form.Item
                                      label={formatMessage({
                                        id: "projects.attributes.is.random.sequenceNumberDigit", allowComponent: true
                                      })}
                                      name="randomSequenceNumberDigit"
                                      initialValue={data.randomSequenceNumberDigit}
                                      style={{
                                        marginTop: 8,
                                        marginBottom: 8,
                                      }}
                                      rules={[{ required: true }]}
                                    >
                                      <InputNumber min={1} precision={0}
                                        disabled={disabled}
                                      />
                                    </Form.Item>
                                  </Col>
                                </Row>
                                <Row>
                                  <Col>
                                    <Form.Item
                                      label={formatMessage({
                                        id: "projects.attributes.is.random.sequenceNumberStartNumber", allowComponent: true
                                      })}
                                      name="randomSequenceNumberStart"
                                      initialValue={data.randomSequenceNumberStart}
                                      style={{
                                        marginTop: 8,
                                        marginBottom: 8,
                                      }}
                                      rules={[{ required: true }]}
                                    >
                                      <InputNumber min={0} precision={0}
                                        disabled={disabled}
                                      />
                                    </Form.Item>
                                  </Col>
                                </Row>
                              </>
                            ) : null}
                          </>
                        ) : null}
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Form.Item
                        label={formatMessage({
                          id: "projects.attributes.dispensingDesign", allowComponent: true
                        })}
                        name="dispensing"
                        initialValue={data.dispensing}
                        style={{ marginTop: -16 }}
                      >
                        <Radio.Group
                          disabled={disabled || researchAttribute === 1}
                          onChange={(v) => {
                            setDispensing(v.target.value);
                            if (v.target.value) {
                              setDtpRuleValue(3);
                              form.setFieldValue("dtpRule", 3);
                            } else {
                              form.setFieldValue("dtpRule", dtpRuleValue);
                            }
                          }}
                        >
                          <Radio value={true}>
                            <FormattedMessage id="projects.attributes.dispensing.yes" />
                          </Radio>
                          <Radio value={false}>
                            <FormattedMessage id="projects.attributes.dispensing.no" />
                            <Tooltip
                              title={formatMessage({
                                id: "projects.attributes.dispensing.noInfo", allowComponent: true
                              })}
                              trigger="hover"
                            >
                              <QuestionCircleFilled
                                style={{
                                  marginLeft: "8px",
                                  color: "#D0D0D0",
                                }}
                              />
                            </Tooltip>
                          </Radio>
                        </Radio.Group>
                      </Form.Item>
                    </Col>
                  </Row>
                  {
                    dispensing ?
                      <Row>
                        <Col>
                          <Form.Item
                            label={formatMessage({
                              id: "projects.attributes.dtp.rule", allowComponent: true
                            })}
                            name="dtpRule"
                            initialValue={data.dtpRule}
                          // style={{ marginTop: -16 }}
                          >
                            <Radio.Group
                              disabled={disabled}
                              value={dtpRuleValue}
                              // style={{
                              //   marginTop: 8,
                              //   marginBottom: 8,
                              // }}
                              onChange={(v) => {
                                setDtpRuleValue(v.target.value);
                              }}
                            >
                              <Radio value={1}>
                                <FormattedMessage id="shipment.medicine" />
                              </Radio>
                              <Radio value={2}>
                                <FormattedMessage id="projects.attributes.dtp.rule.visitFlow" />
                              </Radio>
                              <Radio value={3}>
                                <FormattedMessage id="projects.notApplicable" />
                              </Radio>
                            </Radio.Group>
                          </Form.Item>
                        </Col>
                      </Row> : null
                  }
                  {dispensing ? (
                    <Row>
                      <Col>
                        <Form.Item
                          label={formatMessage({
                            id: "projects.attributes.random.control", allowComponent: true
                          })}
                        >
                          <Row>
                            <Col>
                              <Form.Item
                                name="randomControl"
                                valuePropName="checked"
                                initialValue={data.randomControl}
                              >
                                <Switch
                                  onChange={(v) => {
                                    setRandomControl(v);
                                    setRandomControlRule(null);
                                  }}
                                  size="small"
                                  disabled={disabled}
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                          {randomControl && (
                            <Row>
                              <Col>
                                <Form.Item
                                  label=""
                                  name="randomControlRule"
                                  rules={[{ required: true }]}
                                  initialValue={data.randomControlRule}
                                >
                                  <Radio.Group
                                    disabled={
                                      edit ||
                                      researchAttribute === 1 ||
                                      disabled
                                    }
                                    onChange={(v) =>
                                      setRandomControlRule(v.target.value)
                                    }
                                  >
                                    <Space direction="vertical">
                                      <Radio value={1}>
                                        <FormattedMessage id="projects.attributes.random.control.rule1" />
                                        <Tooltip
                                          title={formatMessage({
                                            id: "projects.attributes.random.control.rule1Info", allowComponent: true
                                          })}
                                          trigger="hover"
                                        >
                                          <QuestionCircleFilled
                                            style={{
                                              marginLeft: "8px",
                                              color: "#D0D0D0",
                                            }}
                                          />
                                        </Tooltip>
                                      </Radio>
                                      <Radio value={2}>
                                        <FormattedMessage id="projects.attributes.random.control.rule2" />
                                        <Tooltip
                                          title={formatMessage({
                                            id: "projects.attributes.random.control.rule2Info", allowComponent: true
                                          })}
                                          trigger="hover"
                                        >
                                          <QuestionCircleFilled
                                            style={{
                                              marginLeft: "8px",
                                              color: "#D0D0D0",
                                            }}
                                          />
                                        </Tooltip>
                                      </Radio>
                                      <Radio value={3}>
                                        <FormattedMessage id="projects.attributes.random.control.rule3" />
                                        <Tooltip
                                          title={formatMessage({
                                            id: "projects.attributes.random.control.rule3Info", allowComponent: true
                                          })}
                                          trigger="hover"
                                        >
                                          <QuestionCircleFilled
                                            style={{
                                              marginLeft: "8px",
                                              color: "#D0D0D0",
                                            }}
                                          />
                                        </Tooltip>
                                        {randomControlRule === 3 && (
                                          <>
                                            <Spin spinning={getRandomizationLoading}>
                                              <br></br>
                                              <Form.Item
                                                label=""
                                                name="randomControlGroup"
                                                initialValue={
                                                  data.randomControlGroup
                                                }
                                              >
                                                <InputNumber
                                                  style={{
                                                    marginTop: "12px",
                                                    width: "420px",
                                                  }}
                                                  precision={0}
                                                  min={0}
                                                  max={maxGroupsCount}
                                                  step={1}
                                                  disabled={
                                                    edit ||
                                                    researchAttribute === 1 ||
                                                    disabled
                                                  }
                                                  controls
                                                  addonBefore={formatMessage({
                                                    id: "projects.attributes.random.control.rule3Input1", allowComponent: true
                                                  })}
                                                  addonAfter={formatMessage({
                                                    id: "projects.attributes.random.control.rule3Input2", allowComponent: true
                                                  })}
                                                />
                                              </Form.Item>
                                              {maxGroupsCount === 0 && (

                                                <Row>
                                                  <span style={{ color: "#677283", }}>
                                                    <svg
                                                      className="iconfont mouse"
                                                      width={12}
                                                      height={12}
                                                      style={{ marginLeft: 4, marginTop: 4, marginRight: 6 }}
                                                    >
                                                      <use xlinkHref="#icon-jinggao" />
                                                    </svg>
                                                    {formatMessage({
                                                      id: "projects.attributes.groupsInfo", allowComponent: true
                                                    })}
                                                    <AuthButton
                                                      type="link"
                                                      onClick={() =>
                                                        window.open(
                                                          "/project/build/randomization",
                                                          "_self"
                                                        )
                                                      }
                                                    >
                                                      {
                                                        <FormattedMessage
                                                          id={"common.to.set"}
                                                        />
                                                      }
                                                    </AuthButton>
                                                  </span>
                                                </Row>

                                                // <Space>
                                                //   <FormattedMessage
                                                //     id={"projects.attributes.groupsInfo"}
                                                //   />

                                                //   <Button
                                                //     type="link"
                                                //     onClick={() =>
                                                //       window.open(
                                                //         "/project/build/randomization",
                                                //         "_self"
                                                //       )
                                                //     }
                                                //   >
                                                //     {
                                                //       <FormattedMessage
                                                //         id={"common.to.set"}
                                                //       />
                                                //     }
                                                //   </Button>
                                                // </Space>


                                              )}
                                            </Spin>
                                          </>
                                        )}
                                      </Radio>
                                    </Space>
                                  </Radio.Group>
                                </Form.Item>
                              </Col>
                            </Row>
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                  ) : null}

                  <Row>
                    <Form.Item valuePropName="checked" name={"allowRegisterGroup"} style={{ marginBottom: 12 }} label={formatMessage({ id: "projects.attributes.random.registerGroup", allowComponent: true })}>
                      <Switch
                        onChange={(v) => {
                          setRegisterGroup(v)
                        }}
                        size="small"
                        disabled={disabled}
                      />
                    </Form.Item>

                  </Row>
                  {
                    registerGroup &&
                    <Row style={{ marginLeft: g.lang === "en" ? "320px" : "260px", color: "#677283" }}> {formatMessage({ id: "projects.attributes.random.registerGroup.info", allowComponent: true })}</Row>
                  }
                  <Row>
                    <Col>
                      <Form.Item
                        label={formatMessage({
                          id: "projects.attributes.isBlind", allowComponent: true
                        })}
                        name="blind"
                        initialValue={data.blind}
                        style={{ marginTop: 12 }}
                      >
                        <Radio.Group
                          disabled={disabled}
                          onChange={(v) => switchBlind(v.target.value)}
                        >
                          <Radio value={true}>
                            <FormattedMessage id="projects.attributes.isBlind.blind" />
                          </Radio>
                          <Radio value={false}>
                            <FormattedMessage id="projects.attributes.isBlind.open" />
                          </Radio>
                        </Radio.Group>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Form.Item
                        label={formatMessage({
                          id: "projects.attributes.isScreen", allowComponent: true
                        })}
                        name="isScreen"
                        valuePropName="checked"
                        initialValue={data.isScreen}
                      >
                        <Switch
                          onChange={(v) => {
                            form.setFieldsValue({ isScreen: v });
                          }}
                          size="small"
                          disabled={
                            disabled || (connectEdc === 1 && pushMode === 1)
                          }
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Form.Item
                      label={formatMessage({
                        id: "projects.attributes.random.minimize.calc", allowComponent: true
                      })}
                      name="minimizeCalc"
                      initialValue={data.minimizeCalc}
                    >
                      <Radio.Group
                        disabled={disabled}
                        onChange={(v) => setMinimizeCalc(v.target.value)}

                      >
                        <Radio value={0}>
                          <FormattedMessage id="projects.attributes.random.minimize.calc.factor" />
                        </Radio>
                        <Radio value={1}>
                          <FormattedMessage id="projects.attributes.random.minimize.calc.actual.factor" />
                        </Radio>
                        <Radio value={2} disabled={disabled || randomizationType === 2}>
                          <FormattedMessage id="projects.notApplicable" />
                        </Radio>
                      </Radio.Group>
                    </Form.Item>
                  </Row>
                  {
                    minimizeCalc === 1 ?
                      <Row>
                        <span style={{ paddingTop: 6, marginLeft: g.lang === "en" ? 530 : 355, color: "#677283", }}>
                          <svg
                            className="iconfont mouse"
                            width={12}
                            height={12}
                            style={{ marginLeft: 4, marginTop: 4, marginRight: 6 }}
                          >
                            <use xlinkHref="#icon-jinggao" />
                          </svg>
                          {formatMessage({
                            id: "projects.attributes.random.minimize.calc.tip", allowComponent: true
                          })}
                        </span>
                      </Row>
                      :
                      null
                  }

                  <Row>
                    <Form.Item valuePropName="checked" initialValue={data.IPInheritance} label={formatMessage({ id: "projects.attributes.visit.inheritance", allowComponent: true })}
                      tooltip={{
                        title: formatMessage({
                          id: "projects.attributes.visit.inheritance.tip", allowComponent: true
                        }),
                        icon: (
                          <QuestionCircleFilled style={{ color: "#D0D0D0" }} />
                        ),
                      }}


                      name={"IPInheritance"}>
                      <Switch
                        onChange={(v) => {
                          form.setFieldsValue({ IPInheritance: v });
                          setIPInheritance(v)
                        }}
                        size="small"
                        disabled={disabled}
                      />
                    </Form.Item>
                    {IPInheritance &&
                      <>
                        <Divider type="vertical" style={{ border: "0.5px solid #C8C9CC", marginTop: 12, marginLeft: 16 }} />

                        <Form.Item labelCol={{ style: { width: g.lang == "zh" ? 100 : 150, marginLeft: 0 } }}
                          label={formatMessage({ id: "projects.attributes.visit.inheritance.enough", allowComponent: true })} initialValue={data.remainingVisit} name={"remainingVisit"}>
                          {
                            disabled ? (
                              !data.remainingVisit ? (
                                "-"
                              ) : (
                                data.remainingVisit
                              )
                            ) :
                              <InputNumber disabled={disabled} bordered={!disabled} min={1} className={"full-width"} placeholder={formatMessage({ id: 'common.required.prefix' }) as string} />

                          }
                        </Form.Item>
                      </>

                    }
                  </Row>

                  <div style={{ marginBottom: 16 }}>
                    <CustomTitle
                      name={intl.formatMessage({
                        id: "projects.attributes.subject.numberRules", allowComponent: true
                      })}
                    />
                  </div>
                  <React.Fragment>
                    <Row>
                      <Col>
                        <Form.Item
                          label={formatMessage({
                            id: "projects.attributes.subject.number.rules", allowComponent: true
                          })}
                          name="subjectNumberRule"
                          initialValue={
                            data.subjectNumberRule === 0
                              ? 1
                              : data.subjectNumberRule
                          }
                        >
                          <Select
                            defaultValue={subjectNumberRule}
                            allowClear
                            disabled={disabled || subjectNumberDisabled}
                            style={{ width: "400px" }}
                            onChange={(value) => {
                              setSubjectNumberRule(value);
                              if (value === 3) {
                                form.setFieldValue("prefix", true);
                                setPrefix(true);
                                form.setFieldValue(
                                  "prefixExpression",
                                  "{siteNO}"
                                );
                              } else {
                                form.setFieldValue("prefixExpression", null);
                              }
                            }}
                          >
                            <Select.Option value={1} key="1">
                              <FormattedMessage id="projects.attributes.subject.number.rule1" />
                            </Select.Option>
                            <Select.Option value={2} key="2">
                              <FormattedMessage id="projects.attributes.subject.number.rule2" />
                            </Select.Option>
                            <Select.Option value={3} key="3">
                              <FormattedMessage id="projects.attributes.subject.number.rule3" />
                            </Select.Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Form.Item
                          label={formatMessage({
                            id: "projects.attributes.prefix.bool", allowComponent: true
                          })}
                          name="prefix"
                          valuePropName="checked"
                          initialValue={data.prefix}
                        >
                          <Switch
                            // checked={subjectNumberRule === 3} // 当subjectNumberRule等于3时，开关为打开状态
                            onChange={(v) => {
                              form.setFieldsValue({ prefix: v });
                              switchBlind(v);
                            }}
                            disabled={subjectNumberRule !== 3 ? disabled : true}
                            size={"small"}
                          ></Switch>
                          {/* <Radio.Group
                            disabled={disabled}
                            onChange={(v) => switchBlind(v.target.value)}
                          >
                            <Radio value={true}>
                              <FormattedMessage id="common.have" />
                            </Radio>
                            {subjectNumberRule !== 3 && (
                              <Radio value={false}>
                                <FormattedMessage id="common.nothing" />
                              </Radio>
                            )}
                          </Radio.Group> */}
                        </Form.Item>
                      </Col>
                    </Row>

                    {prefix ? (
                      <>
                        {/*前缀表达式*/}
                        <Row>
                          <Col>
                            <Form.Item
                              label={formatMessage({
                                id: "projects.attributes.prefix", allowComponent: true
                              })}
                              tooltip={{
                                overlayStyle: {
                                  maxWidth: 800,
                                },
                                title: (
                                  <>
                                    <p>
                                      <FormattedMessage id="projects.attributes.prefix.tooltip1" />
                                    </p>
                                    <p>
                                      <FormattedMessage id="projects.attributes.prefix.tooltip2" />
                                    </p>
                                  </>
                                ),
                                icon: (
                                  <QuestionCircleFilled
                                    style={{
                                      color: "#D0D0D0",
                                    }}
                                  />
                                ),
                              }}
                              name="prefixExpression"
                              rules={[{ required: true }]}
                              initialValue={data.prefixExpression}
                            >
                              {disabled ? (
                                data.prefixExpression
                              ) : (
                                <Input
                                  onBlur={(e) =>
                                    form.setFieldValue(
                                      "prefixExpression",
                                      e.target.value.trim()
                                    )
                                  }
                                  disabled={disabled}
                                  style={{
                                    width: "400px",
                                  }}
                                />
                              )}
                            </Form.Item>
                          </Col>
                        </Row>
                        {/*/!*是否将中心作为前缀*!/*/}
                        {/*<Row gutter={8}>*/}
                        {/*    <Col span={24}>*/}
                        {/*        <Form.Item label={formatMessage({ id: 'projects.attributes.subject.type.site.numberPrefix' })} name="sitePrefix" valuePropName='checked' initialValue={data.sitePrefix}>*/}
                        {/*            <Switch size='small' disabled={disabled}  />*/}
                        {/*        </Form.Item>*/}
                        {/*    </Col>*/}
                        {/*    <Col span={24}>*/}
                        {/*        {*/}
                        {/*            sitePrefixBool ?*/}
                        {/*                <Form.Item label={formatMessage({ id: 'projects.attributes.subject.prefixConnector' })} name="prefixConnector" initialValue={data.prefixConnector}>*/}
                        {/*                    {*/}
                        {/*                        disabled?data.prefixConnector*/}
                        {/*                        :*/}
                        {/*                        <Select disabled={disabled} style={{ width: '400px' }} >*/}
                        {/*                        <Select.Option value="-"> - </Select.Option>*/}
                        {/*                        <Select.Option value="~"> ~ </Select.Option>*/}
                        {/*                        <Select.Option value="/"> / </Select.Option>*/}
                        {/*                    </Select>*/}
                        {/*                    }*/}
                        {/*                </Form.Item>*/}
                        {/*                : null*/}
                        {/*        }*/}
                        {/*    </Col>*/}
                        {/*</Row>*/}
                        {/*<Row gutter={8}>*/}
                        {/*    /!*受试者号的其他前缀*!/*/}
                        {/*    <Col span={24}>*/}
                        {/*        <Form.Item label={formatMessage({ id: 'projects.attributes.subject.other.prefix' })} name="otherPrefix" valuePropName='checked' initialValue={data.otherPrefix}>*/}
                        {/*            <Switch size='small' disabled={disabled} />*/}
                        {/*        </Form.Item>*/}
                        {/*    </Col>*/}
                        {/*    <Col span={24}>*/}
                        {/*        {*/}
                        {/*            otherPrefixBool ?*/}
                        {/*                <Form.Item label={formatMessage({ id: 'projects.attributes.subject.prefix.text' })} name="otherPrefixText" rules={[{ required: true }]} initialValue={data.otherPrefixText}>*/}
                        {/*                    {disabled?data.otherPrefixText:<Input disabled={disabled} style={{ width: '400px' }} />}*/}
                        {/*                </Form.Item>*/}
                        {/*                : null*/}
                        {/*        }*/}
                        {/*    </Col>*/}
                        {/*</Row>*/}
                      </>
                    ) : null}

                    <Row>
                      <Col>
                        <Form.Item
                          label={formatMessage({
                            id: "projects.attributes.subject.replace.text", allowComponent: true
                          })}
                          name="subjectReplaceText"
                          // rules={[{ required: true }]}
                          initialValue={data.subjectReplaceText}
                        >
                          {disabled ? (
                            data.subjectReplaceText === "" ? (
                              "-"
                            ) : (
                              data.subjectReplaceText
                            )
                          ) : (
                            <Input
                              disabled={disabled}
                              style={{
                                width: "400px",
                              }}
                            />
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Form.Item
                          label={formatMessage({
                            id: "projects.attributes.subject.replace.text.en", allowComponent: true
                          })}
                          name="subjectReplaceTextEn"
                          // rules={[{ required: true }]}
                          initialValue={data.subjectReplaceTextEn}
                        >
                          {disabled ? (
                            data.subjectReplaceTextEn === "" ? (
                              "-"
                            ) : (
                              data.subjectReplaceTextEn
                            )
                          ) : (
                            <Input
                              disabled={disabled}
                              style={{
                                width: "400px",
                              }}
                            />
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Form.Item
                          label={formatMessage({
                            id: "form.control.type.exact", allowComponent: true
                          })}
                          name="accuracy"
                          initialValue={data.accuracy}
                        >
                          <Radio.Group disabled={disabled}>
                            <Radio value={1}>
                              <FormattedMessage id="form.control.type.le" />
                            </Radio>
                            <Radio value={2}>
                              <FormattedMessage id="form.control.type.eq" />
                            </Radio>
                          </Radio.Group>
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Form.Item
                          label={formatMessage({
                            id: "form.control.type.maximum", allowComponent: true
                          })}
                          name="digit"
                          rules={[{ required: true }]}
                          initialValue={data.digit}
                        >
                          {disabled ? (
                            data.digit === "" ? (
                              "-"
                            ) : (
                              data.digit
                            )
                          ) : (
                            <InputNumber
                              disabled={disabled}
                              style={{
                                width: "100px",
                              }}
                              precision={0}
                              min={0}
                              step={1}
                            />
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Form.Item
                        label={formatMessage({
                          id: "subject.register.replace", allowComponent: true
                        })}
                        name={"allowReplace"}
                        // initialValue={
                        //     data.allowReplace
                        // }
                        valuePropName="checked"
                      >
                        <Switch
                          onChange={(v) => {
                            setReplace(v);
                          }}
                          disabled={disabled}
                          size={"small"}
                        ></Switch>
                      </Form.Item>
                    </Row>

                    {
                      replace &&
                      <Row style={{ marginLeft: g.lang === "en" ? "320px" : "260px", color: "#677283", paddingBottom: 12 }}>

                        <svg
                          className="iconfont mouse"
                          width={12}
                          height={12}
                          style={{ marginLeft: 4, marginTop: 6, marginRight: 6 }}
                        >
                          <use xlinkHref="#icon-jinggao" />
                        </svg>
                        {formatMessage({ id: "projects.attributes.subject.allow.replace.tip", allowComponent: true })}</Row>
                    }

                    {replace && (
                      <Row>
                        <Col>
                          <Form.Item
                            label={formatMessage({
                              id: "projects.attributes.subject.replace", allowComponent: true
                            })}
                            name="replaceRule"
                            initialValue={data.replaceRule}
                            style={{
                              marginBottom: replaceRule === 1 ? "4px" : "16px",
                            }}
                          >
                            <Radio.Group
                              disabled={disabled}
                              onChange={(e) => setReplaceRule(e.target.value)}
                            >
                              <Radio value={0}>
                                <FormattedMessage id="projects.attributes.subject.replace.customize" />
                              </Radio>
                              <Radio value={1}>
                                <FormattedMessage id="projects.attributes.subject.replace.auto" />
                              </Radio>
                            </Radio.Group>
                          </Form.Item>
                          {replaceRule === 1 ? (
                            <>
                              <span
                                style={{
                                  color: "#677283",
                                  display: "inline-block",
                                  marginLeft:
                                    g.lang === "zh" ? "365px" : "445px",
                                }}
                              >

                                <svg
                                  className="iconfont mouse"
                                  width={12}
                                  height={12}
                                  style={{ marginLeft: 4, marginTop: 6 }}
                                >
                                  <use xlinkHref="#icon-jinggao" />
                                </svg>
                                <FormattedMessage
                                  id={"projects.attributes.subject.replace.tip"}
                                />
                              </span>
                              <Divider
                                style={{
                                  marginTop: "8px",
                                  marginBottom: "8px",
                                  marginLeft:
                                    g.lang === "zh" ? "365px" : "445px",
                                  width: g.lang == "zh" ? "460px" : "570px",
                                  minWidth: g.lang == "zh" ? "400px" : "500px",
                                }}
                              ></Divider>
                              <Row>
                                <Col>
                                  <Input
                                    style={{
                                      marginLeft:
                                        g.lang === "zh" ? "365px" : "445px",
                                      width:
                                        g.lang === "zh" ? "115px" : "255px",
                                    }}
                                    value={"+"}
                                    addonBefore={formatMessage({
                                      id: "projects.attributes.subject.replace.original", allowComponent: true
                                    })}
                                  ></Input>
                                </Col>
                                <Col>
                                  <Form.Item
                                    name="replaceRuleNumber"
                                    initialValue={data.replaceRuleNumber}
                                  >
                                    <InputNumber
                                      disabled={disabled}
                                      style={{
                                        marginLeft: "8px",
                                        width: "70px",
                                      }}
                                      precision={0}
                                      min={0}
                                      max={9999}
                                      step={1}
                                    />
                                  </Form.Item>
                                </Col>
                              </Row>
                            </>
                          ) : null}
                        </Col>
                      </Row>
                    )}
                  </React.Fragment>
                  <React.Fragment>
                    <div style={{ marginBottom: 16 }}>
                      <CustomTitle
                        name={intl.formatMessage({
                          id: "projects.attributes.blind.rules", allowComponent: true
                        })}
                      />
                    </div>
                    <Form.Item
                      label={formatMessage({
                        id: "projects.attributes.blind.rules.stop", allowComponent: true
                      })}
                      tooltip={{
                        title: formatMessage({
                          id: "projects.attributes.blind.rules.stop.open", allowComponent: true
                        }),
                        icon: (
                          <QuestionCircleFilled style={{ color: "#D0D0D0" }} />
                        ),
                      }}
                    >
                      <Row>
                        <Col>
                          <Form.Item
                            name="blindingRestrictions"
                            valuePropName="checked"
                            initialValue={data.blindingRestrictions}
                          >
                            <Switch
                              onChange={(v) => {
                                setBlindRule(v);
                              }}
                              size="small"
                              disabled={disabled}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                      {blindRule && (
                        <Row>
                          <Col>
                            <Form.Item
                              label=""
                              name="pvUnBlindingRestrictions"
                              valuePropName="checked"
                              initialValue={data.pvUnBlindingRestrictions}
                              tooltip={{
                                title: formatMessage({
                                  id: "projects.attributes.isFreeze.info", allowComponent: true
                                }),
                                icon: (
                                  <QuestionCircleFilled
                                    style={{
                                      color: "#D0D0D0",
                                    }}
                                  />
                                ),
                              }}
                            >
                              <Checkbox
                                disabled={disabled}
                              >
                                {formatMessage({
                                  id: "projects.attributes.blind.rules.stop.pv", allowComponent: true
                                })}
                              </Checkbox>
                              {/* <Radio.Group 
                                  disabled={
                                    ! permissionsCohort(
                                        auth.project.permissions,
                                        "operation.build.attribute.edit",
                                        props.cohort?.status
                                    ) || edit
                                  }
                              >
                                  <Radio value={true}>
                                      {formatMessage({
                                        id: "projects.attributes.blind.rules.stop.pv",
                                      })}
                                  </Radio>
                                  <Radio value={false}>
                                      {formatMessage({
                                        id: "projects.attributes.blind.rules.stop.pv.notApplicable",
                                      })}
                                  </Radio>
                              </Radio.Group> */}
                            </Form.Item>
                          </Col>
                        </Row>
                      )}
                    </Form.Item>
                  </React.Fragment>
                  {/* { auth.project.info.type === 1? */}
                  {/* 编码配置 */}
                  <Form.Item hidden name={'codeRule'} />
                  <Form.Item hidden name={'codeConfigInit'} />
                  <CodeRule
                    disabled={disabled}
                    codeRuleConfig={codeRuleConfig}
                    onChange={(codeRule: number) => {
                      form.setFieldsValue({ codeRule: codeRule })
                      setCodeRuleConfig({ ...codeRuleConfig, codeRule: codeRule })
                      formChange()
                    }}
                  />

                  <React.Fragment>
                    <div style={{ marginBottom: 16 }}>
                      <CustomTitle
                        name={intl.formatMessage({
                          id: "common.rest.assured", allowComponent: true
                        })}
                      />
                    </div>
                    <Form.Item
                      label={formatMessage({
                        id: "projects.traceability.code", allowComponent: true
                      })}
                      tooltip={{
                        title: formatMessage({
                          id: "projects.attributes.rest.assured.stop.open", allowComponent: true
                        }),
                        icon: (
                          <QuestionCircleFilled style={{ color: "#D0D0D0" }} />
                        ),
                      }}
                    >
                      <Row>
                        <Col>
                          <Form.Item
                            name="connectAli"
                            valuePropName="checked"
                            initialValue={data.connectAli}
                          >
                            <Switch
                              onChange={(v) => {
                                setTraceabilityCodeShow(v);
                              }}
                              size="small"
                              disabled={disabled}
                            />
                          </Form.Item>
                          {traceabilityCodeShow && (
                            <Row>
                              <Col>
                                <Form.Item
                                  label={formatMessage({
                                    id: "projects.number", allowComponent: true
                                  })}
                                  name="aliProjectNo"
                                  initialValue={data.aliProjectNo}
                                  rules={[
                                    {
                                      required: true,
                                    },
                                  ]}
                                >
                                  <Input
                                    placeholder={formatMessage({
                                      id: "project.statistics.projectNo"
                                    }) as string}
                                    allowClear
                                    maxLength={100}
                                    showCount
                                    style={{
                                      width: "200px",
                                      marginRight: "24px",
                                    }}
                                    disabled={disabled}
                                  />
                                </Form.Item>
                              </Col>
                            </Row>
                          )}
                        </Col>
                      </Row>
                    </Form.Item>
                  </React.Fragment>
                  {/* :null
                            } */}

                  <div style={{ marginBottom: 16 }}>
                    <CustomTitle
                      name={intl.formatMessage({
                        id: "projects.attributes.other.numberRules", allowComponent: true
                      })}
                    />
                  </div>
                  <Row>
                    <Col>
                      <Form.Item
                        label={formatMessage({
                          id: "projects.attributes.isFreeze", allowComponent: true
                        })}
                        name="isFreeze"
                        valuePropName="checked"
                        initialValue={data.isFreeze}
                        tooltip={{
                          title: formatMessage({
                            id: "projects.attributes.isFreeze.info", allowComponent: true
                          }),
                          icon: (
                            <QuestionCircleFilled
                              style={{
                                color: "#D0D0D0",
                              }}
                            />
                          ),
                        }}
                      >
                        <Switch
                          size="small"
                          disabled={
                            !permissionsCohort(
                              auth.project.permissions,
                              "operation.build.attribute.edit",
                              props.cohort?.status
                            ) || edit
                          }
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      {connectEdc === 1 && pushMode === 1 && dispensing ? (
                        <Form.Item
                          label={formatMessage({
                            id: "projects.attributes.edc.label", allowComponent: true
                          })}
                          name="edcDrugConfigLabel"
                          rules={[{ required: true }]}
                          initialValue={data.edcDrugConfigLabel}
                        >
                          <Input disabled={disabled} style={{ width: "30%" }} />
                        </Form.Item>
                      ) : null}
                    </Col>
                  </Row>
                  <Row>
                    {blind && (
                      <Col span={24}>
                        <Form.Item
                          label={formatMessage({
                            id: "projects.attributes.segment", allowComponent: true
                          })}
                        >
                          <Form.Item
                            name="segment"
                            valuePropName="checked"
                            initialValue={data.segment}
                          >
                            <Switch
                              size="small"
                              disabled={disabled}
                              onChange={(c) => switchSegment(c, randomControlRule)}
                            />
                          </Form.Item>
                          <div>
                            {groupsInfo != null && (
                              <Alert
                                message={groupsInfo}
                                type="warning"
                                action={
                                  <Space>
                                    <AuthButton
                                      type="link"
                                      onClick={() =>
                                        window.open(
                                          "/project/build/randomization",
                                          "_self"
                                        )
                                      }
                                    >
                                      {
                                        <FormattedMessage
                                          id={"common.to.set"}
                                        />
                                      }
                                    </AuthButton>
                                  </Space>
                                }
                              />
                            )}
                            {segment && (
                              <div style={{
                                width: 600,
                              }}>
                                <Divider
                                  style={{
                                    margin: "8px 0",
                                  }}
                                />

                                <Form.Item label={formatMessage({ id: "projects.attributes.segment.type", allowComponent: true })} name={"segmentType"}>
                                  <Radio.Group
                                    disabled={disabled}
                                  >
                                    <Radio value={0}>
                                      <FormattedMessage id="drug.list.serialNumber" />
                                    </Radio>
                                    <Radio value={1}>
                                      <FormattedMessage id="drug.list.drugNumber" />
                                    </Radio>
                                  </Radio.Group>
                                </Form.Item>

                                <Table
                                  className="mar-top-5"
                                  size="small"
                                  dataSource={groupsData}
                                  pagination={false}
                                  rowKey={(record) => record.id}

                                >
                                  <Table.Column
                                    title={
                                      <FormattedMessage id="drug.configure.group" />
                                    }
                                    dataIndex={"name"}
                                    key="name"
                                    ellipsis
                                  />
                                  <Table.Column
                                    title={
                                      <FormattedMessage id="projects.attributes.segment.length" />
                                    }
                                    dataIndex={"segmentLength"}
                                    key="segmentLength"
                                    ellipsis
                                    render={(value, record) =>
                                      disabled ? (
                                        value
                                      ) : (
                                        <InputNumber
                                          disabled={disabled}
                                          precision={0}
                                          min={1}
                                          step={1}
                                          max={999}
                                          defaultValue={value}
                                          onChange={(e) =>
                                            otherHandleChange(
                                              {
                                                segmentLength: e,
                                              },
                                              record
                                            )
                                          }
                                        />
                                      )
                                    }
                                  />
                                </Table>
                              </div>
                            )}
                          </div>
                        </Form.Item>
                      </Col>
                    )}
                  </Row>
                  <Row>
                    <Col>
                      <Form.Item
                        label={formatMessage({
                          id: "projects.attributes.unblindingReason", allowComponent: true
                        })}
                        name="unblindingReasonConfig"
                        initialValue={data.unblindingReasonConfig}
                      >
                        <Form.List name="unblindingReasonConfig">
                          {(fields, { add, remove }) => (
                            <>
                              {fields.map((field: any) => (
                                <Space
                                  key={field.key}
                                  style={{
                                    display: "flex",
                                  }}
                                  align="baseline"
                                  size={0}
                                >
                                  <Form.Item
                                    {...field}
                                    name={[field.name, "reason"]}
                                    fieldKey={[field.fieldKey, "reason"]}
                                    key={[field.fieldKey, "reason"]}
                                    rules={[
                                      {
                                        required: true,
                                        message: formatMessage({
                                          id: "subject.unblinding.reason", allowComponent: true
                                        }),
                                        whitespace: true,
                                      },
                                    ]}
                                    style={{
                                      width: "200px",
                                      marginRight: "24px",
                                    }}
                                  >
                                    <Input
                                      disabled={disabled}
                                      placeholder={formatMessage({
                                        id: "placeholder.input.common",
                                      }) as string}
                                    />
                                  </Form.Item>
                                  {!disabled && fields.length > 1 ? (
                                    <MinusCircleFilled
                                      style={{
                                        color: "#F96964",
                                        marginRight: "24px",
                                      }}
                                      onClick={() => remove(field.name)}
                                    />
                                  ) : null}
                                  <Form.Item
                                    {...field}
                                    valuePropName="checked"
                                    name={[field.name, "allowRemark"]}
                                    fieldKey={[field.fieldKey, "allowRemark"]}
                                  >
                                    <Checkbox disabled={disabled}>
                                      {formatMessage({
                                        id: "projects.attributes.allowed.remark", allowComponent: true
                                      })}
                                    </Checkbox>
                                  </Form.Item>
                                </Space>
                              ))}
                              {!disabled ? (
                                <Form.Item
                                  style={{
                                    width: "200px",
                                  }}
                                >
                                  <AuthButton
                                    type="dashed"
                                    onClick={() => add()}
                                    block
                                    icon={<PlusOutlined />}
                                  />
                                </Form.Item>
                              ) : null}
                            </>
                          )}
                        </Form.List>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Form.Item
                        label={formatMessage({
                          id: "projects.attributes.freezeReason", allowComponent: true
                        })}
                        name="freezeReasonConfig"
                        initialValue={data.freezeReasonConfig}
                      >
                        {disabled && (data.freezeReasonConfig == null || data.freezeReasonConfig.length === 0) ? "-" : <Form.List name="freezeReasonConfig">
                          {(fields, { add, remove }) => (
                            <>
                              {fields.map((field: any) => (
                                <Space
                                  key={field.key}
                                  style={{
                                    display: "flex",
                                  }}
                                  align="baseline"
                                  size={0}
                                >
                                  <Form.Item
                                    {...field}
                                    name={[field.name, "reason"]}
                                    fieldKey={[field.fieldKey, "reason"]}
                                    key={[field.fieldKey, "reason"]}
                                    rules={[
                                      {
                                        required: true,
                                        message: formatMessage({
                                          id: "projects.attributes.freezeReason.info", allowComponent: true
                                        }),
                                        whitespace: true,
                                      },
                                    ]}
                                    style={{
                                      width: "200px",
                                      marginRight: "24px",
                                    }}
                                  >
                                    <Input
                                      disabled={disabled}
                                      placeholder={formatMessage({
                                        id: "placeholder.input.common",
                                      }) as string}
                                    />
                                  </Form.Item>
                                  {!disabled ? (
                                    <MinusCircleFilled
                                      style={{
                                        color: "#F96964",
                                        marginRight: "24px",
                                      }}
                                      onClick={() => remove(field.name)}
                                    />
                                  ) : null}
                                  <Form.Item
                                    {...field}
                                    valuePropName="checked"
                                    name={[field.name, "allowRemark"]}
                                    fieldKey={[field.fieldKey, "allowRemark"]}
                                  >
                                    <Checkbox disabled={disabled}>
                                      {formatMessage({
                                        id: "projects.attributes.allowed.remark", allowComponent: true
                                      })}
                                    </Checkbox>
                                  </Form.Item>
                                </Space>
                              ))}
                              {!disabled && fields.length < 10 ? (
                                <Form.Item
                                  style={{
                                    width: "200px",
                                  }}
                                >
                                  <AuthButton
                                    type="dashed"
                                    onClick={() => add()}
                                    block
                                    icon={<PlusOutlined />}
                                  />
                                </Form.Item>
                              ) : null}
                            </>
                          )}
                        </Form.List>}

                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              )}
            <HistoryList bind={history_ref} />
          </Spin>
          {!(
            permissionsCohort(
              auth.project.permissions,
              "operation.build.attribute.edit",
              props.cohort?.status
            )
          ) ||
            projectStatus === 2 ||
            !edit ? null : (
            <FooterRow justify="end">
              <Col>
                <AuthButton type="primary" onClick={updateEdit}>
                  <FormattedMessage id="common.edit" />
                </AuthButton>
              </Col>
            </FooterRow>
          )}
          {projectStatus === 2 || edit ? null : (
            <FooterRow justify="end">
              <Space size={10}>
                <AuthButton onClick={() => cancel()}>
                  <FormattedMessage id="common.cancel" />
                </AuthButton>
                <AuthButton
                  loading={updateProjectAttributeLoading}
                  disabled={send}
                  type="primary"
                  onClick={updateAttribute}
                >
                  {formatMessage({ id: "common.save", allowComponent: true })}
                </AuthButton>
              </Space>
              <Col></Col>
            </FooterRow>
          )}
        </>
      )}
    </div>
  );
};

const FooterRow = styled(Row)`
  width: 100%;
  position: absolute;
  bottom: 0;
  right: 0;
  border-top: 1px solid #e3e4e6;
  padding: 16px 10px;
  background: #fff;
`;
