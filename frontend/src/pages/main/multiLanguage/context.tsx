import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const MultiLanguageContext = React.createContext<{
        data: any[];
        setData: (data: any[]) => void;
        currentPage: number;
        setCurrentPage: (v: number) => void;
        pageSize: number;
        setPageSize: (v: number) => void;
        total: number;
        setTotal: (v: number) => void;
    }
    |
    null>(null);

export const MultiLanguageProvider = ({children}: { children: ReactNode }) => {

    const [data, setData] = useSafeState<any[]>([]);
    const [currentPage, setCurrentPage] = useSafeState(1);
    const [pageSize, setPageSize] = useSafeState(20);
    const [total, setTotal] = useSafeState(0);


    return (
        <MultiLanguageContext.Provider
            value={
                {
                    data,setData,
                    currentPage,setCurrentPage,
                    pageSize,setPageSize,
                    total,setTotal,
                }
            }
        >
            {children}
        </ MultiLanguageContext.Provider>
    )
};

export const useMultiLanguage = () => {
    const context = React.useContext(MultiLanguageContext);
    if (!context) {
        throw new Error("useNotice must be used in MultiLanguageContextProvider");
    }
    return context;
};

