import {Button, message, Modal} from "antd";
import {useIntl} from "react-intl";
import {useAuth} from "../../../context/auth";
import {useHome} from "./context";
import {useNavigate} from "react-router-dom";
import {menuSelect} from "../../../tools/permission";
import styled from "@emotion/styled";


export const RoleModel = () => {
    const intl = useIntl();
    const home = useHome();

    const {formatMessage} = intl;
    const auth = useAuth();
    const ctx = useHome();
    const navigate = useNavigate();
    const handleOk = (value:any) => {
        home.setOpen(true)
        let project = auth.project
        project.permissions = value
        auth.setProject(project)
        sessionStorage.setItem("current_project", JSON.stringify(project))
        ctx.setRolesVisible(false);
        let path = menuSelect(value)
        if (path !== ""){
            navigate(path);
        }else{
            message.success(formatMessage({ id: 'project.noPermission' }));
        }
    }

    const handleCancel = () => {
        home.setOpen(true)
        ctx.setRolesVisible(false);
    };

    return(
        <StyledModal
            title={formatMessage({ id: 'common.select.role' })}
            visible={ctx.rolesVisible}
            onCancel={handleCancel}
            centered
            width={300}
            footer={null}
        >
            {
                ctx.roles.filter(it=>(it.status === 1)).map(
                    it => (
                        <Button key={it.role_id} block className="mar-ver-5" onClick={() => { handleOk(it) }}>
                            {it.role}
                        </Button>
                    )
                )
            }
        </StyledModal>
    )
}

const StyledModal = styled(Modal)`
    & .ant-modal-content {
        width: fit-content;
    }
`