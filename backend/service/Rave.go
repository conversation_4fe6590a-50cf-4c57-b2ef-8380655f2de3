package service

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/base64"
	"fmt"
	"github.com/pkg/errors"
	"github.com/wxnacy/wgo/arrays"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"encoding/json"
	"encoding/xml"
	"github.com/gin-gonic/gin"
)

type RaveService struct{}

// 定义常量
//const myUserId string = "RWSUser2"
//const myPassWord string = "Clinflash123"

const insertRave string = "Insert"
const updateRave string = "Update"

// 设置Rave服务的API URL
// 创建受试者
const raveSubjectUrl string = "/RaveWebServices/webservice.aspx?PostODMClinicalData"

// 更新受试者字段
const raveUpdateSubjectUrl string = "https://tigermed.mdsol.com/RaveWebServices/webservice.aspx?PostODMClinicalData[&RequestIds={True}]"

func (s *RaveService) CreateRaveSubject(ctx *gin.Context, studyOID string, locationOID string, subjectKey string, raveUrl string) error {
	// 创建HTTP请求
	xmlReqBody := models.ODMAdd{
		Xmlns:            "http://www.cdisc.org/ns/odm/v1.3",
		ODMVersion:       "1.3",
		FileType:         "Transactional",
		FileOID:          "Example-1",
		CreationDateTime: time.Now().Format("2006-01-02T15:04:05"),
		ClinicalData: models.ClinicalDataAdd{
			StudyOID:           studyOID,
			MetaDataVersionOID: "1",
			SubjectDataAdd: models.SubjectDataAdd{
				SubjectKey:      subjectKey,
				TransactionType: insertRave,
				SiteRef: models.SiteRefAdd{
					LocationOID: locationOID,
				},
			},
		},
	}

	xmlBytes, err := xml.Marshal(xmlReqBody)
	if err != nil {
		fmt.Println("Failed to marshal XML:", err)
		return err
	}

	reqBody := strings.NewReader(string(xmlBytes))

	req, err := http.NewRequest("POST", raveUrl+raveSubjectUrl, reqBody)
	if err != nil {
		fmt.Println("Error creating request:", err)
		return err
	}

	apiKey := secretKey(raveUrl)
	// 设置请求头
	req.Header.Set("Content-Type", "text/xml")
	req.Header.Set("Authorization", "Basic "+apiKey)

	// 发送HTTP请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return err
	}
	defer resp.Body.Close()

	// 解析响应
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response:", respBody)
		return err
	}

	// 处理响应数据
	var xmlResponse models.XmlResponse
	err = xml.Unmarshal([]byte(respBody), &xmlResponse)
	if err != nil {
		fmt.Println("Error parsing response:", err)
		return err
	}
	// 打印响应数据
	fmt.Println(xmlResponse)

	return nil
}

func (s *RaveService) UpdateRaveSubject(ctx *gin.Context, studyOID string, locationOID string, subjectKey string, raveUrl string, folderOid string, formOid string, fieldData string) error {

	// 创建HTTP请求
	xmlReqBody := models.ODM{
		Xmlns:            "http://www.cdisc.org/ns/odm/v1.3",
		ODMVersion:       "1.3",
		FileType:         "Transactional",
		FileOID:          "Example-1",
		CreationDateTime: time.Now().Format("2006-01-02T15:04:05"),
		ClinicalData: models.ClinicalData{
			StudyOID:           studyOID,
			MetaDataVersionOID: "1",
			SubjectDataUpdate: models.SubjectDataUpdate{
				SubjectKey:      subjectKey,
				TransactionType: updateRave,
				SiteRef: models.SiteRef{
					LocationOID: locationOID,
				},
				StudyEventData: models.StudyEventData{
					StudyEventOID: folderOid,
					//StudyEventRepeatKey: "{folder-repeat-key}",
					TransactionType: updateRave,
					FormData: models.FormData{
						FormOID: formOid,
						//FormRepeatKey:   "{form-repeat-key}",
						TransactionType: updateRave,
						ItemGroupData: models.ItemGroupData{
							ItemGroupOID: formOid + "_LOGLINE",
							//ItemGroupRepeatKey: "{item-group-repeat-key}",
							TransactionType: updateRave,
							//ItemData: ItemData{
							//	ItemOID: fieldOid,
							//	Value:   data,
							//},
							ItemDataList: []models.ItemData{},
						},
					},
				},
			},
		},
	}

	// 定义一个 map 对象
	var fieldMap map[string]string

	// 将 JSON 字符串解析为 map 对象
	err := json.Unmarshal([]byte(fieldData), &fieldMap)
	if err != nil {
		return err
	}
	// 添加ItemData元素
	for fieldOid, data := range fieldMap {
		itemData := models.ItemData{
			ItemOID: fieldOid,
			Value:   data,
		}
		xmlReqBody.ClinicalData.SubjectDataUpdate.StudyEventData.FormData.ItemGroupData.ItemDataList = append(xmlReqBody.ClinicalData.SubjectDataUpdate.StudyEventData.FormData.ItemGroupData.ItemDataList, itemData)
	}

	xmlBytes, err := xml.Marshal(xmlReqBody)
	if err != nil {
		fmt.Println("Failed to marshal XML:", err)
		return err
	}

	reqBody := strings.NewReader(string(xmlBytes))

	req, err := http.NewRequest("POST", raveUrl+raveSubjectUrl, reqBody)
	if err != nil {
		fmt.Println("Error creating request:", err)
		return err
	}

	apiKey := secretKey(raveUrl)
	// 设置请求头
	req.Header.Set("Content-Type", "text/xml")
	req.Header.Set("Authorization", "Basic "+apiKey)

	// 发送HTTP请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return err
	}
	defer resp.Body.Close()

	// 解析响应
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response:", respBody)
		return err
	}

	// 处理响应数据
	var xmlResponse models.XmlResponse
	err = xml.Unmarshal([]byte(respBody), &xmlResponse)
	if err != nil {
		fmt.Println("Error parsing response:", err)
		return err
	}

	// 打印响应数据
	fmt.Println(xmlResponse)
	return nil
}

// querySign 查询加密
func secretKey(raveUrl string) string {

	myUserId := ""
	myPassWord := ""

	var settingConfig map[string]interface{}
	err := tools.Database.Collection("setting_config").FindOne(nil, bson.M{"key": "rave.edc.verify"}).Decode(&settingConfig)
	if err != nil {
		return ""
	}
	data := settingConfig["data"].(primitive.A)
	for _, element := range data {
		if item, ok := element.(map[string]interface{}); ok {
			if item["url"].(string) == raveUrl {
				myUserId = item["user_name"].(string)
				myPassWord = item["password"].(string)
			}
		}
	}

	credentials := myUserId + ":" + myPassWord
	encoded := base64.StdEncoding.EncodeToString([]byte(credentials))
	return encoded
}

func (s *RaveService) GetEnvUrl(ctx *gin.Context, projectId string, customerId string) ([]models.Env, error) {
	var envs []models.Env

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	filter := bson.M{"_id": projectOID}
	if len(customerId) != 0 {
		customerOID, _ := primitive.ObjectIDFromHex(customerId)
		filter = bson.M{"_id": projectOID, "customer_id": customerOID}
	}
	var document models.Project
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project").FindOne(nil, filter, opts).Decode(&document); err != nil {
		return envs, errors.WithStack(err)
	}

	if len(document.Environments) > 0 {
		environments := document.Environments
		for _, environment := range environments {
			var env models.Env
			env.ID = environment.ID
			env.Name = environment.Name
			//TODO: 查询URL
			//env.Url = environment.
			envs = append(envs, env)
		}
	}

	return envs, nil
}

func (s *RaveService) GetCohortVisitRandomization(ctx *gin.Context, projectId string, customerId string, project models.Project) (models.VisitRandomization, error) {

	var visitRandomization models.VisitRandomization

	cohortNames := make([]string, 0)
	var visits []models.Visit
	var randomizations []models.Randomization
	var document models.Project
	if project.ID.IsZero() {
		projectOID, _ := primitive.ObjectIDFromHex(projectId)
		filter := bson.M{"_id": projectOID}
		if len(customerId) != 0 {
			customerOID, _ := primitive.ObjectIDFromHex(customerId)
			filter = bson.M{"_id": projectOID, "customer_id": customerOID}
		}
		opts := &options.FindOneOptions{
			Projection: bson.M{"meta": 0},
		}
		if err := tools.Database.Collection("project").FindOne(nil, filter, opts).Decode(&document); err != nil {
			return visitRandomization, errors.WithStack(err)
		}
	} else {
		document = project
	}
	info := document.ProjectInfo
	vr := document.ProjectInfo.VisitRandomization

	if len(document.Environments) > 0 {
		environments := document.Environments
		for _, environment := range environments {
			cohorts := environment.Cohorts
			if cohorts != nil && len(cohorts) > 0 {
				for _, cohort := range cohorts {
					if info.Type == 3 {
						//访视
						var visit models.Visit
						visit.Name = cohort.Name
						if vr.Visits != nil && len(vr.Visits) > 0 {
							// 遍历对象数组
							for _, v := range vr.Visits {
								if v.Name == cohort.Name {
									visit.VisitConfigs = v.VisitConfigs
								}
							}
						}
						//随机
						var randomization models.Randomization
						randomization.Name = cohort.Name
						if vr.Randomizations != nil && len(vr.Randomizations) > 0 {
							// 遍历对象数组
							for _, r := range vr.Randomizations {
								if r.Name == cohort.Name {
									if r.RandomizationConfigs != nil && len(r.RandomizationConfigs) > 0 {
										randomization.RandomizationConfigs = r.RandomizationConfigs
									} else {
										randomizationConfigs := GetRandomizationConfigs(document.Type)
										randomization.RandomizationConfigs = randomizationConfigs
									}
								}
							}
						}
						index := arrays.ContainsString(cohortNames, cohort.Name)
						if index == -1 {
							cohortNames = append(cohortNames, cohort.Name)
							visits = append(visits, visit)
							randomizations = append(randomizations, randomization)
						}
					} else if info.Type == 2 {
						//访视
						var visit models.Visit
						visit.Name = cohort.Name
						if vr.Visits != nil && len(vr.Visits) > 0 {
							// 遍历对象数组
							for _, v := range vr.Visits {
								if v.Name == cohort.Name {
									visit.VisitConfigs = v.VisitConfigs
								}
							}
						}
						index := arrays.ContainsString(cohortNames, cohort.Name)
						if index == -1 {
							cohortNames = append(cohortNames, cohort.Name)
							visits = append(visits, visit)
						}
					}
				}
			}
		}
	}

	if info.Type == 1 {
		//访视
		var visit models.Visit
		visit.Name = ""
		if vr.Visits != nil && len(vr.Visits) > 0 {
			visit.VisitConfigs = vr.Visits[0].VisitConfigs
		}
		visits = append(visits, visit)
		//随机
		var randomization models.Randomization
		randomization.Name = ""
		if vr.Randomizations != nil && len(vr.Randomizations) > 0 {
			if vr.Randomizations[0].RandomizationConfigs != nil && len(vr.Randomizations[0].RandomizationConfigs) > 0 {
				randomization.RandomizationConfigs = vr.Randomizations[0].RandomizationConfigs
			} else {
				randomizationConfigs := GetRandomizationConfigs(document.Type)
				randomization.RandomizationConfigs = randomizationConfigs
			}
		} else {
			randomizationConfigs := GetRandomizationConfigs(document.Type)
			randomization.RandomizationConfigs = randomizationConfigs
		}
		randomizations = append(randomizations, randomization)
	} else if info.Type == 2 {
		//随机
		var randomization models.Randomization
		randomization.Name = ""
		if vr.Randomizations != nil && len(vr.Randomizations) > 0 {
			if vr.Randomizations[0].RandomizationConfigs != nil && len(vr.Randomizations[0].RandomizationConfigs) > 0 {
				randomization.RandomizationConfigs = vr.Randomizations[0].RandomizationConfigs
			} else {
				randomizationConfigs := GetRandomizationConfigs(document.Type)
				randomization.RandomizationConfigs = randomizationConfigs
			}
		} else {
			randomizationConfigs := GetRandomizationConfigs(document.Type)
			randomization.RandomizationConfigs = randomizationConfigs
		}
		randomizations = append(randomizations, randomization)
	}

	visitRandomization.Visits = visits
	visitRandomization.Randomizations = randomizations

	return visitRandomization, nil
}

func GetRandomizationConfigs(projectType int) []models.RandomizationConfig {
	var randomizationConfigs []models.RandomizationConfig
	for i := 0; i < 5; i++ {
		var randomizationConfig models.RandomizationConfig
		if i == 0 {
			randomizationConfig.RandomizationField = "randomizationCode"
			randomizationConfig.FolderOid = ""
			randomizationConfig.FormOid = ""
			randomizationConfig.FieldOid = ""
			randomizationConfigs = append(randomizationConfigs, randomizationConfig)
		} else if i == 1 {
			randomizationConfig.RandomizationField = "randomizationTime"
			randomizationConfig.FolderOid = ""
			randomizationConfig.FormOid = ""
			randomizationConfig.FieldOid = ""
			randomizationConfigs = append(randomizationConfigs, randomizationConfig)
		} else if i == 2 {
			randomizationConfig.RandomizationField = "group"
			randomizationConfig.FolderOid = ""
			randomizationConfig.FormOid = ""
			randomizationConfig.FieldOid = ""
			randomizationConfigs = append(randomizationConfigs, randomizationConfig)
		} else if i == 3 {
			randomizationConfig.RandomizationField = "factor"
			randomizationConfig.FolderOid = ""
			randomizationConfig.FormOid = ""
			randomizationConfig.FieldOid = ""
			randomizationConfigs = append(randomizationConfigs, randomizationConfig)
		} else if i == 4 {
			if projectType != 1 {
				randomizationConfig.RandomizationField = "cohort"
				randomizationConfig.FolderOid = ""
				randomizationConfig.FormOid = ""
				randomizationConfig.FieldOid = ""
				randomizationConfigs = append(randomizationConfigs, randomizationConfig)
			}
		}
	}
	return randomizationConfigs
}
