import React, { useEffect, useImperativeHandle } from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Modal, Row, Table, Spin } from "antd";
import {useSafeState} from "ahooks";
import { useFetch } from "../../../hooks/request";
import {getHistoryList} from "../../../api/multi_language";
import { usePage } from "../../../context/page";
import { PaginationView } from "../../../components/pagination";

export const HistoryList = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const pageCtx = usePage();

    const [visible, setVisible] = useSafeState<any>(false);
    const [data, setData] = useSafeState<any>([]);

    const [form] = Form.useForm();
    const [projectId, setProjectId] = useSafeState<any>(null);

    const { runAsync, loading: getHistoryListLoading } = useFetch(getHistoryList, { manual: true })

    useImperativeHandle(props.bind, () => ({ show }));

    const show = (customerId: any, projectId: any, permissionList: any) => {
        setVisible(true);
        pageCtx.setCurrentPage(1)
        pageCtx.setPageSize(10)
        pageCtx.setTotal(0)
        setProjectId(projectId);
    };

    useEffect(()=>{
        if (!!projectId) {
            getList(projectId, projectId, pageCtx.currentPage, pageCtx.pageSize);
        }
    },[projectId, pageCtx.currentPage, pageCtx.pageSize]);

    const getList = (id: string, projectId: string, page: number, pageSize: number) => {
        runAsync({
            oid: id,
            projectId: projectId,
            start: (page - 1) * pageSize,
            limit: pageSize,
            module: "operation_log.module.project_multi_language,operation_log.module.project_multi_language_translate,operation_log.module.project_multi_language_batch_upload",
        }).then((result: any) => {
            const data = result.data
            setData(data.items);
            pageCtx.setTotal(data.total)
        });
    }

    const hide = () => {
        setVisible(false);
        setProjectId('')
        setData([]);
        form.resetFields();
    };


    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id={"common.history"} />}
                open={visible}
                onCancel={hide}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-medium-modal-multiLanguage'
                okText={formatMessage({id: 'common.ok'})}
                onOk={hide}
            >
                <Spin spinning={getHistoryListLoading}>
                    <Table
                        loading={getHistoryListLoading}
                        className="mar-top-10"
                        dataSource={data}
                        pagination={false}
                        rowKey={(record: any) => (record.id)}
                    >
                        <Table.Column
                            title={<FormattedMessage id="common.serial" />}
                            dataIndex="#"
                            key="#" width={100} ellipsis
                            render={(text, record, index) => ((pageCtx.currentPage - 1) * pageCtx.pageSize + index + 1)}
                        />
                        <Table.Column 
                            title={<FormattedMessage id="common.operator" />} 
                            key="user" 
                            dataIndex="user"
                            width={300} 
                            align="left" 
                            ellipsis
                            render={(value, record: any) => (record.user_name)}
                        />
                        <Table.Column
                            title={<FormattedMessage id="common.operation.time" />}
                            key="time_str"
                            dataIndex="time_str"
                            align="left"
                            width={300}
                            ellipsis
                        />
                        <Table.Column 
                            title={<FormattedMessage id="common.operation.type" />} 
                            key="operation_type"
                            dataIndex="operation_type" 
                            align="left" 
                            ellipsis 
                            width={150}
                        />
                        <Table.Column 
                            title={<FormattedMessage id="common.operation.content" />} 
                            key="fields"
                            dataIndex="fields" align="left"
                            ellipsis
                            render={(value) => (value && value.map((item: any) =>
                                <Row key={item} style={{ wordBreak: 'break-word' }}>
                                    {item}
                                </Row>
                            ))}
                        />
                    </Table>
                     <PaginationView />
                </Spin>
            </Modal>
        </React.Fragment>
    )
};