package data

import (
	"github.com/duke-git/lancet/v2/slice"
)

type MenuPermission struct {
	Text              string             `json:"text"`
	Children          []MenuPermission   `json:"children"`
	Permissions       []string           `json:"permissions"`
	System            int                `json:"system"` //是否为系统级菜单 0无限制 1是 2否
	ResearchAttribute []int              `json:"researchAttribute"`
	FrozenOperations  []OperationDisable `json:"frozenOperations,omitempty"` //项目冻结时可用的权限
}

type OperationDisable struct {
	Text                 string `json:"text"`
	DisableProjectStatus []int  `json:"disableStatus"`
	DisableCohortStatus  []int  `json:"disableCohortStatus"`
	EnvLockedEdit        bool   `json:"envLockedEdit"`
}

var (
	MenuPermissions = []MenuPermission{
		{Text: "menu.home", System: 1, ResearchAttribute: []int{0, 1}},
		{
			Text: "menu.settings", System: 1, ResearchAttribute: []int{0, 1}, Children: []MenuPermission{
				{
					Text: "menu.settings.roles", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
						"operation.settings.roles.view",
						"operation.settings.roles.add",
						"operation.settings.roles.edit",
						"operation.settings.roles.config",
						"operation.settings.roles.export",
					},
				},
				{
					Text: "menu.settings.users", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
						"operation.settings.users.view",
						"operation.settings.users.add",
						"operation.settings.users.setting",
						"operation.settings.users.edit",
						"operation.settings.users.close",
						"operation.settings.users.close.batch",
						"operation.settings.users.invite-again",
						"operation.settings.users.cancel",
						"operation.settings.users.export",
					},
				},

				{
					Text: "menu.settings.storehouse", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
						"operation.settings.storehouse.view",
						"operation.settings.storehouse.add",
						"operation.settings.storehouse.edit",
						"operation.settings.storehouse.delete",
					},
				},
			},
		},
		{
			Text: "menu.projects", System: 0, ResearchAttribute: []int{0, 1}, Permissions: []string{
				"operation.projects.main.view",
				"operation.projects.main.create",
			}, Children: []MenuPermission{
				{
					Text: "menu.projects.main.setting", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
						"operation.projects.main.setting.view",
					}, Children: []MenuPermission{
						{
							Text: "menu.projects.main.setting.base", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
								"operation.projects.main.setting.base.view",
								"operation.projects.main.setting.base.edit",
								"operation.projects.main.setting.base.modify",
							},
						},
						{
							Text: "menu.projects.main.env", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
								"operation.projects.main.config.view",
								"operation.projects.main.config.create",
								"operation.projects.main.config.copy",
								"operation.projects.main.config.edit_env",
								"operation.projects.main.config.unlock",
								"operation.projects.main.config.lock",
								"operation.projects.main.config.add",
								"operation.projects.main.config.edit",
								"operation.projects.main.config.delete",
								"operation.projects.main.config.copy_cohort",
							},
						},
						{
							Text: "menu.projects.main.setting.function", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
								"operation.projects.main.setting.function.view",
								"operation.projects.main.setting.function.edit",
								"operation.projects.main.setting.function.admin",
							},
						},
						{
							Text: "menu.projects.main.setting.docking", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
								"operation.projects.main.setting.docking.view",
								"operation.projects.main.setting.docking.edit",
							},
						},
						{
							Text: "menu.projects.main.setting.custom", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
								"operation.projects.main.setting.custom.view",
								"operation.projects.main.setting.custom.edit",
							},
						},
						{
							Text: "menu.projects.main.setting.permission", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
								"operation.projects.main.setting.permission.view",
								"operation.projects.main.setting.permission.add",
								"operation.projects.main.setting.permission.edit",
								"operation.projects.main.setting.permission.setting",
								//"operation.projects.main.setting.permission.export",
							},
						},
						{
							Text: "menu.projects.main.setting.notice", System: 1, ResearchAttribute: []int{0, 1}, Permissions: []string{
								"operation.projects.main.setting.notice.view",
								"operation.projects.main.setting.notice.add",
								"operation.projects.main.setting.notice.delete",
								"operation.projects.main.setting.notice.edit",
							},
						},
					},
				},
				{
					Text:              "menu.report",
					System:            0,
					ResearchAttribute: []int{0},
					Children: []MenuPermission{
						{
							Text:              "menu.report.randomizeReport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.subject.download-random",
								"operation.subject.download-random.template",
							},
						},
						{
							Text:              "menu.report.dispenseReport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.subject.medicine.export",
								"operation.subject.medicine.export.template",
							},
						},
						{
							Text:              "menu.report.unblindingReport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.subject.download",
								"operation.subject.download.template",
							},
						},
						{
							Text:              "menu.report.shipmentOrdersReport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.supply.shipment.download",
								"operation.supply.shipment.download.template",
							},
						},
						{
							Text:              "menu.report.returnOrdersReport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.supply.recovery.download",
								"operation.supply.recovery.download.template",
							},
						},
						{
							Text:              "menu.report.siteItemReport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.supply.site.medicine.download",
								"operation.supply.site.medicine.download.template",
							},
						},
						{
							Text:              "menu.report.depotItemReport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.supply.storehouse.medicine.download",
								"operation.supply.storehouse.medicine.download.template",
							},
						},
						{
							Text:              "menu.report.sourceRandomizationListExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.build.randomization.list.export",
							},
						},
						{
							Text:              "menu.report.sourceIPExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.build.medicine.upload.downdata",
								"operation.build.medicine.upload.downdata.template",
							},
						},
						{
							Text:              "menu.report.sourceIpUploadHistory",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.source.ip.upload.history.downdata",
							},
						},
						{
							Text:              "menu.report.randomizationSimulationResult",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.build.simulate-random.download",
							},
						},
						{
							Text:              "menu.report.RandomizationSimulationPDFExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.build.simulate-random.pdf.download",
							},
						},
						{
							Text:              "menu.report.configureReport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.build.randomization.info.export",
							},
						},
						{
							Text:              "menu.report.auditTrailExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.auditTrailExport.download",
								"operation.report.auditTrailExport.build",
								"operation.report.auditTrailExport.settings",
								"operation.report.auditTrailExport.release-record",
								"operation.report.auditTrailExport.order",
								"operation.report.auditTrailExport.drug_recovery",
								"operation.report.auditTrailExport.subject",
								"operation.report.auditTrailExport.dispensing",
								"operation.report.auditTrailExport.ip",
							},
						},
						{
							Text:              "menu.report.projectPermissionConfigurationExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.projects.main.setting.permission.export",
							},
						},
						{
							Text:              "menu.report.ProjectNotificationsConfigurationReportExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.build.projectNotificationsConfigurationReport.download",
							},
						},
						{
							Text:              "menu.report.userRoleStatus",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								//"operation.report.userRoleStatus.download",
								"operation.build.settings.user.download",
							},
						},
						{
							Text:              "menu.report.userLoginHistory",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.userLoginHistory.download",
							},
						},
						{
							Text:              "menu.report.userRoleAssignHistory",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.userRoleAssignHistory.download",
							},
						},
						{
							Text:              "menu.report.siteIPStatisticsExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.siteIPStatisticsExport.download",
								"operation.report.siteIPStatisticsExport.download.template",
							},
						},
						{
							Text:              "menu.report.depotIPStatisticsExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.depotIPStatisticsExport.download",
								"operation.report.depotIPStatisticsExport.download.template",
							},
						},
						{
							Text:              "menu.report.randomizationStatisticsExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.randomizationStatisticsExport.download",
							},
						},
						{
							Text:              "menu.report.subjectStatisticsExport",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.subjectStatisticsExport.download",
							},
						},
						{
							Text:              "menu.report.forecastingPrediction",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.forecastingPrediction.download",
							},
						},
						{
							Text:              "menu.report.visitForecast",
							System:            2,
							ResearchAttribute: []int{0},
							Permissions: []string{
								"operation.report.visitForecast.download",
								"operation.report.visitForecast.download.template",
							},
						},
					},
				},
				{
					Text: "menu.projects.project", System: 2, ResearchAttribute: []int{0, 1}, Children: []MenuPermission{
						{
							Text: "menu.projects.project.home", System: 2, ResearchAttribute: []int{0, 1}, Permissions: []string{
								"operation.projects.home.view",
							},
						},
						{
							Text: "menu.projects.project.overview", System: 2, ResearchAttribute: []int{0, 1},
							Children: []MenuPermission{
								{
									Text: "menu.projects.project.status", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.project.status.view",
									},
								},
								{
									Text: "menu.projects.project.task", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.project.task.view",
									},
								},
								{
									Text: "menu.projects.project.random.statistics", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.project.random.view",
										"operation.project.random.download",
									},
								},
								{
									Text: "menu.projects.project.subject.statistics", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.project.subject.view",
										"operation.project.subject.download",
									},
								},
								{
									Text: "menu.projects.project.depot.ip.statistics", System: 2,
									ResearchAttribute: []int{0},
									Permissions: []string{
										"operation.project.depot.IPStatistics.view",
										"operation.project.depot.IPStatistics.download",
									},
								},
								{
									Text: "menu.projects.project.site.ip.statistics", System: 2,
									ResearchAttribute: []int{0},
									Permissions: []string{
										"operation.project.site.IPStatistics.view",
										"operation.project.site.IPStatistics.download",
									},
								},
								{
									Text: "menu.projects.project.analysis", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.project.analysis.view",
									},
								},
								{
									Text: "menu.projects.project.dynamics", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.project.dynamics.view",
									},
								},
							},
						},
						{
							Text: "menu.projects.project.sub", System: 2, ResearchAttribute: []int{0, 1},
							Children: []MenuPermission{
								{
									Text: "menu.projects.project.subject", System: 2, ResearchAttribute: []int{0},
									Children: []MenuPermission{
										{
											Text: "menu.projects.project.subject.urgent-unblinding", System: 2,
											ResearchAttribute: []int{0},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.subject.urgent-unblinding.unblinding", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.subject.unblinding",
														"operation.subject.unblinding-application",
														"operation.subject.unblinding-approval",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.subject.unblinding",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.subject.unblinding-application",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.subject.unblinding-approval",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
												{
													Text: "menu.projects.project.subject.urgent-unblinding.approval-log", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.subject.unblinding-log",
														"operation.subject.unblinding-sms",
														"operation.subject.unblinding-print",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.subject.unblinding-sms",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
											},
										},
										{
											Text: "menu.projects.project.subject.urgent-unblinding-pv", System: 2,
											ResearchAttribute: []int{0},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.subject.urgent-unblinding.unblinding-pv", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.subject.unblinding-pv-view",
														"operation.subject.unblinding-pv-application",
														"operation.subject.unblinding-pv-approval",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.subject.unblinding-pv-view",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.subject.unblinding-pv-application",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.subject.unblinding-pv-approval",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
												{
													Text: "menu.projects.project.subject.urgent-unblinding.approval-log-pv", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.subject.unblinding-pv-log",
														"operation.subject.unblinding-pv-sms",
														"operation.subject.unblinding-pv-print",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.subject.unblinding-pv-sms",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
											},
										},
										{
											Text: "menu.projects.project.subject.urgent-unblinding-ip", System: 2,
											ResearchAttribute: []int{0},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.subject.urgent-unblinding.unblinding-ip", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.subject.unblinding-ip-view",
														"operation.subject.unblinding-ip-application",
														"operation.subject.unblinding-ip-approval",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.subject.unblinding-ip-view",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.subject.unblinding-ip-application",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.subject.unblinding-ip-approval",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
												{
													Text: "menu.projects.project.subject.urgent-unblinding.approval-log-ip", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.subject.unblinding-ip-log",
														"operation.subject.unblinding-ip-sms",
														"operation.subject.unblinding-ip-print",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.subject.unblinding-ip-sms",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
											},
										},

										{
											Text: "menu.projects.project.subject.dispensing", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.subject.medicine.view-dispensing",
												"operation.subject.medicine.dispensing",
												"operation.subject.medicine.reissue",
												"operation.subject.medicine.replace",
												"operation.subject.medicine.resume",
												"operation.subject.medicine.retrieval",
												"operation.subject.medicine.out-visit-dispensing",
												"operation.subject.medicine.invalid",
												"operation.subject.medicine.register",
												"operation.subject.medicine.formula.update",
												"operation.subject.medicine.joinTime",
												"operation.subject.medicine.setUp",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.subject.medicine.dispensing",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},

												{
													Text:                 "operation.subject.medicine.reissue",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.replace",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.resume",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.retrieval",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.out-visit-dispensing",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.invalid",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.register",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.formula.update",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.joinTime",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.subject.medicine.setUp",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{1, 3, 4},
													EnvLockedEdit:        true,
												},
											},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.subject.medicine.trail", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.subject.medicine.trail",
														"operation.subject.medicine.trail.print",
													},
												},
											},
										},
										{
											Text: "menu.projects.project.subject.trail", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.subject.trail",
												"operation.subject.print",
											},
										},
									},
									Permissions: []string{
										"operation.subject.view-list",
										"operation.subject.random",
										"operation.subject.replace",
										//"operation.subject.unblinding-pv",
										"operation.subject.registered",
										"operation.subject.switch.cohort",
										"operation.subject.update",
										"operation.subject.delete",
										"operation.subject.secede",
										"operation.subject.secede-registered",
										//"operation.subject.cohort.status",
										"operation.subject.screen",
										"operation.subject.finish",
										"operation.subject.medicine.transport",
										"operation.subject.invalid-list",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.subject.random",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4, 5},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject.replace",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										//{
										//	Text:                 "operation.subject.unblinding-pv",
										//	DisableProjectStatus: []int{2, 3, 4},
										//	DisableCohortStatus:  []int{1, 4},
										//},
										{
											Text:                 "operation.subject.registered",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject.switch.cohort",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject.update",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject.delete",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject.secede",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject.secede-registered",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										//{
										//	Text:                 "operation.subject.cohort.status",
										//	DisableProjectStatus: []int{1, 2, 3, 4},
										//	DisableCohortStatus:  []int{3, 4},
										//},
										{
											Text:                 "operation.subject.finish",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject.medicine.transport",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject.screen",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.subject", System: 2, ResearchAttribute: []int{1},
									Children: []MenuPermission{
										{
											Text: "menu.projects.project.subject.urgent-unblinding", System: 2,
											ResearchAttribute: []int{1},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.subject.urgent-unblinding.unblinding", System: 2,
													ResearchAttribute: []int{1},
													Permissions: []string{
														"operation.subject-dtp.unblinding",
														"operation.subject-dtp.unblinding-application",
														"operation.subject-dtp.unblinding-approval",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.subject-dtp.unblinding-application",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.subject-dtp.unblinding-approval",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
												{
													Text: "menu.projects.project.subject.urgent-unblinding.approval-log", System: 2,
													ResearchAttribute: []int{1},
													Permissions: []string{
														"operation.subject-dtp.unblinding-log",
														"operation.subject-dtp.unblinding-sms",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.subject-dtp.unblinding-sms",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
											},
										},
									},
									Permissions: []string{
										"operation.subject-dtp.view-list",
										"operation.subject-dtp.random",
										"operation.subject-dtp.replace",
										"operation.subject-dtp.trail",
										"operation.subject-dtp.unblinding-pv",
										"operation.subject-dtp.medicine.view-dispensing",
										"operation.subject-dtp.medicine.trail",
										"operation.subject-dtp.medicine.dispensing",
										"operation.subject-dtp.medicine.reissue",
										"operation.subject-dtp.medicine.replace",
										"operation.subject-dtp.download",
										"operation.subject-dtp.medicine.export",
										"operation.subject-dtp.download-random",
										"operation.subject-dtp.registered",
										"operation.subject-dtp.update",
										"operation.subject-dtp.delete",
										"operation.subject-dtp.medicine.out-visit-dispensing",
										"operation.subject-dtp.medicine.invalid",
										"operation.subject-dtp.medicine.register",
										"operation.subject-dtp.secede",
										"operation.subject-dtp.secede-registered",
										"operation.subject-dtp.print",
										"operation.subject-dtp.medicine.print",
										"operation.subject-dtp.screen",
										"operation.subject-dtp.finish",
										"operation.subject-dtp.medicine.transport",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.subject-dtp.random",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.replace",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.unblinding-pv",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{1, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.medicine.dispensing",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.medicine.transport",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.medicine.reissue",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.medicine.replace",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.registered",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.update",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.delete",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.medicine.out-visit-dispensing",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.medicine.invalid",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.medicine.register",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.secede",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.finish",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.secede-registered",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.subject-dtp.screen",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{1, 3, 4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.subject.visit.cycle", System: 2, ResearchAttribute: []int{0, 1}, Permissions: []string{
										"operation.project.subject.visit.cycle.view",
										"operation.project.subject.visit.cycle.notice.view",
										"operation.project.subject.visit.cycle.send.notice",
									},
								},
							},
						},
						{
							Text: "menu.projects.project.supply", System: 2, ResearchAttribute: []int{0, 1}, Children: []MenuPermission{
								{
									Text: "menu.projects.project.supply.storehouse", System: 2, ResearchAttribute: []int{0, 1}, Children: []MenuPermission{
										{
											Text: "menu.projects.project.supply.storehouse.summary", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.supply.storehouse.medicine.summary",
											},
										},
										{
											Text: "menu.projects.project.supply.storehouse.single", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.storehouse.medicine.singe",
												"operation.supply.storehouse.medicine.use",
												"operation.supply.storehouse.medicine.freeze",
												"operation.supply.storehouse.medicine.lost",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.supply.storehouse.medicine.use",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.storehouse.medicine.freeze",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.storehouse.medicine.lost",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
											},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.storehouse.sku.trail", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.supply.storehouse.medicine.history",
														"operation.supply.storehouse.medicine.print",
													},
												},
											},
										},
										{
											Text: "menu.projects.project.supply.storehouse.single", System: 2,
											ResearchAttribute: []int{1},
											Permissions: []string{
												"operation.supply.storehouse.medicine.singe",
												"operation.supply.storehouse.medicine.use",
												"operation.supply.storehouse.medicine.freeze",
												"operation.supply.storehouse.medicine.lost",
												"operation.supply.storehouse.medicine.download",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.supply.storehouse.medicine.use",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.storehouse.medicine.freeze",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.storehouse.medicine.lost",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
											},
										},
										{
											Text: "menu.projects.project.supply.storehouse.no_number", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.supply.storehouse.no_number.view",
												"operation.supply.storehouse.no_number.freeze",
												"operation.supply.storehouse.no_number.lost",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.supply.storehouse.no_number.freeze",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.storehouse.no_number.lost",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
											},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.storehouse.no_number.trail", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.supply.storehouse.no_number.history",
														"operation.supply.storehouse.no_number.print",
													},
												},
											},
										},
									},
								},
								{
									Text: "menu.projects.project.supply.drug", System: 2, ResearchAttribute: []int{1}, Children: []MenuPermission{
										{
											Text: "menu.projects.project.supply.drug.order", System: 2,
											ResearchAttribute: []int{1},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.supply.shipment.logistics", System: 2,
													ResearchAttribute: []int{1},
													Permissions: []string{
														"operation.supply.shipment.logistics.view",
														"operation.supply.shipment.logistics.edit",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.supply.shipment.logistics.edit",
															DisableProjectStatus: []int{1, 2, 3, 4},
															DisableCohortStatus:  []int{3, 4},
															EnvLockedEdit:        true,
														},
													},
												},
												{
													Text: "menu.projects.project.supply.order.trail", System: 2,
													ResearchAttribute: []int{1},
													Permissions: []string{
														"operation.supply.drug.order.history",
														"operation.supply.drug.order.print",
													},
												},
											},
											Permissions: []string{
												"operation.supply.drug.order.list",
												"operation.supply.drug.order.send",
												"operation.supply.drug.order.receive",
												"operation.supply.drug.order.cancel",
												"operation.supply.drug.order.end",
												"operation.supply.drug.order.reason",
												"operation.supply.drug.order.download",
												"operation.supply.drug.order.close",
												"operation.supply.drug.order.confirm",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.supply.drug.order.send",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.drug.order.receive",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.drug.order.cancel",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.drug.order.end",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.drug.order.reason",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.drug.order.confirm",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.drug.order.close",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
											},
										},
										{
											Text: "menu.projects.project.supply.drug.single", System: 2,
											ResearchAttribute: []int{1},
											Permissions: []string{
												"operation.supply.drug.single.sku",
												"operation.supply.drug.single.download",
												"operation.supply.drug.single.delete",
												"operation.supply.drug.single.use",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.supply.drug.single.delete",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.drug.single.use",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
											},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.drug.sku.trail", System: 2,
													ResearchAttribute: []int{1},
													Permissions: []string{
														"operation.supply.drug.single.history",
														"operation.supply.drug.single.print",
													},
												},
											},
										},
										{
											Text: "menu.projects.project.supply.drug.no_number", System: 2,
											ResearchAttribute: []int{1},
											Permissions: []string{
												"operation.supply.drug.no_number.view",
											},
										},
									},
								},
								{
									Text: "menu.projects.project.supply.site", System: 2, ResearchAttribute: []int{0}, Children: []MenuPermission{
										{
											Text: "menu.projects.project.supply.site.summary", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.site.medicine.summary",
												"operation.supply.site.medicine.summary.formula",
											},
										},
										{
											Text: "menu.projects.project.supply.site.single", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.site.medicine.singe",
												"operation.supply.site.medicine.use",
												"operation.supply.site.medicine.freeze",
												"operation.supply.site.medicine.lost",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.supply.site.medicine.use",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.site.medicine.freeze",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.site.medicine.lost",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
											},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.site.sku.trail", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.supply.site.medicine.history",
														"operation.supply.site.medicine.print",
													},
												},
											},
										},
										{
											Text: "menu.projects.project.supply.site.no_number", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.site.no_number.view",
												"operation.supply.site.no_number.freeze",
												"operation.supply.site.no_number.lost",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.supply.site.no_number.freeze",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.supply.site.no_number.lost",
													DisableProjectStatus: []int{1, 2, 3, 4},
													DisableCohortStatus:  []int{3, 4},
													EnvLockedEdit:        true,
												},
											},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.site.no_number.trail", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.supply.site.no_number.history",
														"operation.supply.site.no_number.print",
													},
												},
											},
										},
									},
								},
								{
									Text: "menu.projects.project.supply.shipment", System: 2, ResearchAttribute: []int{0},
									Children: []MenuPermission{
										{
											Text: "menu.projects.project.supply.shipment.detail", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.shipment.detail.change",
												"operation.supply.shipment.detail.changeRecord",
												"operation.supply.shipment.detail.edit",
											},
										},
										{
											Text: "menu.projects.project.supply.shipment.approval", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.shipment.approval.view",
												"operation.supply.shipment.approval.print",
											},
										},
										//{
										//	Text: "menu.projects.project.supply.shipment.logistics", System: 2,
										//	ResearchAttribute: []int{0},
										//	Permissions: []string{
										//		"operation.supply.shipment.logistics.view",
										//		"operation.supply.shipment.logistics.edit",
										//	},
										//	FrozenOperations: []OperationDisable{
										//		{
										//			Text:                 "operation.supply.shipment.logistics.edit",
										//			DisableProjectStatus: []int{1, 2, 3, 4},
										//			DisableCohortStatus:  []int{3, 4},
										//		},
										//	},
										//},
										{
											Text: "operation.supply.shipment.detail.view", System: 2,
											ResearchAttribute: []int{0},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.supply.shipment.logistics", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.supply.shipment.logistics.view",
														"operation.supply.shipment.logistics.edit",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.supply.shipment.logistics.edit",
															DisableProjectStatus: []int{1, 2, 3, 4},
															DisableCohortStatus:  []int{3, 4},
															EnvLockedEdit:        true,
														},
													},
												},
											},
											Permissions: []string{
												"operation.supply.shipment.contacts",
												"operation.supply.shipment.reason",
											},
										},
										{
											Text: "menu.projects.project.supply.shipment.trail", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.shipment.history",
												"operation.supply.shipment.print",
											},
										},
									},
									Permissions: []string{
										"operation.supply.shipment.create",
										"operation.supply.shipment.cancel",
										"operation.supply.shipment.cancel-dtp",
										"operation.supply.shipment.send",
										"operation.supply.shipment.lose",
										"operation.supply.shipment.list",
										"operation.supply.shipment.receive",
										"operation.supply.shipment.alarm",
										"operation.supply.shipment.confirm",
										"operation.supply.shipment.confirm-dtp",
										"operation.supply.shipment.approval",
										"operation.supply.shipment.close",
										"operation.supply.shipment.close-dtp",
										"operation.supply.shipment.terminated",
										"operation.supply.shipment.terminated-dtp",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.supply.shipment.create",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.cancel",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.cancel-dtp",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.send",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.lose",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.receive",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.alarm",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.confirm",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.confirm-dtp",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.approval",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.close",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.close-dtp",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.terminated",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.shipment.terminated-dtp",
											DisableProjectStatus: []int{1, 2, 3, 4},
											DisableCohortStatus:  []int{3, 4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.supply.drug_recovery", System: 2,
									ResearchAttribute: []int{0},
									Permissions: []string{
										"operation.supply.recovery.list",
										"operation.supply.recovery.add",
										"operation.supply.recovery.receive",
										"operation.supply.recovery.confirm",
										"operation.supply.recovery.cancel",
										"operation.supply.recovery.lose",
										//"operation.supply.recovery.reason",
										"operation.supply.recovery.determine",
										"operation.supply.recovery.close",
										"operation.supply.recovery.end",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.supply.recovery.add",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.recovery.receive",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.recovery.confirm",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.recovery.cancel",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.recovery.lose",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.recovery.determine",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.recovery.close",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.recovery.end",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
									Children: []MenuPermission{
										{
											Text: "menu.projects.project.supply.recovery.detail", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.recovery.detail.change",
												"operation.supply.recovery.detail.changeRecord",
											},
										},
										{
											Text: "menu.projects.project.recovery.trail", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.recovery.history",
												"operation.supply.recovery.print",
											},
										},
										{
											Text: "operation.supply.drug_recovery.detail.view", System: 2,
											ResearchAttribute: []int{0},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.supply.drug_recovery.logistics", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.supply.drug_recovery.logistics.view",
														"operation.supply.drug_recovery.logistics.edit",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.supply.drug_recovery.logistics.edit",
															DisableProjectStatus: []int{1, 2, 3, 4},
															DisableCohortStatus:  []int{3, 4},
															EnvLockedEdit:        true,
														},
													},
												},
											},
											Permissions: []string{
												"operation.supply.recovery.reason",
											},
										},
									},
								},
								{
									Text: "menu.projects.project.supply.release-record", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.supply.freeze.list",
										"operation.supply.freeze.release",
										"operation.supply.freeze.delete",
										"operation.supply.freeze.approval",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.supply.freeze.release",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.freeze.delete",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.supply.freeze.approval",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
									Children: []MenuPermission{
										{
											Text: "menu.projects.project.freeze.trail", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.supply.freeze.history",
												"operation.supply.freeze.print",
											},
										},
									},
								},
							},
						},
						{
							Text: "menu.projects.project.build", System: 2, ResearchAttribute: []int{0, 1}, Children: []MenuPermission{
								{
									Text: "menu.projects.project.build.storehouse", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.build.storehouse.add",
										"operation.build.storehouse.delete",
										"operation.build.storehouse.edit",
										"operation.build.storehouse.notice",
										"operation.build.storehouse.view",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.storehouse.add",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.storehouse.delete",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.storehouse.edit",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.storehouse.notice",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.build.site", System: 2, ResearchAttribute: []int{0, 1}, Children: []MenuPermission{
										{
											Text: "menu.projects.project.build.site.supply-plan", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.build.site.supply-plan.view",
												"operation.build.site.supply-plan.edit",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.site.supply-plan.edit",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
											},
										},
									}, Permissions: []string{
										"operation.build.site.view",
										"operation.build.site.edit",
										"operation.build.site.add",
										"operation.build.site.dispensing",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.site.edit",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.site.add",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.site.dispensing",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.build.attributes", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.build.attribute.view",
										"operation.build.attribute.edit",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.attribute.edit",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true, // 此处前端也要再判断锁定
										},
									},
								},
								//{
								//	Text: "menu.projects.project.build.code_rule", System: 2, ResearchAttribute: []int{0, 1}, Permissions: []string{
								//		"operation.build.code-rule.view",
								//		"operation.build.code-rule.edit",
								//	},
								//	FrozenOperations: []OperationDisable{
								//		{
								//			Text:                 "operation.build.code-rule.edit",
								//			DisableProjectStatus: []int{2, 3, 4},
								//			DisableCohortStatus:  []int{4},
								//		},
								//	},
								//},
								{
									Text: "menu.projects.project.build.simulate_random", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.build.simulate-random.view",
										"operation.build.simulate-random.edit",
										"operation.build.simulate-random.add",
										"operation.build.simulate-random.run",
										"operation.build.simulate-random.site",
										"operation.build.simulate-random.factor",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.simulate-random.edit",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.simulate-random.add",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.simulate-random.run",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.build.randomization", System: 2,
									ResearchAttribute: []int{0, 1},
									Children: []MenuPermission{
										{
											Text: "menu.projects.project.build.randomization.design", System: 2,
											ResearchAttribute: []int{0, 1},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.build.randomization.design.type", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.build.randomization.type.view",
														"operation.build.randomization.type.edit",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.randomization.type.edit",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
													},
												},
												{
													Text: "menu.projects.project.build.randomization.design.group", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.build.randomization.group.add",
														"operation.build.randomization.group.delete",
														"operation.build.randomization.group.edit",
														"operation.build.randomization.group.view",
														"operation.build.randomization.group.inactivating",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.randomization.group.add",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.randomization.group.delete",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.randomization.group.edit",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.randomization.group.inactivating",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
													},
												},
												{
													Text: "menu.projects.project.build.randomization.design.factor", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.build.randomization.factor.add",
														"operation.build.randomization.factor.view",
														"operation.build.randomization.factor.delete",
														"operation.build.randomization.factor.edit",
														"operation.build.randomization.factor.set-toplimit",
														"operation.build.randomization.list.sync",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.randomization.factor.add",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.randomization.factor.delete",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.randomization.factor.edit",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.randomization.factor.set-toplimit",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.randomization.list.sync",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
													},
												},
												{
													Text: "menu.projects.project.build.randomization.design.list", System: 2,
													ResearchAttribute: []int{0},
													Permissions: []string{
														"operation.build.randomization.list.view-summary",
														"operation.build.randomization.list.upload",
														"operation.build.randomization.list.generate",
														"operation.build.randomization.list.active",
														//"operation.build.randomization.list.export",
														"operation.build.randomization.list.invalid",
														"operation.build.randomization.list.edit",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.randomization.list.upload",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.generate",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.active",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.invalid",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.edit",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
													Children: []MenuPermission{
														{
															Text: "menu.projects.project.randomization.list.trail", System: 2,
															ResearchAttribute: []int{0},
															Permissions: []string{
																"operation.build.randomization.list.history",
																"operation.build.randomization.list.print",
															},
														},
													},
												},
												{
													Text: "menu.projects.project.build.randomization.design.list", System: 2,
													ResearchAttribute: []int{1},
													Permissions: []string{
														"operation.build.randomization.list.view-summary",
														"operation.build.randomization.list.upload",
														"operation.build.randomization.list.generate",
														"operation.build.randomization.list.active",
														"operation.build.randomization.list.export",
														"operation.build.randomization.list.invalid",
														"operation.build.randomization.list.edit",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.randomization.list.upload",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.generate",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.active",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.invalid",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.edit",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
												{
													Text: "menu.projects.project.build.randomization.design.attribute", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.build.randomization.list.attribute",
													},
												},
												{
													Text: "menu.projects.project.build.randomization.design.block", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.build.randomization.list.segmentation.view",
														"operation.build.randomization.list.segmentation.clear",
														"operation.build.randomization.list.segmentation.site",
														"operation.build.randomization.list.segmentation.region",
														"operation.build.randomization.list.segmentation.country",
														"operation.build.randomization.list.segmentation.factor",
														"operation.build.randomization.list.segmentation.activate",
														"operation.build.randomization.list.segmentation.deactivate",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.randomization.list.segmentation.activate",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.segmentation.deactivate",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.segmentation.clear",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.segmentation.site",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.segmentation.region",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.segmentation.country",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.list.segmentation.factor",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
												{
													Text: "menu.projects.project.build.randomization.design.factor-in", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.build.randomization.factor-in.view",
														"operation.build.randomization.factor-in.add",
														"operation.build.randomization.factor-in.set-people",
														"operation.build.randomization.factor-in.delete",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.randomization.factor-in.set-people",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.factor-in.add",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
														{
															Text:                 "operation.build.randomization.factor-in.delete",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
															EnvLockedEdit:        true,
														},
													},
												},
											},
										},
										{
											Text: "menu.projects.project.build.randomization.form", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.build.randomization.form.add",
												"operation.build.randomization.form.delete",
												"operation.build.randomization.form.edit",
												"operation.build.randomization.form.list",
												"operation.build.randomization.form.preview",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.randomization.form.add",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.randomization.form.delete",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.randomization.form.edit",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
											},
										},
									},
								},
								{
									Text: "menu.projects.project.build.drug", System: 2, ResearchAttribute: []int{0, 1}, Children: []MenuPermission{
										{
											Text: "menu.projects.project.build.drug.visit", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.build.medicine.visit.update",
												"operation.build.medicine.visit.drag",
												"operation.build.medicine.visit.copy",
												"operation.build.medicine.visit.push",
												"operation.build.medicine.visit.push.record",
												"operation.build.medicine.visit.add",
												"operation.build.medicine.visit.delete",
												"operation.build.medicine.visit.edit",
												"operation.build.medicine.visit.list",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.medicine.visit.update",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.visit.drag",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.visit.copy",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.visit.push",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.visit.add",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.visit.delete",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.visit.edit",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
											},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.build.drug.visit.setting", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.build.medicine.visit.setting.edit",
														"operation.build.medicine.visit.setting.list",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.medicine.visit.setting.delete",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.medicine.visit.setting.edit",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
													},
												},
											},
										},
										{
											Text: "menu.projects.project.build.drug.config", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.build.medicine.configuration.add",
												"operation.build.medicine.configuration.delete",
												"operation.build.medicine.configuration.edit",
												"operation.build.medicine.configuration.list",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.medicine.configuration.add",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.configuration.delete",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.configuration.edit",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
											},
											Children: []MenuPermission{
												{
													Text: "menu.projects.project.build.drug.config.setting", System: 2,
													ResearchAttribute: []int{0, 1},
													Permissions: []string{
														"operation.build.medicine.configuration.setting.add",
														"operation.build.medicine.configuration.setting.delete",
														"operation.build.medicine.configuration.setting.edit",
														"operation.build.medicine.configuration.setting.list",
													},
													FrozenOperations: []OperationDisable{
														{
															Text:                 "operation.build.medicine.configuration.setting.add",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.medicine.configuration.setting.list",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.medicine.configuration.setting.delete",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
														{
															Text:                 "operation.build.medicine.configuration.setting.edit",
															DisableProjectStatus: []int{2, 3, 4},
															DisableCohortStatus:  []int{4},
														},
													},
												},
											},
										},
										{
											Text: "menu.projects.project.build.drug.list", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.build.medicine.upload.list",
												"operation.build.medicine.upload.upload",
												"operation.build.medicine.package.setting",
												"operation.build.medicine.examine",
												"operation.build.medicine.update",
												"operation.build.medicine.release",
												"operation.build.medicine.upload.delete",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.medicine.package.setting",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.examine",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.update",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.release",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.upload.upload",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.upload.delete",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
											},
										},
										{
											Text: "menu.projects.project.build.drug.list", System: 2,
											ResearchAttribute: []int{1},
											Permissions: []string{
												"operation.build.medicine.upload.list",
												"operation.build.medicine.upload.upload",
												"operation.build.medicine.upload.downdata",
												"operation.build.medicine.package.setting",
												"operation.build.medicine.examine",
												"operation.build.medicine.update",
												"operation.build.medicine.release",
												"operation.build.medicine.upload.delete",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.medicine.upload.upload",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.upload.downdata",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.package.setting",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.examine",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.update",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.release",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.upload.delete",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
											},
										},
										{
											Text: "menu.projects.project.build.drug.no_number", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.build.medicine.otherm.add",
												"operation.build.medicine.otherm.delete",
												"operation.build.medicine.otherm.edit",
												"operation.build.medicine.otherm.list",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.medicine.otherm.add",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.otherm.delete",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.otherm.edit",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
											},
										},
										{
											Text: "menu.projects.project.build.drug.batch", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.build.medicine.batch.update",
												"operation.build.medicine.batch.list",
												"operation.build.medicine.batch.edit",
												"operation.build.medicine.batch.setting",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.medicine.batch.update",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.batch.edit",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.batch.setting",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
											},
										},
										{
											Text: "menu.projects.project.build.drug.barcode", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.build.medicine.barcode.view",
												"operation.build.medicine.barcode.add",
												"operation.build.medicine.barcode.scan",
												"operation.build.medicine.barcode.scanPackage",
												"operation.build.medicine.barcode.export",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.medicine.barcode.add",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.barcode.scan",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.barcode.scanPackage",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
												{
													Text:                 "operation.build.medicine.barcode.export",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
												},
											},
										},
										{
											Text: "menu.projects.project.build.drug.barcode_label", System: 2,
											ResearchAttribute: []int{0, 1},
											Permissions: []string{
												"operation.build.medicine.barcode_label.view",
												"operation.build.medicine.barcode_label.add",
												"operation.build.medicine.barcode_label.preview",
												"operation.build.medicine.barcode_label.send",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.medicine.barcode_label.add",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.medicine.barcode_label.send",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
											},
										},
									},
								},
								{
									Text: "menu.projects.project.build.plan", System: 2, ResearchAttribute: []int{0}, Permissions: []string{
										"operation.build.supply-plan.add",
										"operation.build.supply-plan.delete",
										"operation.build.supply-plan.edit",
										"operation.build.supply-plan.view",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.supply-plan.add",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.supply-plan.delete",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.supply-plan.edit",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
									Children: []MenuPermission{
										{
											Text: "menu.projects.project.build.plan.config", System: 2,
											ResearchAttribute: []int{0},
											Permissions: []string{
												"operation.build.supply-plan.medicine.add",
												"operation.build.supply-plan.medicine.delete",
												"operation.build.supply-plan.medicine.edit",
												"operation.build.supply-plan.medicine.view",
											},
											FrozenOperations: []OperationDisable{
												{
													Text:                 "operation.build.supply-plan.medicine.add",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.supply-plan.medicine.delete",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
												{
													Text:                 "operation.build.supply-plan.medicine.edit",
													DisableProjectStatus: []int{2, 3, 4},
													DisableCohortStatus:  []int{4},
													EnvLockedEdit:        true,
												},
											},
										},
									},
								},
								{
									Text: "menu.projects.project.build.history", System: 2, ResearchAttribute: []int{0, 1}, Permissions: []string{
										"operation.build.history.view",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.push.batch.send",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.push.send",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.build.push", System: 2, ResearchAttribute: []int{0, 1}, Permissions: []string{
										"operation.build.push.view",
										"operation.build.push.batch.send",
										"operation.build.push.send",
										"operation.build.push.details",
										"operation.build.push.history",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.push.batch.send",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.push.send",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.push.history",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
								},
							},
						},
						{
							Text: "menu.projects.project.settings", System: 2, ResearchAttribute: []int{0, 1}, Children: []MenuPermission{
								{
									Text: "menu.projects.project.settings.notice", System: 2, ResearchAttribute: []int{0, 1}, Permissions: []string{
										"operation.build.settings.notice.view",
										"operation.build.settings.notice.edit",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.settings.notice.edit",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.settings.user", System: 2, ResearchAttribute: []int{0}, Permissions: []string{
										"operation.build.settings.user.view",
										"operation.build.settings.user.add",
										"operation.build.settings.user.role",
										"operation.build.settings.user.site",
										"operation.build.settings.user.depot",
										"operation.build.settings.user.app",
										//"operation.build.settings.user.edit",
										"operation.build.settings.user.unbind",
										"operation.build.settings.user.unbind.batch",
										"operation.build.settings.users.invite-again",
										"operation.build.settings.user.reauthorization",
										"operation.build.settings.user.history",
										"operation.build.settings.user.print",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.settings.user.add",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.role",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.users.site",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.depot",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.app",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										// {
										// 	Text:                 "operation.build.settings.user.edit",
										// 	DisableProjectStatus: []int{2, 3, 4},
										// 	DisableCohortStatus:  []int{4},
										// },
										{
											Text:                 "operation.build.settings.user.unbind",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.unbind.batch",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.users.invite-again",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.reauthorization",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
								},
								{
									Text: "menu.projects.project.settings.user", System: 2, ResearchAttribute: []int{1}, Permissions: []string{
										"operation.build.settings.user.view",
										"operation.build.settings.user.add",
										"operation.build.settings.user.role",
										"operation.build.settings.user.site",
										"operation.build.settings.user.depot",
										"operation.build.settings.user.app",
										// "operation.build.settings.user.edit",
										"operation.build.settings.user.unbind",
										"operation.build.settings.user.unbind.batch",
										"operation.build.settings.users.invite-again",
										"operation.build.settings.user.reauthorization",
										"operation.build.settings.user.download",
										"operation.build.settings.user.history",
										"operation.build.settings.user.print",
									},
									FrozenOperations: []OperationDisable{
										{
											Text:                 "operation.build.settings.user.add",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.role",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.users.site",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.depot",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.app",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										// {
										// 	Text:                 "operation.build.settings.user.edit",
										// 	DisableProjectStatus: []int{2, 3, 4},
										// 	DisableCohortStatus:  []int{4},
										// },
										{
											Text:                 "operation.build.settings.user.unbind",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.unbind.batch",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.users.invite-again",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
										{
											Text:                 "operation.build.settings.user.reauthorization",
											DisableProjectStatus: []int{2, 3, 4},
											DisableCohortStatus:  []int{4},
											EnvLockedEdit:        true,
										},
									},
								},
								/*{
									Text: "menu.projects.project.settings.export", System: 2,
									ResearchAttribute: []int{0},
									Permissions: []string{
										"operation.build.randomization.info.view",
									},
								},*/
								/*{
									Text: "menu.projects.project.settings.export", System: 2,
									ResearchAttribute: []int{1},
									Permissions: []string{
										"operation.build.randomization.info.view",
										"operation.build.randomization.info.export",
									},
								},*/
							},
						},
						//{
						//	Text: "menu.projects.project.monitor", System: 2, ResearchAttribute: []int{0, 1}, Permissions: []string{
						//		"operation.monitor.view",
						//		"operation.monitor.edit",
						//	},
						//	FrozenOperations: []OperationDisable{
						//		{
						//			Text:                 "operation.monitor.edit",
						//			DisableProjectStatus: []int{1, 2, 3, 4},
						//			DisableCohortStatus:  []int{4},
						//		},
						//	},
						//},
						{
							Text: "menu.projects.project.info", System: 2, ResearchAttribute: []int{0, 1},
							Children: []MenuPermission{
								{
									Text: "operation.projects.project.info.view", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.projects.project.info.view",
									},
								},
								{
									Text: "menu.projects.project.basic.information", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.projects.project.basic.information.view",
									},
								},
								{
									Text: "menu.projects.project.basic.environment", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.projects.project.basic.environment.view",
									},
								},
								{
									Text: "menu.projects.project.business.functions", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.projects.project.business.functions.view",
									},
								},
								{
									Text: "menu.projects.project.external.docking", System: 2,
									ResearchAttribute: []int{0},
									Permissions: []string{
										"operation.projects.project.external.docking.view",
									},
								},
								{
									Text: "menu.projects.project.custom.process", System: 2,
									ResearchAttribute: []int{0},
									Permissions: []string{
										"operation.projects.project.custom.process.view",
									},
								},
								{
									Text: "menu.projects.project.permissions", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.projects.project.permissions.view",
									},
								},
								{
									Text: "menu.projects.notice.permissions", System: 2,
									ResearchAttribute: []int{0, 1},
									Permissions: []string{
										"operation.projects.notice.permissions.view",
									},
								},
							},
						},
					},
				},
				//{
				//	Text:              "menu.projects.project.multiLanguage",
				//	System:            0,
				//	ResearchAttribute: []int{0},
				//	Permissions: []string{
				//		"operation.projects.project.multiLanguage.view",
				//		"operation.projects.project.multiLanguage.add",
				//		"operation.projects.project.multiLanguage.edit",
				//		"operation.projects.project.multiLanguage.delete",
				//		"operation.projects.project.multiLanguage.trail",
				//	},
				//	Children: []MenuPermission{
				//		{
				//			Text:              "menu.projects.project.multiLanguage.details",
				//			System:            0,
				//			ResearchAttribute: []int{0},
				//			Permissions: []string{
				//				"operation.projects.project.multiLanguage.details.view",
				//				"operation.projects.project.multiLanguage.details.edit",
				//				"operation.projects.project.multiLanguage.details.preview",
				//				"operation.projects.project.multiLanguage.details.downloadTemplate",
				//				"operation.projects.project.multiLanguage.details.batchExport",
				//			},
				//		},
				//	},
				//},
			},
		},
	}
)

func GetFrozenOperationDisable(key string, projectStatus int, envStatus int, envLocked bool) bool {
	find, disable := recur(MenuPermissions, key, projectStatus, envStatus, envLocked)
	if find {
		return disable
	}
	return false
}

func recur(permissions []MenuPermission, key string, projectStatus int, envStatus int, envLocked bool) (bool, bool) {
	for _, permission := range permissions {
		for _, op := range permission.FrozenOperations {
			if op.Text == key && ((envLocked && !op.EnvLockedEdit) || slice.Contain(op.DisableProjectStatus, projectStatus) || slice.Contain(op.DisableCohortStatus, envStatus)) {
				return true, true
			}
		}
		find, disable := recur(permission.Children, key, projectStatus, envStatus, envLocked)
		if find {
			return find, disable
		}
	}
	return false, false
}
