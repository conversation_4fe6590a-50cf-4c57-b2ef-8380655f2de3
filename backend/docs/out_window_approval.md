# 超窗访视审批功能

## 概述

超窗访视审批功能用于判断访视是否超出窗口期，如果超窗且配置了审批流程，则需要走审批申请流程；如果不是超窗访视或者未配置审批流程，则直接允许操作。

## 核心方法

### CheckOutWindowApprovalProcess

判断是否超窗走审批申请流程的核心方法。

```go
func CheckOutWindowApprovalProcess(ctx *gin.Context, subjectID, visitCycleInfoID primitive.ObjectID, 
    dispensingID primitive.ObjectID, requestData interface{}, reason, remark string) (bool, *OutWindowApprovalResponse, error)
```

#### 参数说明

- `ctx`: gin上下文
- `subjectID`: 受试者ID
- `visitCycleInfoID`: 访视周期信息ID
- `dispensingID`: 发药记录ID（可选）
- `requestData`: 请求数据（如果是申请流程则保存）
- `reason`: 申请原因（如果是申请流程）
- `remark`: 备注（如果是申请流程）

#### 返回值

- `bool`: true表示可以继续处理，false表示需要走审批流程
- `*OutWindowApprovalResponse`: 审批响应信息
- `error`: 错误信息

#### 处理逻辑

1. **获取受试者信息**：查询受试者的基本信息和所属环境、群组
2. **获取访视周期信息**：查询对应的访视周期配置
3. **查找访视信息**：在访视周期中找到对应的访视配置
4. **获取属性配置**：查询群组的属性配置信息
5. **计算访视窗口期**：根据受试者的发药历史计算当前访视的窗口期
6. **判断是否超窗**：检查当前时间是否在访视窗口期内
7. **检查现有审批**：查看是否已有待审批的申请
8. **检查审批配置**：确认是否配置了超窗审批流程
9. **创建审批申请**：如果需要审批，创建新的审批申请

## 使用示例

### 基本使用

```go
// 在发药接口中使用
func DispensingHandler(ctx *gin.Context) {
    subjectID, _ := primitive.ObjectIDFromHex("受试者ID")
    visitCycleInfoID, _ := primitive.ObjectIDFromHex("访视周期信息ID")
    
    // 原始请求数据
    requestData := map[string]interface{}{
        "visitLabels":    []string{"label1", "label2"},
        "medicineInfo":   []interface{}{},
        "formulaMedicine": []interface{}{},
        "remark":         "发药备注",
    }
    
    reason := "受试者因特殊情况需要超窗发药"
    remark := "经医生确认，受试者状态良好"
    
    // 检查是否需要审批
    canProceed, response, err := CheckOutWindowApprovalProcess(
        ctx, subjectID, visitCycleInfoID, primitive.NilObjectID, 
        requestData, reason, remark)
    
    if err != nil {
        ctx.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    if !canProceed {
        // 需要审批
        ctx.JSON(202, gin.H{
            "needApproval":   true,
            "approvalNumber": response.ApprovalNumber,
            "message":        response.Message,
        })
        return
    }
    
    // 可以继续正常发药流程
    // ... 执行发药逻辑
    ctx.JSON(200, gin.H{"success": true, "message": "发药成功"})
}
```

### 集成到现有发药流程

```go
func ProcessDispensingWithApproval(ctx *gin.Context, req DispensingWithApprovalRequest) (*DispensingWithApprovalResponse, error) {
    // 1. 检查超窗审批
    canProceed, approvalResponse, err := CheckOutWindowApprovalProcess(
        ctx, req.SubjectID, req.VisitCycleInfoID, req.DispensingID,
        req, req.Reason, req.ApprovalRemark)
    
    if err != nil {
        return nil, err
    }
    
    // 2. 如果需要审批，返回审批信息
    if !canProceed {
        return &DispensingWithApprovalResponse{
            Success:        false,
            NeedApproval:   true,
            ApprovalNumber: approvalResponse.ApprovalNumber,
            Message:        approvalResponse.Message,
        }, nil
    }
    
    // 3. 继续正常发药流程
    dispensingInfo, err := processNormalDispensing(ctx, req)
    if err != nil {
        return nil, err
    }
    
    return &DispensingWithApprovalResponse{
        Success:        true,
        NeedApproval:   false,
        DispensingInfo: dispensingInfo,
    }, nil
}
```

## 数据结构

### OutWindowApprovalRequest

```go
type OutWindowApprovalRequest struct {
    SubjectID       primitive.ObjectID `json:"subjectId"`
    VisitCycleInfoID primitive.ObjectID `json:"visitCycleInfoId"`
    DispensingID    primitive.ObjectID `json:"dispensingId"`
    RequestData     interface{}        `json:"requestData"`
    Reason          string             `json:"reason"`
    Remark          string             `json:"remark"`
    ApprovalType    int                `json:"approvalType"`
}
```

### OutWindowApprovalResponse

```go
type OutWindowApprovalResponse struct {
    NeedApproval    bool   `json:"needApproval"`    // 是否需要审批
    ApprovalNumber  string `json:"approvalNumber"`  // 审批编号
    Message         string `json:"message"`         // 提示信息
}
```

## 配置要求

### 审批配置表

需要在 `approval_config` 集合中配置超窗审批流程：

```json
{
    "project_id": "项目ID",
    "env_id": "环境ID",
    "type": "out_window_visit",
    "enabled": true,
    "config": {
        "approval_users": ["审批人ID1", "审批人ID2"],
        "notification_enabled": true,
        "auto_execute": true
    }
}
```

### 审批流程表

审批申请会保存在 `approval_process` 集合中：

```json
{
    "number": "审批编号",
    "name": "超窗访视发药审批",
    "type": 4,
    "status": 0,
    "application_time": "申请时间",
    "application_by": "申请人ID",
    "out_window_approval_data": {
        "subject_id": "受试者ID",
        "visit_cycle_info_id": "访视周期信息ID",
        "dispensing_id": "发药记录ID",
        "request_data": "原始请求数据JSON",
        "reason": "申请原因",
        "remark": "备注"
    }
}
```

## 测试

运行测试：

```bash
cd backend
go test -v ./test -run TestCheckOutWindowApprovalProcess
```

## 注意事项

1. **权限控制**：确保只有有权限的用户才能创建审批申请
2. **重复申请**：方法会自动检查是否已有待审批的申请，避免重复创建
3. **数据保存**：原始请求数据会被序列化保存，用于审批通过后的自动执行
4. **时区处理**：窗口期计算会考虑项目的时区设置
5. **错误处理**：所有数据库操作都有适当的错误处理
6. **性能考虑**：方法会进行多次数据库查询，建议在高并发场景下进行性能优化

## 扩展功能

1. **通知机制**：可以扩展添加邮件或短信通知功能
2. **审批流程**：可以支持多级审批流程
3. **自动执行**：审批通过后可以自动执行原始操作
4. **审批历史**：可以记录完整的审批历史和操作轨迹
