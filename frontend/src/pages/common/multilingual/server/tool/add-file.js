import fs from "fs";
import path from "path";
import generate from '@babel/generator';
import t from '@babel/types';
import babelParser from '@babel/parser';
import babel from '@babel/traverse';
import _ from 'lodash';

/**
 * 将字段添加到相应的 locales 文件中
 * @param {string} fileName - 文件名（基于路径生成）
 * @param {Array} fields - 字段数组，每个字段包含 key, type, path
 */
export const addFile = (fileName, fields) => {
    if (!fields || !Array.isArray(fields) || fields.length === 0) {
        return Promise.reject(new Error('字段数据不能为空'));
    }

    // 按实际文件路径分组字段（处理多个 children 映射到同一文件的情况）
    const groupedFields = _.groupBy(fields, (field) => {
        const fieldPath = field.path || [];
        const targetKey = fieldPath[fieldPath.length - 1];
        const { filePath } = getFilePathAndVariableName(fieldPath);
        return filePath; // 按实际文件路径分组
    });

    // 为每个文件处理保存
    const savePromises = Object.entries(groupedFields).map(([filePath, fieldsGroup]) => {
        const firstField = fieldsGroup[0];
        const fieldPath = firstField.path || [];

        // 构建变量名
        const { variableName } = getFilePathAndVariableName(fieldPath);

        // 确保目录存在
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 检查文件是否存在并处理
        if (fs.existsSync(filePath)) {
            return appendToExistingFile(filePath, variableName, fieldsGroup);
        } else {
            return createNewFile(filePath, variableName, fieldsGroup);
        }
    });

    return Promise.all(savePromises).then(results => {
        const totalFields = fields.length;
        const fileCount = results.length;
        return `成功添加 ${totalFields} 个字段到 ${fileCount} 个文件中`;
    });
};

/**
 * 根据路径数组确定文件路径和变量名
 * 规则：选择路径的最后一层与 locales 中文件夹内的 key 做对应
 */
const getFilePathAndVariableName = (fieldPath) => {
    // 使用相对路径，从 server/tool 目录到 locales 目录
    const localesDir = '../locales';

    if (!fieldPath || fieldPath.length === 0) {
        return {
            filePath: `${localesDir}/main/multi_language.js`,
            variableName: 'multi_language'
        };
    }

    // 获取路径的最后一层作为目标 key
    const targetKey = fieldPath[fieldPath.length - 1];

    // 动态扫描 locales 目录，查找包含目标 key 的文件
    const result = findFileByKey(localesDir, targetKey);

    if (result) {
        return result;
    }

    // 如果没有找到，使用默认路径
    return {
        filePath: `${localesDir}/main/multi_language.js`,
        variableName: 'multi_language'
    };
};

/**
 * 递归扫描 locales 目录，查找包含指定 key 的文件
 */
const findFileByKey = (localesDir, targetKey) => {
    try {
        // 扫描所有子目录
        const directories = ['main', 'project', 'project_info', 'report'];

        for (const dir of directories) {
            const dirPath = path.resolve(localesDir, dir);
            if (fs.existsSync(dirPath)) {
                const result = scanDirectoryForKey(dirPath, dir, targetKey);
                if (result) {
                    return result;
                }
            }
        }

        return null;
    } catch (error) {
        console.error('扫描 locales 目录时出错:', error);
        return null;
    }
};

/**
 * 扫描指定目录中的所有 JS 文件，查找包含目标 key 的文件
 */
const scanDirectoryForKey = (dirPath, dirName, targetKey) => {
    try {
        const files = fs.readdirSync(dirPath, { withFileTypes: true });

        for (const file of files) {
            const filePath = path.join(dirPath, file.name);

            if (file.isDirectory()) {
                // 递归扫描子目录
                const result = scanDirectoryForKey(filePath, `${dirName}/${file.name}`, targetKey);
                if (result) {
                    return result;
                }
            } else if (file.name.endsWith('.js')) {
                // 检查 JS 文件
                const result = checkFileForKey(filePath, dirName, file.name, targetKey);
                if (result) {
                    return result;
                }
            }
        }

        return null;
    } catch (error) {
        console.error(`扫描目录 ${dirPath} 时出错:`, error);
        return null;
    }
};

/**
 * 检查单个文件是否包含目标 key
 */
const checkFileForKey = (filePath, dirName, fileName, targetKey) => {
    try {
        const content = fs.readFileSync(filePath, 'utf8');

        // 解析文件内容
        const ast = babelParser.parse(content, {
            sourceType: 'module',
            plugins: ['typescript'],
        });

        let foundKey = false;
        let variableName = null;
        let referencedFile = null;

        // 遍历 AST 查找目标 key
        babel.default(ast, {
            enter(path) {
                // 查找导出的变量声明
                if (
                    path.node.type === 'ExportNamedDeclaration' &&
                    path.node.declaration &&
                    path.node.declaration.type === 'VariableDeclaration'
                ) {
                    const declaration = path.node.declaration.declarations[0];
                    variableName = declaration.id.name;

                    // 检查是否包含目标 key
                    const keyResult = containsKey(declaration.init, targetKey, filePath);
                    if (keyResult && keyResult.found) {
                        foundKey = true;
                        referencedFile = keyResult.referencedFile;
                    }
                }
            }
        });

        if (foundKey) {
            // 如果找到了引用的文件，返回引用文件的信息
            if (referencedFile) {
                return referencedFile;
            }

            // 否则返回当前文件的信息
            const relativePath = path.relative(path.resolve('.'), filePath).replace(/\\/g, '/');
            return {
                filePath: relativePath,
                variableName: variableName
            };
        }

        return null;
    } catch (error) {
        console.error(`检查文件 ${filePath} 时出错:`, error);
        return null;
    }
};

/**
 * 检查 AST 节点是否包含目标 key
 */
const containsKey = (node, targetKey, currentFilePath) => {
    if (!node) return false;

    // 如果是数组表达式
    if (node.type === 'ArrayExpression') {
        for (const element of node.elements) {
            const result = containsKey(element, targetKey, currentFilePath);
            if (result) return result;
        }
        return false;
    }

    // 如果是对象表达式
    if (node.type === 'ObjectExpression') {
        // 检查对象的 key 属性
        const keyProperty = node.properties.find(prop =>
            prop.key && prop.key.name === 'key'
        );

        if (keyProperty && keyProperty.value && keyProperty.value.value === targetKey) {
            // 检查 children 属性是否是引用
            const childrenProperty = node.properties.find(prop =>
                prop.key && prop.key.name === 'children'
            );

            if (childrenProperty && childrenProperty.value) {
                // 如果 children 是标识符（引用），查找对应的文件
                if (childrenProperty.value.type === 'Identifier') {
                    const referencedVariableName = childrenProperty.value.name;
                    const referencedFile = findReferencedFile(currentFilePath, referencedVariableName);

                    if (referencedFile) {
                        return {
                            found: true,
                            referencedFile: referencedFile
                        };
                    }
                }
            }

            return { found: true };
        }

        // 检查 children 数组
        const childrenProperty = node.properties.find(prop =>
            prop.key && prop.key.name === 'children'
        );

        if (childrenProperty && childrenProperty.value) {
            const result = containsKey(childrenProperty.value, targetKey, currentFilePath);
            if (result) return result;
        }
    }

    // 如果是标识符（可能是引用）
    if (node.type === 'Identifier') {
        const referencedFile = findReferencedFile(currentFilePath, node.name);
        if (referencedFile) {
            // 检查引用的文件是否包含目标 key
            if (checkReferencedFileForKey(referencedFile.filePath, referencedFile.variableName, targetKey)) {
                return {
                    found: true,
                    referencedFile: referencedFile
                };
            }
        }
    }

    return false;
};

/**
 * 根据当前文件路径和变量名查找引用的文件
 */
const findReferencedFile = (currentFilePath, variableName) => {
    try {
        const content = fs.readFileSync(currentFilePath, 'utf8');

        // 解析文件内容查找 import 语句
        const ast = babelParser.parse(content, {
            sourceType: 'module',
            plugins: ['typescript'],
        });

        let importPath = null;

        // 查找对应的 import 语句
        babel.default(ast, {
            enter(path) {
                if (path.node.type === 'ImportDeclaration') {
                    const specifiers = path.node.specifiers;
                    const hasVariable = specifiers.some(spec =>
                        (spec.type === 'ImportSpecifier' && spec.imported.name === variableName) ||
                        (spec.type === 'ImportDefaultSpecifier' && spec.local.name === variableName)
                    );

                    if (hasVariable) {
                        importPath = path.node.source.value;
                    }
                }
            }
        });

        if (importPath) {
            // 解析相对路径
            const currentDir = path.dirname(currentFilePath);
            let referencedFilePath = path.resolve(currentDir, importPath);

            // 如果没有扩展名，添加 .js
            if (!path.extname(referencedFilePath)) {
                referencedFilePath += '.js';
            }

            // 检查文件是否存在
            if (fs.existsSync(referencedFilePath)) {
                const relativePath = path.relative(path.resolve('.'), referencedFilePath).replace(/\\/g, '/');
                return {
                    filePath: relativePath,
                    variableName: variableName
                };
            }
        }

        return null;
    } catch (error) {
        console.error(`查找引用文件时出错:`, error);
        return null;
    }
};

/**
 * 检查引用的文件是否包含目标 key
 */
const checkReferencedFileForKey = (filePath, variableName, targetKey) => {
    try {
        const content = fs.readFileSync(filePath, 'utf8');

        // 解析文件内容
        const ast = babelParser.parse(content, {
            sourceType: 'module',
            plugins: ['typescript'],
        });

        let found = false;

        // 查找对应的变量声明
        babel.default(ast, {
            enter(path) {
                if (
                    path.node.type === 'ExportNamedDeclaration' &&
                    path.node.declaration &&
                    path.node.declaration.type === 'VariableDeclaration'
                ) {
                    const declaration = path.node.declaration.declarations[0];
                    if (declaration.id.name === variableName) {
                        // 检查数组中是否包含目标 key
                        if (declaration.init && declaration.init.type === 'ArrayExpression') {
                            found = declaration.init.elements.some(element => {
                                if (element.type === 'ObjectExpression') {
                                    const keyProperty = element.properties.find(prop =>
                                        prop.key && prop.key.name === 'key'
                                    );
                                    return keyProperty && keyProperty.value && keyProperty.value.value === targetKey;
                                }
                                return false;
                            });
                        }
                    }
                }
            }
        });

        return found;
    } catch (error) {
        console.error(`检查引用文件时出错:`, error);
        return false;
    }
};

/**
 * 创建新文件
 */
const createNewFile = (filePath, variableName, fields) => {
    return new Promise((resolve, reject) => {
        try {
            // 按字段路径分组，每个路径对应一个子模块
            const fieldsByPath = _.groupBy(fields, (field) => {
                const fieldPath = field.path || [];
                return fieldPath[fieldPath.length - 1]; // 使用路径最后一层作为分组key
            });

            // 创建子模块数组
            const subModules = Object.entries(fieldsByPath).map(([pathKey, pathFields]) => {
                // 为每个子模块创建 children 数组
                const childrenArray = t.arrayExpression(
                    pathFields.map(field => {
                        const properties = [
                            t.objectProperty(t.identifier("key"), t.stringLiteral(field.key))
                        ];

                        if (field.type && field.type !== 'label') {
                            properties.push(
                                t.objectProperty(t.identifier("type"), t.stringLiteral(field.type))
                            );
                        }

                        return t.objectExpression(properties);
                    })
                );

                // 创建子模块对象
                return t.objectExpression([
                    t.objectProperty(t.identifier("key"), t.stringLiteral(pathKey)),
                    t.objectProperty(t.identifier("name"), t.stringLiteral(`menu.${pathKey.replace('_', '.')}`)),
                    t.objectProperty(t.identifier("children"), childrenArray)
                ]);
            });

            // 创建主数组表达式
            const arrayExpression = t.arrayExpression(subModules);

            // 创建变量声明
            const variableDeclarator = t.variableDeclarator(
                t.identifier(variableName),
                arrayExpression
            );

            const variableDeclaration = t.variableDeclaration("const", [variableDeclarator]);

            // 创建导出语句
            const exportNamedDeclaration = t.exportNamedDeclaration(variableDeclaration);

            // 生成 AST 和代码
            const ast = t.program([exportNamedDeclaration]);
            const { code } = generate.default(ast, {
                compact: false,
                retainLines: false,
                concise: false,
                minified: false,
                jsescOption: {
                    quotes: 'double'
                }
            });

            // 格式化代码，确保每个字段占一行
            const formattedCode = formatCode(code);

            // 写入文件
            fs.writeFile(filePath, formattedCode, 'utf8', (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(`成功添加 ${fields.length} 个字段到新文件 ${filePath}`);
                }
            });
        } catch (error) {
            reject(error);
        }
    });
};

/**
 * 向现有文件追加字段
 */
const appendToExistingFile = (filePath, variableName, fields) => {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                reject(err);
                return;
            }

            try {
                // 解析现有文件
                const ast = babelParser.parse(data, {
                    sourceType: 'module',
                    plugins: ['typescript'],
                });

                let foundVariable = false;

                // 按字段路径分组，每个路径对应一个子模块
                const fieldsByPath = _.groupBy(fields, (field) => {
                    const fieldPath = field.path || [];
                    return fieldPath[fieldPath.length - 1]; // 使用路径最后一层作为分组key
                });

                // 遍历 AST，找到对应的变量声明
                babel.default(ast, {
                    enter(path) {
                        if (
                            path.node.type === 'ExportNamedDeclaration' &&
                            path.node.declaration &&
                            path.node.declaration.type === 'VariableDeclaration'
                        ) {
                            const declaration = path.node.declaration.declarations[0];
                            if (declaration.id.name === variableName) {
                                foundVariable = true;

                                // 处理每个路径分组
                                Object.entries(fieldsByPath).forEach(([pathKey, pathFields]) => {
                                    // 查找是否已存在对应的子模块
                                    let targetSubModule = null;

                                    declaration.init.elements.forEach(element => {
                                        if (element.type === 'ObjectExpression') {
                                            const keyProperty = element.properties.find(prop =>
                                                prop.key && prop.key.name === 'key'
                                            );
                                            if (keyProperty && keyProperty.value.value === pathKey) {
                                                targetSubModule = element;
                                            }
                                        }
                                    });

                                    if (targetSubModule) {
                                        // 找到现有子模块，添加到其 children 数组中
                                        const childrenProperty = targetSubModule.properties.find(prop =>
                                            prop.key && prop.key.name === 'children'
                                        );

                                        if (childrenProperty && childrenProperty.value.type === 'ArrayExpression') {
                                            // 为每个新字段创建对象表达式并添加到 children 数组
                                            pathFields.forEach(field => {
                                                const properties = [
                                                    t.objectProperty(t.identifier("key"), t.stringLiteral(field.key))
                                                ];

                                                if (field.type && field.type !== 'label') {
                                                    properties.push(
                                                        t.objectProperty(t.identifier("type"), t.stringLiteral(field.type))
                                                    );
                                                }

                                                const objectExpression = t.objectExpression(properties);
                                                childrenProperty.value.elements.push(objectExpression);
                                            });
                                        }
                                    } else {
                                        // 创建新的子模块
                                        const childrenArray = t.arrayExpression(
                                            pathFields.map(field => {
                                                const properties = [
                                                    t.objectProperty(t.identifier("key"), t.stringLiteral(field.key))
                                                ];

                                                if (field.type && field.type !== 'label') {
                                                    properties.push(
                                                        t.objectProperty(t.identifier("type"), t.stringLiteral(field.type))
                                                    );
                                                }

                                                return t.objectExpression(properties);
                                            })
                                        );

                                        const newSubModule = t.objectExpression([
                                            t.objectProperty(t.identifier("key"), t.stringLiteral(pathKey)),
                                            t.objectProperty(t.identifier("name"), t.stringLiteral(`menu.${pathKey.replace('_', '.')}`)),
                                            t.objectProperty(t.identifier("children"), childrenArray)
                                        ]);

                                        declaration.init.elements.push(newSubModule);
                                    }
                                });
                            }
                        }
                    }
                });

                if (!foundVariable) {
                    reject(new Error(`在文件 ${filePath} 中未找到变量 ${variableName}`));
                    return;
                }

                // 生成新代码
                const { code: newCode } = generate.default(ast, {
                    compact: false,
                    retainLines: false,
                    concise: false,
                    minified: false,
                    jsescOption: {
                        quotes: 'double'
                    }
                });

                // 格式化代码，确保每个字段占一行
                const formattedCode = formatCode(newCode);

                // 写入文件
                fs.writeFile(filePath, formattedCode, 'utf8', (writeErr) => {
                    if (writeErr) {
                        reject(writeErr);
                    } else {
                        resolve(`成功添加 ${fields.length} 个字段到现有文件 ${filePath}`);
                    }
                });
            } catch (parseError) {
                reject(parseError);
            }
        });
    });
};

/**
 * 格式化代码，确保符合原始文件格式
 */
const formatCode = (code) => {
    // 使用正则表达式重新格式化代码
    // 匹配 export const variableName = [...]
    const exportMatch = code.match(/export const (\w+) = \[(.*)\];/s);

    if (!exportMatch) {
        return code; // 如果格式不匹配，返回原代码
    }

    const variableName = exportMatch[1];
    const arrayContent = exportMatch[2];

    // 解析数组中的对象
    const objects = parseObjects(arrayContent);

    // 生成格式化的代码
    let result = `export const ${variableName} = [\n`;

    objects.forEach((obj, index) => {
        result += '    {\n';
        result += `        key: "${obj.key}"`;

        if (obj.type && obj.type !== 'label') {
            result += ',\n';
            result += `        type: "${obj.type}"`;
        }

        result += '\n    }';

        if (index < objects.length - 1) {
            result += ',';
        }

        result += '\n';
    });

    result += '];';

    return result;
};

/**
 * 解析数组中的对象
 */
const parseObjects = (arrayContent) => {
    const objects = [];

    // 使用正则表达式匹配对象
    const objectRegex = /\{\s*key:\s*"([^"]+)"(?:,\s*type:\s*"([^"]+)")?\s*\}/g;
    let match;

    while ((match = objectRegex.exec(arrayContent)) !== null) {
        const obj = {
            key: match[1]
        };

        if (match[2]) {
            obj.type = match[2];
        }

        objects.push(obj);
    }

    return objects;
};